/// <reference types="../vite-env.d.ts" />
import React, { useState, useEffect, useRef } from 'react';
import { Image, Sparkles, RefreshCw, Upload, X, Edit2, Trash2 } from 'lucide-react';

interface SampleImage {
  id: number;
  name: string;
  filename: string;
  url: string;
  category: string;
  description: string;
  error?: string;
}

interface SampleGalleryProps {
  onSampleSelect: (sample: { filename: string; prompt: string; url?: string }) => void;
  isDarkMode: boolean;
}

const CATEGORIES = [
  { id: 'Furniture', keywords: ['chair', 'table', 'lamp', 'furniture'] },
  { id: 'Fashion', keywords: ['shoe', 'watch', 'glasses', 'bag', 'clothing'] },
  { id: 'Electronics', keywords: ['phone', 'camera', 'headphones', 'electronics'] },
  { id: 'Nature', keywords: ['plant', 'flower', 'tree', 'nature', 'organic'] },
  { id: 'People', keywords: ['person', 'people', 'human', 'portrait', 'face', 'head', 'figure'] },
  { id: 'Objects', keywords: [] } // Default category
];

export const SampleGallery: React.FC<SampleGalleryProps> = ({ onSampleSelect, isDarkMode }) => {
  const [sampleImages, setSampleImages] = useState<SampleImage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Upload modal state
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('Objects');
  const [previewUrl, setPreviewUrl] = useState<string>('');

  // Edit modal state
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingImage, setEditingImage] = useState<SampleImage | null>(null);
  const [editName, setEditName] = useState('');
  const [editCategory, setEditCategory] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);

  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [imageToDelete, setImageToDelete] = useState<SampleImage | null>(null);

  const loadSampleImages = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const images = await window.electronAPI.getSampleImages();
      setSampleImages(images || []);
    } catch (err) {
      console.error('Error loading sample images:', err);
      setError('Failed to load sample images.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadSampleImages();
  }, []);

  const handleSampleClick = (image: SampleImage) => {
    onSampleSelect({
      filename: image.filename,
      prompt: image.description || image.name,
      url: image.url
    });
  };

  const handleRefresh = () => {
    loadSampleImages();
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const url = URL.createObjectURL(file);
    setPreviewUrl(url);
    setSelectedFile(file);

    const filename = file.name.toLowerCase();
    const suggestedCategory = CATEGORIES.find(category => 
      category.keywords.some(keyword => filename.includes(keyword))
    )?.id || 'Objects';
    
    setSelectedCategory(suggestedCategory);
    setShowUploadModal(true);
  };

  const handleUploadConfirm = async () => {
    if (!selectedFile) return;

    try {
      setIsUploading(true);
      setError(null);

      const buffer = await selectedFile.arrayBuffer();
      const newImage = await window.electronAPI.uploadSampleImage({
        buffer: new Uint8Array(buffer),
        filename: selectedFile.name,
        category: selectedCategory,
      });

      if (newImage) {
        setSampleImages(prev => [...prev, newImage]);
        setShowUploadModal(false);
        setSelectedFile(null);
        setPreviewUrl('');
        setSelectedCategory('Objects');
      } else {
        setError('Failed to upload image');
      }
    } catch (err) {
      console.error('Error uploading image:', err);
      setError('Failed to upload image');
    } finally {
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleUploadCancel = () => {
    setShowUploadModal(false);
    setSelectedFile(null);
    setPreviewUrl('');
    setSelectedCategory('Objects');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleEditClick = (e: React.MouseEvent, image: SampleImage) => {
    e.stopPropagation();
    setEditingImage(image);
    setEditName(image.name);
    setEditCategory(image.category);
    setShowEditModal(true);
  };

  const handleEditSave = async () => {
    if (!editingImage) return;

    try {
      setIsUpdating(true);
      setError(null);

      const updatedImage = await window.electronAPI.updateSampleImage(editingImage.filename, {
        name: editName,
        category: editCategory,
      });

      if (updatedImage) {
        setSampleImages(prev => prev.map(img => 
          img.id === editingImage.id ? updatedImage : img
        ));
        setShowEditModal(false);
        setEditingImage(null);
        setEditName('');
        setEditCategory('');
      } else {
        setError('Failed to update image');
      }
    } catch (err) {
      console.error('Error updating image:', err);
      setError('Failed to update image');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleEditCancel = () => {
    setShowEditModal(false);
    setEditingImage(null);
    setEditName('');
    setEditCategory('');
  };

  const handleDeleteClick = (e: React.MouseEvent, image: SampleImage) => {
    e.preventDefault();
    e.stopPropagation();
    setImageToDelete(image);
    setShowDeleteConfirm(true);
  };

  const handleDeleteConfirm = async () => {
    if (!imageToDelete) return;

    try {
      setIsDeleting(true);
      setError(null);
      await window.electronAPI.deleteSampleImage(imageToDelete.filename);
      setSampleImages(prev => prev.filter(img => img.id !== imageToDelete.id));
      setShowDeleteConfirm(false);
      setImageToDelete(null);
    } catch (err) {
      console.error('Error deleting image:', err);
      setError('Failed to delete image');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteConfirm(false);
    setImageToDelete(null);
  };

  return (
    <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-4 shadow-lg h-full flex flex-col`}>
      <div className="flex items-center justify-between mb-4 flex-shrink-0">
        <div className="flex items-center gap-2">
          <Sparkles className={`w-5 h-5 ${isDarkMode ? 'text-yellow-400' : 'text-purple-600'}`} />
          <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Sample Images
          </h3>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={handleUploadClick}
            disabled={isUploading}
            className={`p-2 rounded-lg transition-colors ${
              isDarkMode
                ? 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
            } disabled:opacity-50`}
            title="Upload sample image"
          >
            <Upload className={`w-4 h-4 ${isUploading ? 'animate-pulse' : ''}`} />
          </button>
          <button
            onClick={handleRefresh}
            disabled={isLoading}
            className={`p-2 rounded-lg transition-colors ${
              isDarkMode
                ? 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
            } disabled:opacity-50`}
            title="Refresh sample images"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Upload Modal */}
      {showUploadModal && selectedFile && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`p-6 rounded-lg shadow-xl max-w-md w-full mx-4 ${
            isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'
          }`}>
            <div className="flex justify-between items-start mb-4">
              <h3 className="text-lg font-semibold">Upload Sample Image</h3>
              <button
                onClick={handleUploadCancel}
                className={`p-1 rounded-lg ${
                  isDarkMode
                    ? 'hover:bg-gray-700 text-gray-400'
                    : 'hover:bg-gray-100 text-gray-600'
                }`}
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {previewUrl && (
              <div className="mb-4">
                <img
                  src={previewUrl}
                  alt="Preview"
                  className="w-full h-48 object-cover rounded-lg"
                />
              </div>
            )}

            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">Category</label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className={`w-full p-3 rounded-lg border ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                {CATEGORIES.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.id}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex justify-end gap-3">
              <button
                onClick={handleUploadCancel}
                className={`px-4 py-2 rounded-lg ${
                  isDarkMode
                    ? 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                }`}
              >
                Cancel
              </button>
              <button
                onClick={handleUploadConfirm}
                disabled={isUploading}
                className={`px-4 py-2 rounded-lg bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50`}
              >
                {isUploading ? 'Uploading...' : 'Upload'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Modal */}
      {showEditModal && editingImage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`p-6 rounded-lg shadow-xl max-w-md w-full mx-4 ${
            isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'
          }`}>
            <div className="flex justify-between items-start mb-4">
              <h3 className="text-lg font-semibold">Edit Image Details</h3>
              <button
                onClick={handleEditCancel}
                className={`p-1 rounded-lg ${
                  isDarkMode
                    ? 'hover:bg-gray-700 text-gray-400'
                    : 'hover:bg-gray-100 text-gray-600'
                }`}
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Image Preview */}
            <div className="mb-4">
              <div className="aspect-square rounded-lg overflow-hidden border-2 border-dashed border-gray-300">
                {editingImage.url ? (
                  <img
                    src={editingImage.url}
                    alt={editingImage.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className={`w-full h-full flex items-center justify-center ${
                    isDarkMode ? 'bg-gray-700 text-gray-400' : 'bg-gray-200 text-gray-500'
                  }`}>
                    <div className="text-center p-4">
                      <Image className="w-12 h-12 mx-auto mb-2" />
                      <p className="text-sm">Preview not available</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Name</label>
                <input
                  type="text"
                  value={editName}
                  onChange={(e) => setEditName(e.target.value)}
                  className={`w-full p-3 rounded-lg border ${
                    isDarkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                  placeholder="Enter image name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Category</label>
                <select
                  value={editCategory}
                  onChange={(e) => setEditCategory(e.target.value)}
                  className={`w-full p-3 rounded-lg border ${
                    isDarkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                >
                  {CATEGORIES.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.id}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="flex justify-end gap-3 mt-6">
              <button
                onClick={handleEditCancel}
                className={`px-4 py-2 rounded-lg ${
                  isDarkMode
                    ? 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                }`}
              >
                Cancel
              </button>
              <button
                onClick={handleEditSave}
                disabled={isUpdating}
                className={`px-4 py-2 rounded-lg bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50`}
              >
                {isUpdating ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && imageToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`p-6 rounded-lg shadow-xl max-w-md w-full mx-4 ${
            isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'
          }`}>
            <h3 className="text-lg font-semibold mb-4">Delete Image</h3>
            <p className="mb-6">Are you sure you want to delete "{imageToDelete.name}"? This action cannot be undone.</p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={handleDeleteCancel}
                disabled={isDeleting}
                className={`px-4 py-2 rounded-md ${
                  isDarkMode 
                    ? 'bg-gray-700 hover:bg-gray-600 text-white' 
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-800'
                } transition-colors duration-200`}
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteConfirm}
                disabled={isDeleting}
                className="px-4 py-2 rounded-md bg-red-600 hover:bg-red-700 text-white transition-colors duration-200 disabled:opacity-50"
              >
                {isDeleting ? 'Deleting...' : 'Delete'}
              </button>
            </div>
          </div>
        </div>
      )}

      <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'} mb-4 flex-shrink-0`}>
        {sampleImages.length > 0
          ? `Click any image to generate a 3D model (${sampleImages.length} available)`
          : 'Add images to the sample_images folder to see them here'
        }
      </p>

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center flex-1 min-h-32">
          <div className="text-center">
            <RefreshCw className={`w-8 h-8 animate-spin mx-auto mb-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`} />
            <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
              Loading sample images...
            </p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && !isLoading && (
        <div className={`p-4 rounded-lg flex-1 ${isDarkMode ? 'bg-red-900 text-red-200' : 'bg-red-100 text-red-700'}`}>
          <p className="text-sm font-medium">Error loading sample images</p>
          <p className="text-xs mt-1">{error}</p>
          <button
            onClick={handleRefresh}
            className={`mt-2 px-3 py-1 rounded text-xs ${
              isDarkMode
                ? 'bg-red-800 hover:bg-red-700 text-red-200'
                : 'bg-red-200 hover:bg-red-300 text-red-800'
            }`}
          >
            Try Again
          </button>
        </div>
      )}

      {/* Empty State */}
      {!isLoading && !error && sampleImages.length === 0 && (
        <div className="text-center py-8 flex-1 flex flex-col justify-center">
          <Image className={`w-12 h-12 mx-auto mb-3 ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`} />
          <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            No sample images found
          </p>
          <p className={`text-xs mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            Add images to the <code className="bg-gray-200 dark:bg-gray-700 px-1 rounded">sample_images</code> folder
          </p>
        </div>
      )}

      {/* Sample Images Grid */}
      {!isLoading && !error && sampleImages.length > 0 && (
        <div className="grid grid-cols-2 gap-2 flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-200 min-h-0 auto-rows-max content-start">
          {sampleImages.map((sample) => (
            <div
              key={sample.id}
              onClick={() => handleSampleClick(sample)}
              className={`group cursor-pointer rounded-lg overflow-hidden border-2 transition-all duration-200 hover:scale-[1.02] hover:shadow-lg ${
                isDarkMode
                  ? 'border-gray-600 hover:border-purple-400 bg-gray-700'
                  : 'border-gray-200 hover:border-purple-500 bg-gray-50'
              }`}
            >
              <div className="aspect-square relative overflow-hidden">
                {sample.url ? (
                  <img
                    src={sample.url}
                    alt={sample.name}
                    className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
                    loading="lazy"
                    onError={(e) => {
                      console.error(`Failed to load image: ${sample.url}`);
                      e.currentTarget.src = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="%23f3f4f6"/><text x="50%" y="50%" font-family="Arial" font-size="12" fill="%239ca3af" text-anchor="middle" dy=".3em">Image not found</text></svg>';
                    }}
                  />
                ) : (
                  <div className={`w-full h-full flex items-center justify-center ${
                    isDarkMode ? 'bg-gray-700 text-gray-400' : 'bg-gray-200 text-gray-500'
                  }`}>
                    <div className="text-center p-2">
                      <Image className="w-8 h-8 mx-auto mb-1" />
                      <p className="text-xs">
                        {sample.error === 'Image too large' ? 'Too Large' : 
                         sample.error === 'File not found' ? 'Not Found' : 
                         'Unavailable'}
                      </p>
                    </div>
                  </div>
                )}
                {/* Category badge */}
                <div className={`absolute top-2 left-2 px-2 py-1 rounded-full text-xs font-medium z-10 ${
                  sample.category === 'Furniture' ? 'bg-blue-500 text-white' :
                  sample.category === 'Fashion' ? 'bg-purple-500 text-white' :
                  sample.category === 'Electronics' ? 'bg-green-500 text-white' :
                  sample.category === 'Nature' ? 'bg-emerald-500 text-white' :
                  sample.category === 'People' ? 'bg-pink-500 text-white' :
                  'bg-gray-500 text-white'
                }`}>
                  {sample.category}
                </div>
                {/* Edit button */}
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleEditClick(e, sample);
                  }}
                  className="absolute top-2 right-2 p-1.5 rounded-full bg-white bg-opacity-90 opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-opacity-100 z-20"
                  title="Edit image details"
                >
                  <Edit2 className="w-4 h-4 text-gray-600" />
                </button>
                {/* Delete button */}
                <button
                  onClick={(e) => handleDeleteClick(e, sample)}
                  className="absolute top-2 right-12 p-1.5 rounded-full bg-white bg-opacity-90 opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-opacity-100 z-20"
                  title="Delete image"
                >
                  <Trash2 className="w-4 h-4 text-red-600" />
                </button>
                {/* Hover overlay */}
                <div className={`absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center z-0`}>
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white bg-opacity-90 rounded-full p-2">
                    <Image className="w-5 h-5 text-gray-800" />
                  </div>
                </div>
              </div>
              <div className="p-2">
                <h4 className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'} truncate`}>
                  {sample.name}
                </h4>
                <p className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} truncate`}>
                  {sample.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      )}

      <div className={`mt-4 p-3 rounded-lg flex-shrink-0 ${isDarkMode ? 'bg-gray-700' : 'bg-blue-50'}`}>
        <div className="flex items-start gap-2">
          <Sparkles className={`w-4 h-4 mt-0.5 ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`} />
          <div>
            <p className={`text-xs font-medium ${isDarkMode ? 'text-blue-300' : 'text-blue-800'}`}>
              Pro Tips
            </p>
            <p className={`text-xs ${isDarkMode ? 'text-blue-200' : 'text-blue-700'}`}>
              • Objects with clear backgrounds work best<br/>
              • Try different categories for varied results<br/>
              • Single objects generate better 3D models
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};