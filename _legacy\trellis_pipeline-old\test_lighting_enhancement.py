#!/usr/bin/env python3
"""
Test script for lighting enhancement functionality.
This script tests the lighting enhancement modules independently.
"""

import sys
import os
from pathlib import Path
import numpy as np
import trimesh
from PIL import Image

# Add current directory to path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def create_test_mesh():
    """Create a simple test mesh for testing lighting enhancements."""
    print("Creating test mesh...")
    
    # Create a simple cube mesh
    vertices = np.array([
        [-1, -1, -1], [1, -1, -1], [1, 1, -1], [-1, 1, -1],  # Bottom face
        [-1, -1, 1], [1, -1, 1], [1, 1, 1], [-1, 1, 1]       # Top face
    ])
    
    faces = np.array([
        [0, 1, 2], [0, 2, 3],  # Bottom
        [4, 7, 6], [4, 6, 5],  # Top
        [0, 4, 5], [0, 5, 1],  # Front
        [2, 6, 7], [2, 7, 3],  # Back
        [0, 3, 7], [0, 7, 4],  # Left
        [1, 5, 6], [1, 6, 2]   # Right
    ])
    
    # Create mesh
    mesh = trimesh.Trimesh(vertices=vertices, faces=faces)

    # Add some vertex colors (simulate dark areas)
    vertex_colors = np.array([
        [50, 50, 50, 255],    # Dark corners
        [100, 100, 100, 255],
        [150, 150, 150, 255],
        [80, 80, 80, 255],
        [60, 60, 60, 255],    # More dark areas
        [120, 120, 120, 255],
        [200, 200, 200, 255], # Lighter areas
        [90, 90, 90, 255]
    ], dtype=np.uint8)

    # Set vertex colors using ColorVisuals
    mesh.visual = trimesh.visual.ColorVisuals(vertex_colors=vertex_colors)
    
    print(f"✓ Test mesh created with {len(vertices)} vertices and {len(faces)} faces")
    return mesh

def test_lighting_enhancement():
    """Test the lighting enhancement functionality."""
    print("Testing lighting enhancement...")
    
    try:
        from lighting_enhancement import LightingEnhancer
        
        # Create test mesh
        test_mesh = create_test_mesh()
        
        # Create enhancer
        enhancer = LightingEnhancer()
        
        # Test with default settings
        print("Applying lighting enhancements with default settings...")
        enhanced_mesh = enhancer.enhance_model(test_mesh)
        
        print("✓ Default enhancement completed successfully")
        
        # Test with custom settings
        custom_settings = {
            'ambient_boost': 0.4,
            'shadow_softening': 0.5,
            'gamma_correction': 1.3,
            'material_brightness': 0.3,
            'contrast_enhancement': 1.2,
            'saturation_boost': 1.1,
        }
        
        print("Applying lighting enhancements with custom settings...")
        enhanced_mesh_custom = enhancer.enhance_model(test_mesh, custom_settings)
        
        print("✓ Custom enhancement completed successfully")
        
        # Save test results
        output_dir = Path("test_output")
        output_dir.mkdir(exist_ok=True)
        
        # Save original mesh
        test_mesh.export(str(output_dir / "original_mesh.glb"))
        print(f"✓ Original mesh saved to {output_dir / 'original_mesh.glb'}")
        
        # Save enhanced mesh
        enhanced_mesh.export(str(output_dir / "enhanced_mesh_default.glb"))
        print(f"✓ Enhanced mesh (default) saved to {output_dir / 'enhanced_mesh_default.glb'}")
        
        # Save custom enhanced mesh
        enhanced_mesh_custom.export(str(output_dir / "enhanced_mesh_custom.glb"))
        print(f"✓ Enhanced mesh (custom) saved to {output_dir / 'enhanced_mesh_custom.glb'}")
        
        return True
        
    except ImportError as e:
        print(f"✗ Failed to import lighting enhancement: {e}")
        return False
    except Exception as e:
        print(f"✗ Error during lighting enhancement test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_postprocessing():
    """Test the enhanced post-processing functionality."""
    print("Testing enhanced post-processing...")
    
    try:
        from enhanced_postprocessing import EnhancedPostProcessor
        
        # Create test mesh
        test_mesh = create_test_mesh()
        
        # Create processor
        processor = EnhancedPostProcessor()
        
        # Test comprehensive enhancements
        print("Applying comprehensive enhancements...")
        enhanced_mesh = processor._apply_comprehensive_enhancements(test_mesh, processor.default_enhancement_settings)
        
        print("✓ Comprehensive enhancement completed successfully")
        
        # Save result
        output_dir = Path("test_output")
        output_dir.mkdir(exist_ok=True)
        
        enhanced_mesh.export(str(output_dir / "comprehensive_enhanced_mesh.glb"))
        print(f"✓ Comprehensive enhanced mesh saved to {output_dir / 'comprehensive_enhanced_mesh.glb'}")
        
        return True
        
    except ImportError as e:
        print(f"✗ Failed to import enhanced post-processing: {e}")
        return False
    except Exception as e:
        print(f"✗ Error during enhanced post-processing test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("=" * 60)
    print("LIGHTING ENHANCEMENT TEST SUITE")
    print("=" * 60)
    
    # Test basic lighting enhancement
    print("\n1. Testing basic lighting enhancement...")
    test1_success = test_lighting_enhancement()
    
    # Test enhanced post-processing
    print("\n2. Testing enhanced post-processing...")
    test2_success = test_enhanced_postprocessing()
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST RESULTS SUMMARY")
    print("=" * 60)
    print(f"Basic lighting enhancement: {'✓ PASSED' if test1_success else '✗ FAILED'}")
    print(f"Enhanced post-processing: {'✓ PASSED' if test2_success else '✗ FAILED'}")
    
    if test1_success and test2_success:
        print("\n🎉 All tests passed! Lighting enhancement is ready to use.")
        print("\nTest output files have been saved to the 'test_output' directory.")
        print("You can open these GLB files in a 3D viewer to see the lighting improvements.")
    else:
        print("\n⚠️  Some tests failed. Please check the error messages above.")
    
    return test1_success and test2_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
