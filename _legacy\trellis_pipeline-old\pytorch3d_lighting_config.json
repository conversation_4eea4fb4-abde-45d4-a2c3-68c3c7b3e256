{"pytorch3d_optimization_settings": {"description": "Configuration for PyTorch3D differentiable lighting optimization", "presets": {"fast": {"description": "Quick optimization with minimal iterations", "num_lights": 3, "optimization_steps": 50, "learning_rate": 0.02, "ambient_strength": 0.25, "diffuse_strength": 0.7, "specular_strength": 0.15, "light_distance": 2.5, "target_brightness": 0.55, "shadow_softness": 0.05, "color_preservation": 0.9}, "balanced": {"description": "Balanced optimization for most use cases", "num_lights": 4, "optimization_steps": 100, "learning_rate": 0.01, "ambient_strength": 0.3, "diffuse_strength": 0.7, "specular_strength": 0.2, "light_distance": 3.0, "target_brightness": 0.6, "shadow_softness": 0.1, "color_preservation": 0.8}, "high_quality": {"description": "High-quality optimization with more iterations", "num_lights": 6, "optimization_steps": 200, "learning_rate": 0.008, "ambient_strength": 0.35, "diffuse_strength": 0.75, "specular_strength": 0.25, "light_distance": 3.5, "target_brightness": 0.65, "shadow_softness": 0.15, "color_preservation": 0.75}, "color_preserving": {"description": "Optimization focused on preserving original colors", "num_lights": 4, "optimization_steps": 80, "learning_rate": 0.005, "ambient_strength": 0.2, "diffuse_strength": 0.6, "specular_strength": 0.1, "light_distance": 2.8, "target_brightness": 0.55, "shadow_softness": 0.05, "color_preservation": 0.95}, "dramatic": {"description": "Strong lighting optimization for very dark models", "num_lights": 5, "optimization_steps": 150, "learning_rate": 0.015, "ambient_strength": 0.4, "diffuse_strength": 0.8, "specular_strength": 0.3, "light_distance": 3.2, "target_brightness": 0.7, "shadow_softness": 0.2, "color_preservation": 0.7}, "disabled": {"description": "Disable PyTorch3D optimization (fallback to traditional)", "num_lights": 0, "optimization_steps": 0, "learning_rate": 0.0, "ambient_strength": 0.0, "diffuse_strength": 0.0, "specular_strength": 0.0, "light_distance": 0.0, "target_brightness": 0.0, "shadow_softness": 0.0, "color_preservation": 1.0}}, "parameter_descriptions": {"num_lights": "Number of optimizable point lights around the model (2-8)", "optimization_steps": "Number of gradient descent iterations (50-300)", "learning_rate": "Learning rate for optimization (0.001-0.05)", "ambient_strength": "Ambient lighting intensity (0.0-0.5)", "diffuse_strength": "Diffuse lighting intensity (0.3-1.0)", "specular_strength": "Specular highlight intensity (0.0-0.5)", "light_distance": "Distance of lights from model center (1.5-5.0)", "target_brightness": "Target average brightness for optimization (0.3-0.8)", "shadow_softness": "Weight for shadow softening loss (0.0-0.3)", "color_preservation": "How much to preserve original colors (0.5-1.0)"}, "recommended_ranges": {"num_lights": {"min": 2, "max": 8, "default": 4, "step": 1}, "optimization_steps": {"min": 20, "max": 300, "default": 100, "step": 10}, "learning_rate": {"min": 0.001, "max": 0.05, "default": 0.01, "step": 0.001}, "ambient_strength": {"min": 0.0, "max": 0.5, "default": 0.3, "step": 0.05}, "diffuse_strength": {"min": 0.3, "max": 1.0, "default": 0.7, "step": 0.05}, "specular_strength": {"min": 0.0, "max": 0.5, "default": 0.2, "step": 0.05}, "light_distance": {"min": 1.5, "max": 5.0, "default": 3.0, "step": 0.1}, "target_brightness": {"min": 0.3, "max": 0.8, "default": 0.6, "step": 0.05}, "shadow_softness": {"min": 0.0, "max": 0.3, "default": 0.1, "step": 0.05}, "color_preservation": {"min": 0.5, "max": 1.0, "default": 0.8, "step": 0.05}}}, "usage_instructions": {"how_to_use": ["1. Choose a preset from 'fast', 'balanced', 'high_quality', 'color_preserving', 'dramatic', or 'disabled'", "2. Or customize individual parameters using the recommended ranges", "3. PyTorch3D optimization will automatically fall back to traditional methods if PyTorch3D is not available", "4. Higher optimization_steps provide better results but take longer to compute"], "when_to_use_presets": {"fast": "For quick results when processing time is limited", "balanced": "For most models with moderate lighting issues (recommended default)", "high_quality": "For final production models where quality is most important", "color_preserving": "For models where color accuracy is critical", "dramatic": "For very dark models that need significant lighting enhancement", "disabled": "To disable PyTorch3D optimization and use traditional methods"}, "performance_notes": {"gpu_acceleration": "PyTorch3D optimization benefits significantly from GPU acceleration", "memory_usage": "Higher num_lights and optimization_steps increase GPU memory usage", "processing_time": "Typical optimization takes 10-60 seconds depending on settings and hardware"}, "troubleshooting": {"out_of_memory": "Reduce num_lights or optimization_steps, or use 'fast' preset", "too_slow": "Use 'fast' preset or reduce optimization_steps", "colors_washed_out": "Increase color_preservation or use 'color_preserving' preset", "still_too_dark": "Use 'dramatic' preset or increase target_brightness", "unnatural_lighting": "Reduce ambient_strength and specular_strength", "pytorch3d_not_available": "System will automatically fall back to traditional lighting enhancement"}}}