{"lighting_enhancement_settings": {"description": "Configuration for 3D model lighting and shadow reduction", "presets": {"pytorch3d_balanced": {"description": "PyTorch3D differentiable rendering optimization (recommended)", "enable_lighting_enhancement": true, "optimization_method": "pytorch3d", "num_lights": 4, "optimization_steps": 100, "learning_rate": 0.01, "ambient_strength": 0.3, "diffuse_strength": 0.7, "specular_strength": 0.2, "light_distance": 3.0, "target_brightness": 0.6, "shadow_softness": 0.1, "color_preservation": 0.8, "multi_light_setup": true, "ssao_reduction": true}, "pytorch3d_fast": {"description": "Fast PyTorch3D optimization with fewer iterations", "enable_lighting_enhancement": true, "optimization_method": "pytorch3d", "num_lights": 3, "optimization_steps": 50, "learning_rate": 0.02, "ambient_strength": 0.25, "diffuse_strength": 0.7, "specular_strength": 0.15, "light_distance": 2.5, "target_brightness": 0.55, "shadow_softness": 0.05, "color_preservation": 0.9, "multi_light_setup": true, "ssao_reduction": true}, "color_preserving": {"description": "Gentle lighting improvements that preserve original colors (traditional)", "enable_lighting_enhancement": true, "optimization_method": "traditional", "ambient_boost": 0.08, "shadow_softening": 0.15, "gamma_correction": 1.05, "material_brightness": 0.05, "contrast_enhancement": 1.01, "saturation_boost": 1.0, "multi_light_setup": true, "ssao_reduction": true}, "conservative": {"description": "Subtle lighting improvements with minimal changes", "enable_lighting_enhancement": true, "ambient_boost": 0.12, "shadow_softening": 0.18, "gamma_correction": 1.08, "material_brightness": 0.08, "contrast_enhancement": 1.02, "saturation_boost": 1.01, "multi_light_setup": true, "ssao_reduction": true}, "balanced": {"description": "Balanced lighting improvements for most use cases", "enable_lighting_enhancement": true, "ambient_boost": 0.25, "shadow_softening": 0.3, "gamma_correction": 1.15, "material_brightness": 0.15, "contrast_enhancement": 1.05, "saturation_boost": 1.02, "multi_light_setup": true, "ssao_reduction": true}, "aggressive": {"description": "Strong lighting improvements for very dark models", "enable_lighting_enhancement": true, "ambient_boost": 0.4, "shadow_softening": 0.5, "gamma_correction": 1.3, "material_brightness": 0.25, "contrast_enhancement": 1.15, "saturation_boost": 1.05, "multi_light_setup": true, "ssao_reduction": true}, "disabled": {"description": "Disable all lighting enhancements", "enable_lighting_enhancement": false, "ambient_boost": 0.0, "shadow_softening": 0.0, "gamma_correction": 1.0, "material_brightness": 0.0, "contrast_enhancement": 1.0, "saturation_boost": 1.0, "multi_light_setup": false, "ssao_reduction": false}}, "parameter_descriptions": {"enable_lighting_enhancement": "Master switch to enable/disable all lighting enhancements", "ambient_boost": "Increase ambient lighting to brighten dark areas (0.0-1.0)", "shadow_softening": "Reduce shadow intensity and harshness (0.0-1.0)", "gamma_correction": "Gamma correction to brighten overall appearance (0.5-2.0)", "material_brightness": "Boost material base color brightness (0.0-0.5)", "contrast_enhancement": "Enhance contrast while preserving details (0.5-2.0)", "saturation_boost": "Slight saturation increase for better colors (0.5-2.0)", "multi_light_setup": "Setup materials optimized for multi-light environments", "ssao_reduction": "Reduce Screen Space Ambient Occlusion effects"}, "recommended_ranges": {"ambient_boost": {"min": 0.0, "max": 0.5, "default": 0.25, "step": 0.05}, "shadow_softening": {"min": 0.0, "max": 0.8, "default": 0.3, "step": 0.1}, "gamma_correction": {"min": 0.8, "max": 2.0, "default": 1.15, "step": 0.05}, "material_brightness": {"min": 0.0, "max": 0.4, "default": 0.15, "step": 0.05}, "contrast_enhancement": {"min": 0.8, "max": 1.5, "default": 1.05, "step": 0.05}, "saturation_boost": {"min": 0.8, "max": 1.3, "default": 1.02, "step": 0.01}}}, "usage_instructions": {"how_to_use": ["1. Choose a preset from 'color_preserving', 'conservative', 'balanced', 'aggressive', or 'disabled'", "2. Or customize individual parameters using the recommended ranges", "3. The settings will be automatically applied during 3D model generation", "4. Compare results with different presets to find what works best for your models"], "when_to_use_presets": {"color_preserving": "For models where color accuracy is most important (recommended for colorful models)", "conservative": "For models that are already well-lit but need minor improvements", "balanced": "For most models with moderate shadow/darkness issues", "aggressive": "For very dark models or models with heavy shadows", "disabled": "To turn off all enhancements and use original Trellis output"}, "troubleshooting": {"model_too_bright": "Reduce ambient_boost and gamma_correction values", "model_too_dark": "Increase ambient_boost and gamma_correction values", "colors_look_washed_out": "Use 'color_preserving' preset or reduce all enhancement values significantly", "colors_blown_out_white": "Use 'color_preserving' preset and reduce gamma_correction to 1.02 or lower", "details_lost": "Reduce shadow_softening and material_brightness", "unnatural_appearance": "Use 'color_preserving' or 'conservative' preset", "model_looks_overexposed": "Switch to 'color_preserving' preset or disable lighting enhancement"}}}