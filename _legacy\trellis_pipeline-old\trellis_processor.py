#!/usr/bin/env python3
"""
Standalone Trellis processor that runs in the proper Trellis environment.
This script is designed to be called as a subprocess from the main pipeline.
"""

import os
import sys
import json
import argparse
import uuid
import time
from pathlib import Path

# Set environment variables for optimal performance
os.environ['SPCONV_ALGO'] = 'native'

# Set default encoding to UTF-8
if sys.stdout.encoding != 'utf-8':
    sys.stdout.reconfigure(encoding='utf-8')
if sys.stderr.encoding != 'utf-8':
    sys.stderr.reconfigure(encoding='utf-8')

def safe_print(*args, **kwargs):
    """Print function that safely handles Unicode characters"""
    try:
        print(*args, **kwargs)
    except UnicodeEncodeError:
        # Fallback to ASCII-only output
        print(*[str(arg).encode('ascii', 'replace').decode('ascii') for arg in args], **kwargs)

def setup_trellis_environment():
    """Setup the Trellis environment and imports"""
    try:
        # Add the Trellis app directory to Python path
        import sys
        from pathlib import Path

        # Get the directory where this script is located
        script_dir = Path(__file__).parent.resolve()

        # Navigate to the Trellis app directory
        trellis_app_dir = script_dir.parent.parent / "Resources" / "TRELLIS" / "app"

        # Add to Python path
        if str(trellis_app_dir) not in sys.path:
            sys.path.insert(0, str(trellis_app_dir))

        safe_print(f"Added to Python path: {trellis_app_dir}")
        safe_print(f"Current working directory: {os.getcwd()}")
        safe_print(f"Python path: {sys.path[:3]}...")  # Show first 3 entries

        import imageio
        from PIL import Image
        from trellis.pipelines import TrellisImageTo3DPipeline
        from trellis.utils import render_utils, postprocessing_utils

        safe_print("Successfully imported all Trellis modules!")
        return True, imageio, Image, TrellisImageTo3DPipeline, render_utils, postprocessing_utils
    except ImportError as e:
        safe_print(f"Error importing Trellis modules: {e}")
        import traceback
        traceback.print_exc()
        return False, None, None, None, None, None



def process_image_with_trellis(image_path, output_dir, settings):
    """Process an image using the Trellis pipeline"""

    # Setup environment
    success, imageio, Image, TrellisImageTo3DPipeline, render_utils, postprocessing_utils = setup_trellis_environment()

    if not success:
        return {"error": "Failed to setup Trellis environment"}

    try:
        safe_print(f"Loading image: {image_path}")
        image = Image.open(image_path)
        safe_print(f"Image loaded: {image.size}, mode: {image.mode}")

        safe_print(f"Loading Trellis pipeline...")
        pipeline = TrellisImageTo3DPipeline.from_pretrained("JeffreyXiang/TRELLIS-image-large")

        # Check if CUDA is available and move to GPU
        try:
            import torch
            if torch.cuda.is_available():
                pipeline.cuda()
                safe_print(f"Pipeline loaded on GPU: {torch.cuda.get_device_name(0)}")
            else:
                safe_print("CUDA not available, using CPU")
        except ImportError:
            safe_print("PyTorch not available, using CPU")

        # Extract settings
        seed = settings.get('seed', 1)
        ss_steps = settings.get('ss_steps', 12)
        ss_cfg_strength = settings.get('ss_cfg_strength', 7.5)
        slat_steps = settings.get('slat_steps', 12)
        slat_cfg_strength = settings.get('slat_cfg_strength', 3.0)
        simplify = settings.get('simplify', 0.95)
        texture_size = settings.get('texture_size', 1024)

        safe_print(f"Running Trellis pipeline with settings: {settings}")



        # Run the pipeline exactly like the official example
        outputs = pipeline.run(
            image,
            seed=seed,
            formats=["gaussian", "mesh"],
            preprocess_image=True,
            sparse_structure_sampler_params={
                "steps": ss_steps,
                "cfg_strength": ss_cfg_strength,
            },
            slat_sampler_params={
                "steps": slat_steps,
                "cfg_strength": slat_cfg_strength,
            },
        )

        safe_print("Pipeline execution completed successfully")

        # Stages completed as part of pipeline.run
        safe_print("Sparse structure completed")
        safe_print("SLAT generation completed")

        # Generate unique output filenames
        output_id = str(uuid.uuid4())[:8]
        base_name = f"trellis_output_{output_id}"

        # Create output directory
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        # Save Gaussian as PLY
        safe_print("Saving Gaussian PLY...")
        gaussian_ply_path = output_path / f"{base_name}_gaussian.ply"
        outputs['gaussian'][0].save_ply(str(gaussian_ply_path))

        # Generate GLB file with lighting optimizer support
        glb_path = None
        try:
            # Check if lighting optimizer is enabled
            enable_lighting_optimizer = settings.get('enable_lighting_optimizer', True)

            if enable_lighting_optimizer:
                safe_print("Generating GLB file with lighting enhancements...")

                # Try to use enhanced post-processing
                try:
                    # Import enhanced postprocessing from the current directory (Trellis app)
                    import sys
                    current_dir = os.getcwd()  # Should be Resources/TRELLIS/app
                    if current_dir not in sys.path:
                        sys.path.insert(0, current_dir)

                    from enhanced_postprocessing import enhanced_to_glb

                    # Enhanced settings for better lighting
                    enhancement_settings = {
                        'enable_lighting_enhancement': True,
                        'ambient_boost': settings.get('ambient_boost', 0.25),
                        'shadow_softening': settings.get('shadow_softening', 0.3),
                        'gamma_correction': settings.get('gamma_correction', 1.15),
                        'material_brightness': settings.get('material_brightness', 0.15),
                        'contrast_enhancement': settings.get('contrast_enhancement', 1.05),
                        'saturation_boost': settings.get('saturation_boost', 1.02),
                    }

                    glb = enhanced_to_glb(
                        outputs['gaussian'][0],
                        outputs['mesh'][0],
                        simplify=simplify,
                        texture_size=texture_size,
                        enhancement_settings=enhancement_settings,
                        verbose=True
                    )
                    safe_print("✓ Enhanced lighting applied to 3D model")

                except ImportError as e:
                    safe_print(f"Enhanced post-processing not available ({e}), using standard processing...")
                    glb = postprocessing_utils.to_glb(
                        outputs['gaussian'][0],
                        outputs['mesh'][0],
                        simplify=simplify,
                        fill_holes=False,  # Skip problematic hole filling
                        texture_size=texture_size,
                    )
                    safe_print("✓ Standard Trellis GLB generation completed")
            else:
                # Lighting optimizer disabled - use standard processing
                safe_print("Generating GLB file with standard processing (lighting optimizer disabled)...")
                glb = postprocessing_utils.to_glb(
                    outputs['gaussian'][0],
                    outputs['mesh'][0],
                    simplify=simplify,
                    fill_holes=False,  # Skip problematic hole filling
                    texture_size=texture_size,
                )
                safe_print("✓ Standard Trellis GLB generation completed")

            glb_path = output_path / f"{base_name}.glb"
            glb.export(str(glb_path))
            safe_print("GLB file generated successfully")



        except Exception as e:
            error_msg = str(e)
            if any(keyword in error_msg for keyword in ["Ninja", "_get_vc_env", "distutils", "CUDA"]):
                safe_print(f"Warning: Skipping GLB generation due to compilation issue: {error_msg}")
                safe_print("PLY and video files generated successfully")

                glb_path = None
            else:
                safe_print(f"Warning: GLB generation failed with error: {error_msg}")
                safe_print("PLY and video files generated successfully")

                glb_path = None

        # Render videos (we'll skip this since user doesn't need it)
        safe_print("Rendering videos...")
        gaussian_video = render_utils.render_video(outputs['gaussian'][0])['color']
        gaussian_video_path = output_path / f"{base_name}_gaussian.mp4"
        imageio.mimsave(str(gaussian_video_path), gaussian_video, fps=30)

        safe_print(f"Successfully generated output files")

        result = {
            'success': True,
            'gaussian': str(gaussian_ply_path),
            'video': str(gaussian_video_path)
        }

        if glb_path:
            result['glb'] = str(glb_path)

        return result

    except Exception as e:
        safe_print(f"Error processing image: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"error": f"Error processing image: {str(e)}"}

def main():
    """Main function for command line usage"""
    parser = argparse.ArgumentParser(description='Process image with Trellis pipeline')
    parser.add_argument('--image_path', required=True, help='Path to input image')
    parser.add_argument('--output_dir', required=True, help='Output directory')
    parser.add_argument('--settings', required=True, help='JSON string with settings')
    parser.add_argument('--result_file', required=True, help='File to write results to')


    args = parser.parse_args()

    # Add ninja executable to PATH for GLB generation
    ninja_path = os.path.join(os.getcwd(), "Resources", "TRELLIS", "app", "env", "Scripts")
    current_path = os.environ.get('PATH', '')
    if ninja_path not in current_path:
        os.environ['PATH'] = ninja_path + os.pathsep + current_path
        safe_print(f"Added ninja to PATH: {ninja_path}")

    # Parse settings
    try:
        settings = json.loads(args.settings)
    except json.JSONDecodeError as e:
        settings = {}
        safe_print(f"Warning: Could not parse settings JSON: {e}")

    # Process the image
    result = process_image_with_trellis(args.image_path, args.output_dir, settings)

    # Write result to file
    with open(args.result_file, 'w') as f:
        json.dump(result, f, indent=2)

    safe_print(f"Result written to: {args.result_file}")

if __name__ == '__main__':
    main()
