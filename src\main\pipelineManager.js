const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs').promises;
const logger = require('./logger');

class PipelineManager {
    constructor(dependencyManager) {
        this.dependencyManager = dependencyManager;
        this.pipelines = {};
    }

    async initializePipeline(pipelineName) {
        // For pipelines that don't need complex initialization, just mark as initialized
        if (pipelineName === 'ImageGeneration') {
            this.pipelines[pipelineName] = true;
            return true;
        }

        // Get the Python executable from the pipeline's virtual environment
        const pythonExe = this.dependencyManager._getPythonExe(pipelineName);
        const pipelineDir = path.join('pipelines', pipelineName);

        // Import and initialize the pipeline
        const script = `
import sys
import json
from pathlib import Path

try:
    sys.path.append(str(Path("${pipelineDir}").absolute()))
    from trellis_pipeline import TrellisPipeline
    
    # Test pipeline initialization
    pipeline = TrellisPipeline()
    if pipeline.is_available():
        print(json.dumps({"success": True}))
    else:
        print(json.dumps({"success": False, "error": "Pipeline not available"}))
except Exception as e:
    print(json.dumps({"success": False, "error": str(e)}))
`;

        const tempScript = path.join(pipelineDir, 'temp_init.py');
        await fs.writeFile(tempScript, script);

        try {
            const result = await new Promise((resolve, reject) => {
                const process = spawn(pythonExe, [tempScript], {
                    env: {
                        ...process.env,
                        PYTHONPATH: '',
                        PYTHONHOME: ''
                    }
                });

                let stdout = '';
                let stderr = '';

                process.stdout.on('data', (data) => {
                    stdout += data.toString();
                });

                process.stderr.on('data', (data) => {
                    stderr += data.toString();
                });

                process.on('close', (code) => {
                    if (code === 0) {
                        try {
                            const result = JSON.parse(stdout);
                            resolve(result);
                        } catch {
                            reject(new Error(`Failed to parse pipeline output: ${stdout}`));
                        }
                    } else {
                        reject(new Error(`Pipeline initialization failed: ${stderr}`));
                    }
                });
            });

            if (!result.success) {
                throw new Error(result.error || 'Pipeline initialization failed');
            }

            this.pipelines[pipelineName] = true;
            return true;

        } finally {
            // Clean up temp script
            try {
                await fs.unlink(tempScript);
            } catch {}
        }
    }

    async generateTrellis3D(imagePath, outputPath, settings = {}) {
        const PIPELINE_NAME = 'TrellisSource';

        // Ensure pipeline is initialized
        if (!this.pipelines[PIPELINE_NAME]) {
            await this.initializePipeline(PIPELINE_NAME);
        }

        const pythonExe = this.dependencyManager._getPythonExe(PIPELINE_NAME);
        const pipelineDir = path.join('pipelines', PIPELINE_NAME);

        const script = `
import sys
import json
from pathlib import Path

try:
    sys.path.append(str(Path("${pipelineDir}").absolute()))
    from trellis_pipeline import TrellisPipeline
    
    pipeline = TrellisPipeline()
    result = pipeline.generate_3d_from_image(
        "${imagePath.replace(/\\/g, '/')}",
        "${outputPath.replace(/\\/g, '/')}",
        ${JSON.stringify(settings)}
    )
    print(json.dumps(result))
except Exception as e:
    print(json.dumps({"success": False, "error": str(e)}))
`;

        const tempScript = path.join(pipelineDir, 'temp_generate.py');
        await fs.writeFile(tempScript, script);

        try {
            return await new Promise((resolve, reject) => {
                const childProcess = spawn(pythonExe, [tempScript], {
                    env: {
                        ...process.env,
                        PYTHONPATH: '',
                        PYTHONHOME: ''
                    }
                });

                let stdout = '';
                let stderr = '';

                childProcess.stdout.on('data', (data) => {
                    stdout += data.toString();
                });

                childProcess.stderr.on('data', (data) => {
                    stderr += data.toString();
                });

                childProcess.on('close', (code) => {
                    if (code === 0) {
                        try {
                            const result = JSON.parse(stdout);
                            resolve(result);
                        } catch {
                            reject(new Error(`Failed to parse generation output: ${stdout}`));
                        }
                    } else {
                        reject(new Error(`Generation failed: ${stderr}`));
                    }
                });
            });

        } finally {
            // Clean up temp script
            try {
                await fs.unlink(tempScript);
            } catch {}
        }
    }

    async generateImage(prompt, outputPath, settings = {}, progressCallback = null) {
        const PIPELINE_NAME = 'ImageGeneration';

        // Mark pipeline as initialized (dependency manager handles dependency checking)
        this.pipelines[PIPELINE_NAME] = true;

        const pythonExe = this.dependencyManager._getPythonExe(PIPELINE_NAME);
        const pipelineDir = path.join('pipelines', PIPELINE_NAME);
        // Use application-level generation script for portability
        const scriptPath = path.join('utils', 'helpers', 'generate_image.py');

        // Build command line arguments
        const args = [
            scriptPath,
            '--prompt', prompt,
            '--output', outputPath,
            '--model', settings.model || 'sdxl-turbo'
        ];

        // Add optional arguments
        if (settings.width) args.push('--width', settings.width.toString());
        if (settings.height) args.push('--height', settings.height.toString());
        if (settings.steps) args.push('--steps', settings.steps.toString());
        if (settings.guidance_scale !== undefined) args.push('--guidance_scale', settings.guidance_scale.toString());
        if (settings.seed !== undefined) args.push('--seed', settings.seed.toString());
        if (settings.negative_prompt) args.push('--negative_prompt', settings.negative_prompt);
        
        // SDXL Refiner arguments
        if (settings.use_refiner) args.push('--use_refiner');
        if (settings.refiner_steps) args.push('--refiner_steps', settings.refiner_steps.toString());

        // Add result file for JSON output (use absolute path for portability)
        const resultFile = path.join(pipelineDir, 'temp_result.json');
        args.push('--result_file', resultFile);

        logger.info(`Starting image generation with command: ${pythonExe} ${args.join(' ')}`);

        try {
            return await new Promise((resolve, reject) => {
                const childProcess = spawn(pythonExe, args, {
                    cwd: process.cwd(), // Use application root as working directory
                    env: {
                        ...process.env,
                        PYTHONPATH: '',
                        PYTHONHOME: ''
                    },
                    windowsHide: true
                });
                
                // Store progress callback for stdout handler
                this.progressCallback = progressCallback;

                let stdout = '';
                let stderr = '';

                childProcess.stdout.on('data', (data) => {
                    const text = data.toString();
                    stdout += text;
                    
                    // Parse progress updates from the pipeline
                    const lines = text.split('\n').filter(line => line.trim());
                    
                    for (const line of lines) {
                        if (line.startsWith('PROGRESS:')) {
                            try {
                                const progressJson = line.substring(9); // Remove "PROGRESS:" prefix
                                const progressData = JSON.parse(progressJson);
                                
                                // Emit progress event that can be caught by the main process
                                if (this.progressCallback) {
                                    const callbackData = {
                                        type: 'progress',
                                        stage: progressData.stage,
                                        step: progressData.step,
                                        total: progressData.total,
                                        stage_progress: progressData.stage_progress,
                                        overall_progress: progressData.overall_progress,
                                        message: progressData.message,
                                        timestamp: progressData.timestamp
                                    };
                                    
                                    // Handle preview image from base64 data
                                    if (progressData.preview_image) {
                                        // Extract base64 data from data URI
                                        const base64Data = progressData.preview_image.split(',')[1];
                                        callbackData.preview_base64 = base64Data;
                                    }
                                    
                                    this.progressCallback(callbackData);
                                }
                            } catch (e) {
                                logger.warn(`Failed to parse progress data: ${line}`);
                            }
                        } else {
                            logger.info(`ImageGeneration stdout: ${line.trim()}`);
                        }
                    }
                });

                childProcess.stderr.on('data', (data) => {
                    stderr += data.toString();
                    logger.info(`ImageGeneration stderr: ${data.toString().trim()}`);
                });

                childProcess.on('error', (error) => {
                    logger.error(`Image generation process error: ${error.message}`);
                    reject(new Error(`Failed to start image generation process: ${error.message}`));
                });

                childProcess.on('close', async (code) => {
                    try {
                        logger.info(`Image generation process exited with code: ${code}`);
                        
                        if (code === 0) {
                                                    try {
                            // Read result from JSON file (resultFile is already the full path)
                            logger.info(`Reading result file: ${resultFile}`);
                            const resultData = await fs.readFile(resultFile, 'utf8');
                            const result = JSON.parse(resultData);
                            logger.info(`Image generation completed successfully: ${result.output_path}`);
                            logger.info(`Result details: success=${result.success}, model=${result.model}`);
                            resolve(result);
                        } catch (e) {
                            logger.warn(`Failed to read result file, using fallback: ${e.message}`);
                            // Fallback to stdout if JSON file doesn't exist
                            const fallbackResult = {
                                success: true,
                                output_path: outputPath,
                                stdout: stdout,
                                stderr: stderr
                            };
                            logger.info(`Using fallback result: ${JSON.stringify(fallbackResult)}`);
                            resolve(fallbackResult);
                        }
                        } else {
                            const errorMsg = `Image generation failed with code ${code}: ${stderr}`;
                            logger.error(errorMsg);
                            reject(new Error(errorMsg));
                        }
                    } catch (error) {
                        logger.error(`Error in image generation close handler: ${error.message}`);
                        reject(error);
                    }
                });
            });

        } finally {
            // Clean up temp result file
            try {
                await fs.unlink(resultFile);
            } catch {}
        }
    }
}

module.exports = PipelineManager; 