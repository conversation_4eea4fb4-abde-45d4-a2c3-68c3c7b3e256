"""
Text-to-Image Pipeline using FLUX Schnell

This module implements text-to-image generation using FLUX Schnell,
which is faster and higher quality than SDXL. The generated images
are then passed to the existing Trellis image-to-3D pipeline.
"""

import os
import torch
import uuid
from pathlib import Path
from PIL import Image
from typing import Dict, Optional
import tempfile

# Try to import FLUX/diffusers components
try:
    from diffusers import FluxPipeline
    import torch
    FLUX_AVAILABLE = True
    print("FLUX diffusers modules available")
except ImportError as e:
    print(f"Warning: Could not import FLUX modules: {e}")
    FLUX_AVAILABLE = False

# Fallback to Stable Diffusion if FLUX is not available
try:
    from diffusers import StableDiffusionPipeline, StableDiffusionXLPipeline, DPMSolverMultistepScheduler
    SD_AVAILABLE = True
    print("Stable Diffusion modules available")
except ImportError as e:
    print(f"Warning: Could not import Stable Diffusion modules: {e}")
    SD_AVAILABLE = False


class TextToImagePipeline:
    """
    A pipeline for generating images from text prompts using FLUX Schnell or Stable Diffusion.
    """

    def __init__(self, output_dir: str, model_preference: str = "flux"):
        """
        Initialize the text-to-image pipeline.

        Args:
            output_dir: Directory where generated images will be saved
            model_preference: "flux", "sdxl_turbo", "sdxl", or "stable_diffusion"
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        self.pipeline = None
        self.model_type = None
        self.model_preference = model_preference
        self._initialized = False

        # Don't initialize the pipeline immediately to allow faster server startup
        # It will be initialized on first use

    def _get_gpu_memory_info(self):
        """Get GPU memory information for optimization."""
        if not torch.cuda.is_available():
            return None

        try:
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)  # GB
            gpu_name = torch.cuda.get_device_name(0)
            return {
                'total_memory_gb': gpu_memory,
                'name': gpu_name,
                'is_rtx_3060': 'RTX 3060' in gpu_name
            }
        except:
            return None

    def _initialize_pipeline(self):
        """Initialize the text-to-image pipeline with the best available model."""

        # Get GPU info for optimization
        gpu_info = self._get_gpu_memory_info()
        if gpu_info:
            print(f"GPU detected: {gpu_info['name']} ({gpu_info['total_memory_gb']:.1f}GB)")
            if gpu_info['is_rtx_3060']:
                print("RTX 3060 detected - applying specific optimizations")

        # Try direct pipeline approach first (more reliable)
        print(f"Attempting to load text-to-image pipeline with preference: {self.model_preference}")

        # Try the preferred model first
        if self.model_preference == "flux" and FLUX_AVAILABLE:
            if self._load_flux_pipeline():
                return
        elif self.model_preference == "sdxl_turbo" and SD_AVAILABLE:
            if self._load_sdxl_turbo_pipeline():
                return
        elif self.model_preference == "sdxl" and SD_AVAILABLE:
            if self._load_sdxl_pipeline():
                return
        elif self.model_preference == "stable_diffusion" and SD_AVAILABLE:
            if self._load_stable_diffusion_pipeline():
                return

        # Fallback order: FLUX -> SDXL Turbo -> SDXL -> Stable Diffusion
        print(f"Preferred model '{self.model_preference}' not available, trying fallbacks...")

        if FLUX_AVAILABLE and self.model_preference != "flux":
            if self._load_flux_pipeline():
                return

        if SD_AVAILABLE and self.model_preference != "sdxl_turbo":
            if self._load_sdxl_turbo_pipeline():
                return

        if SD_AVAILABLE and self.model_preference != "sdxl":
            if self._load_sdxl_pipeline():
                return

        if SD_AVAILABLE and self.model_preference != "stable_diffusion":
            if self._load_stable_diffusion_pipeline():
                return

        # If direct approach fails, try subprocess approach
        if self._can_use_subprocess():
            print("Direct pipeline failed, using subprocess approach for text-to-image generation")
            self.model_type = "subprocess"
            return

        print("Warning: No text-to-image pipeline could be loaded")

    def _can_use_subprocess(self) -> bool:
        """Check if we can use the subprocess approach."""
        # Check if we have a Python environment with diffusers
        # We'll use the main environment and install diffusers if needed
        processor_script = Path(__file__).parent / "text_to_image_processor.py"
        return processor_script.exists()

    def _load_flux_pipeline(self) -> bool:
        """Load FLUX Schnell pipeline."""
        try:
            print("Loading FLUX Schnell pipeline...")

            # Get Hugging Face token for authentication
            hf_token = None
            try:
                import sys
                import os
                sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
                from config_manager import get_config_manager

                config = get_config_manager()
                hf_token = config.get_huggingface_token()

                if hf_token:
                    print("Using Hugging Face token for FLUX authentication...")
                    # Authenticate with Hugging Face
                    try:
                        from huggingface_hub import login
                        login(token=hf_token)
                        print("Successfully authenticated with Hugging Face")
                    except Exception as e:
                        print(f"Failed to authenticate with Hugging Face: {e}")
                        return False
                else:
                    print("No Hugging Face token found - FLUX requires authentication")
                    return False

            except Exception as e:
                print(f"Error getting Hugging Face token: {e}")
                return False

            # Set environment variable to disable symlinks warning
            os.environ['HF_HUB_DISABLE_SYMLINKS_WARNING'] = '1'

            # FLUX Schnell is the fastest FLUX model
            # Use float16 for RTX 3060 (better compatibility than bfloat16)
            dtype = torch.float16 if torch.cuda.is_available() else torch.float32

            print(f"Loading FLUX with dtype: {dtype}")
            
            # Clear GPU cache before loading
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                print("GPU cache cleared")

            # First load to CPU to avoid memory issues
            try:
                self.pipeline = FluxPipeline.from_pretrained(
                    "black-forest-labs/FLUX.1-schnell",
                    torch_dtype=dtype,
                    token=hf_token,
                    variant="fp16" if torch.cuda.is_available() else None,
                    use_safetensors=True,
                    local_files_only=False,
                    device_map="auto",  # Let the pipeline decide the best device mapping
                    low_cpu_mem_usage=True
                )
            except Exception as e:
                print(f"Error during initial model load: {e}")
                # Try alternative loading method
                print("Attempting alternative loading method...")
                self.pipeline = FluxPipeline.from_pretrained(
                    "black-forest-labs/FLUX.1-schnell",
                    torch_dtype=dtype,
                    token=hf_token,
                    variant="fp16" if torch.cuda.is_available() else None,
                    use_safetensors=True,
                    local_files_only=False,
                    device_map="cpu"  # Force CPU loading first
                )

            # Move to GPU if available
            if torch.cuda.is_available():
                print(f"Moving FLUX pipeline to GPU: {torch.cuda.get_device_name(0)}")
                
                # Clear GPU cache again before moving model
                torch.cuda.empty_cache()
                
                # Move model to GPU with memory optimization
                try:
                    # Enable memory efficient attention first
                    self.pipeline.enable_attention_slicing()
                    print("✓ Attention slicing enabled")
                    
                    # Move to GPU
                    self.pipeline.to("cuda")
                    
                    # Additional optimizations after moving to GPU
                    try:
                        self.pipeline.enable_xformers_memory_efficient_attention()
                        print("✓ XFormers memory efficient attention enabled")
                    except Exception as e:
                        print(f"XFormers not available: {e}")

                    # Enable VAE optimizations
                    try:
                        self.pipeline.enable_vae_slicing()
                        print("✓ VAE slicing enabled")
                    except:
                        pass

                    try:
                        self.pipeline.enable_vae_tiling()
                        print("✓ VAE tiling enabled")
                    except:
                        pass

                    # Compile the UNet for faster inference (PyTorch 2.0+)
                    try:
                        if hasattr(torch, 'compile'):
                            print("Compiling FLUX UNet for faster inference...")
                            self.pipeline.transformer = torch.compile(self.pipeline.transformer, mode="reduce-overhead")
                            print("✓ UNet compiled successfully")
                    except Exception as e:
                        print(f"UNet compilation not available: {e}")

                    print(f"FLUX pipeline optimized for GPU: {torch.cuda.get_device_name(0)}")
                except Exception as e:
                    print(f"Error during GPU optimization: {e}")
                    # Fallback to CPU if GPU optimization fails
                    print("Falling back to CPU mode")
                    self.pipeline.to("cpu")
            else:
                print("CUDA not available, using CPU for FLUX")
            
            self.model_type = "flux"
            print("FLUX Schnell pipeline initialized successfully!")
            return True
            
        except Exception as e:
            print(f"Error loading FLUX pipeline: {e}")
            return False

    def _load_stable_diffusion_pipeline(self) -> bool:
        """Load Stable Diffusion pipeline as fallback."""
        try:
            print("Loading Stable Diffusion pipeline...")

            # Check if CUDA is available
            if not torch.cuda.is_available():
                print("CUDA not available - using fast CPU-optimized approach")
                return self._load_cpu_optimized_pipeline()

            # Use regular SD 1.5 for GPU
            model_id = "runwayml/stable-diffusion-v1-5"

            print(f"Downloading Stable Diffusion model: {model_id}")
            print("This may take a few minutes for first-time download...")

            self.pipeline = StableDiffusionPipeline.from_pretrained(
                model_id,
                torch_dtype=torch.float16,
                safety_checker=None,
                requires_safety_checker=False
            )

            print("Model downloaded, initializing pipeline...")

            # Use DPM solver for better quality
            self.pipeline.scheduler = DPMSolverMultistepScheduler.from_config(
                self.pipeline.scheduler.config
            )

            # Move to GPU
            self.pipeline.to("cuda")
            print(f"Stable Diffusion pipeline loaded on GPU: {torch.cuda.get_device_name(0)}")

            # Enable memory efficient attention if available
            try:
                self.pipeline.enable_xformers_memory_efficient_attention()
            except:
                pass

            self.model_type = "stable_diffusion"
            print("Stable Diffusion pipeline initialized successfully!")
            return True

        except Exception as e:
            print(f"Error loading Stable Diffusion pipeline: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _load_sdxl_turbo_pipeline(self) -> bool:
        """Load SDXL Turbo pipeline for fast generation."""
        try:
            print("Loading SDXL Turbo pipeline...")

            # Use SDXL Turbo which is designed for speed
            model_id = "stabilityai/sdxl-turbo"

            print(f"Downloading SDXL Turbo model: {model_id}")
            print("This may take a few minutes for first-time download...")

            self.pipeline = StableDiffusionXLPipeline.from_pretrained(
                model_id,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                use_safetensors=True,
                variant="fp16" if torch.cuda.is_available() else None
            )

            print("Model downloaded, initializing pipeline...")

            # Move to GPU if available
            if torch.cuda.is_available():
                self.pipeline.to("cuda")
                print(f"SDXL Turbo pipeline loaded on GPU: {torch.cuda.get_device_name(0)}")
            else:
                print("CUDA not available, using CPU for SDXL Turbo")

            # Enable memory efficient attention if available
            try:
                self.pipeline.enable_xformers_memory_efficient_attention()
            except:
                pass

            self.model_type = "sdxl_turbo"
            print("SDXL Turbo pipeline initialized successfully!")
            return True

        except Exception as e:
            print(f"Error loading SDXL Turbo pipeline: {e}")
            return False

    def _load_sdxl_pipeline(self) -> bool:
        """Load full SDXL pipeline for high quality generation."""
        try:
            print("Loading SDXL pipeline...")

            # Use full SDXL for best quality
            model_id = "stabilityai/stable-diffusion-xl-base-1.0"

            print(f"Downloading SDXL model: {model_id}")
            print("This may take several minutes for first-time download...")

            self.pipeline = StableDiffusionXLPipeline.from_pretrained(
                model_id,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                use_safetensors=True,
                variant="fp16" if torch.cuda.is_available() else None
            )

            print("Model downloaded, initializing pipeline...")

            # Move to GPU if available
            if torch.cuda.is_available():
                self.pipeline.to("cuda")
                print(f"SDXL pipeline loaded on GPU: {torch.cuda.get_device_name(0)}")
            else:
                print("CUDA not available, using CPU for SDXL")

            # Enable memory efficient attention if available
            try:
                self.pipeline.enable_xformers_memory_efficient_attention()
            except:
                pass

            self.model_type = "sdxl"
            print("SDXL pipeline initialized successfully!")
            return True

        except Exception as e:
            print(f"Error loading SDXL pipeline: {e}")
            return False

    def _load_cpu_optimized_pipeline(self) -> bool:
        """Load SDXL Turbo for fast CPU generation."""
        try:
            print("CUDA not available - loading SDXL Turbo for fast CPU generation")
            print("SDXL Turbo is optimized for speed and works well on CPU")

            # Use SDXL Turbo which is designed for speed
            model_id = "stabilityai/sdxl-turbo"

            print(f"Downloading SDXL Turbo model: {model_id}")
            print("This is a one-time download, subsequent uses will be much faster...")

            self.pipeline = StableDiffusionXLPipeline.from_pretrained(
                model_id,
                torch_dtype=torch.float32,  # Use float32 for CPU
                use_safetensors=True,
                variant="fp16"
            )

            print("Model downloaded, optimizing for CPU...")

            # SDXL Turbo uses a special scheduler optimized for few steps
            # Keep the default scheduler as it's optimized for turbo

            # Keep on CPU
            print("SDXL Turbo pipeline loaded successfully!")
            print("This model can generate high-quality images in just 1-4 steps!")

            self.model_type = "sdxl_turbo"
            return True

        except Exception as e:
            print(f"Error loading SDXL Turbo pipeline: {e}")
            print("Falling back to placeholder generator...")

            # Fallback to placeholder
            self.pipeline = None
            self.model_type = "placeholder"
            print("Fast placeholder generator ready!")
            return True

    def is_available(self) -> bool:
        """Check if the text-to-image pipeline is available."""
        # Always return True for lazy loading - we'll initialize when needed
        return True

    def generate_image(self, prompt: str, settings: Dict = None, session_id: str = None, enhance_for_3d: bool = False) -> Dict[str, str]:
        """
        Generate an image from a text prompt.

        Args:
            prompt: Text description of the desired image
            settings: Optional settings dictionary with parameters like:
                - seed: Random seed for generation
                - guidance_scale: How closely to follow the prompt
                - num_inference_steps: Number of denoising steps
                - width: Image width
                - height: Image height
            session_id: Optional session ID for progress tracking
            enhance_for_3d: Whether to enhance the prompt for 3D generation

        Returns:
            Dictionary containing:
            {
                'image_path': path_to_generated_image,
                'prompt': enhanced_prompt_used
            }
        """
        # Initialize pipeline on first use (lazy loading)
        if not self._initialized:
            print("Initializing text-to-image pipeline on first use...")
            self._initialize_pipeline()
            self._initialized = True

        # Default settings
        if settings is None:
            settings = {}

        try:
            print(f"Generating image from prompt: {prompt}")
            print(f"Using model: {self.model_type}")
            print(f"Settings: {settings}")

            # Progress tracking helper
            def update_progress_safe(progress, description):
                if session_id:
                    try:
                        import sys
                        import os
                        sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
                        from progress_utils import update_progress
                        update_progress(session_id, 'text_to_image', progress, description)
                    except Exception as e:
                        print(f"Progress update failed: {e}")

            # Use subprocess approach if available
            if self.model_type == "subprocess":
                return self._generate_with_subprocess(prompt, settings)

            update_progress_safe(50, 'Preparing image generation...')

            # Only enhance prompt for 3D if specifically requested
            if enhance_for_3d:
                enhanced_prompt = self._enhance_prompt_for_3d(prompt)
                print(f"Enhanced prompt for 3D: {enhanced_prompt}")
            else:
                enhanced_prompt = prompt
                print(f"Using original prompt: {prompt}")

            update_progress_safe(60, 'Starting generation...')

            # Generate unique filename
            image_id = str(uuid.uuid4())[:8]
            image_filename = f"text_to_image_{image_id}.png"
            image_path = self.output_dir / image_filename

            update_progress_safe(70, f'Generating image with {self.model_type}...')

            # Set up generation parameters based on model type
            if self.model_type == "flux":
                image = self._generate_with_flux(enhanced_prompt, settings)
            elif self.model_type == "sdxl_turbo":
                image = self._generate_with_sdxl_turbo(enhanced_prompt, settings)
            elif self.model_type == "sdxl":
                image = self._generate_with_sdxl(enhanced_prompt, settings)
            elif self.model_type == "placeholder":
                image = self._generate_placeholder_image(enhanced_prompt, settings)
            else:
                image = self._generate_with_stable_diffusion(enhanced_prompt, settings)

            update_progress_safe(90, 'Saving generated image...')

            # Save the generated image
            image.save(str(image_path))
            print(f"Image saved to: {image_path}")

            update_progress_safe(100, 'Text-to-image generation completed!')

            return {
                'image_path': str(image_path),
                'prompt': enhanced_prompt,
                'original_prompt': prompt
            }

        except Exception as e:
            print(f"Error generating image: {str(e)}")
            import traceback
            traceback.print_exc()
            raise RuntimeError(f"Error generating image: {str(e)}")

    def _enhance_prompt_for_3d(self, prompt: str) -> str:
        """Enhance the prompt for better 3D object generation."""
        # Add descriptors that help with 3D generation
        enhanced_prompt = f"A detailed 3D render of {prompt}, studio lighting, white background, centered object, high quality, photorealistic, clean background, single object"
        return enhanced_prompt

    def _generate_with_flux(self, prompt: str, settings: Dict) -> Image.Image:
        """Generate image using FLUX Schnell optimized for RTX 3060."""
        print("Generating image with FLUX Schnell (RTX 3060 optimized)...")

        # Set seed for reproducibility
        seed = settings.get('seed', None)
        if seed is not None:
            generator = torch.Generator(device="cuda" if torch.cuda.is_available() else "cpu").manual_seed(seed)
        else:
            generator = None

        # Optimized parameters based on GPU
        gpu_info = self._get_gpu_memory_info()

        if gpu_info and gpu_info['is_rtx_3060']:
            # RTX 3060 optimizations: smaller size for better speed
            default_size = 768
        elif gpu_info and gpu_info['total_memory_gb'] >= 16:
            # High-end GPU: can handle larger images
            default_size = 1024
        else:
            # Conservative default
            default_size = 768

        width = settings.get('width', default_size)
        height = settings.get('height', default_size)

        # Ensure dimensions are multiples of 8 (required by FLUX)
        width = (width // 8) * 8
        height = (height // 8) * 8

        print(f"FLUX generation settings: {width}x{height}, steps: {settings.get('num_inference_steps', 4)}")

        # Clear GPU cache before generation
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        # FLUX Schnell parameters optimized for speed
        try:
            image = self.pipeline(
                prompt=prompt,
                generator=generator,
                num_inference_steps=settings.get('num_inference_steps', 4),  # FLUX Schnell is optimized for 4 steps
                guidance_scale=settings.get('guidance_scale', 0.0),  # FLUX Schnell doesn't use guidance
                width=width,
                height=height,
                max_sequence_length=256,  # Reduce sequence length for speed
            ).images[0]

        except RuntimeError as e:
            if "out of memory" in str(e).lower():
                print(f"GPU out of memory with {width}x{height}, trying smaller size...")
                # Clear cache and try with smaller size
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()

                # Reduce size by 25%
                new_width = int(width * 0.75) // 8 * 8  # Keep multiple of 8
                new_height = int(height * 0.75) // 8 * 8

                print(f"Retrying FLUX generation with {new_width}x{new_height}")
                image = self.pipeline(
                    prompt=prompt,
                    generator=generator,
                    num_inference_steps=settings.get('num_inference_steps', 4),
                    guidance_scale=settings.get('guidance_scale', 0.0),
                    width=new_width,
                    height=new_height,
                    max_sequence_length=256,
                ).images[0]
            else:
                raise e

        # Clear GPU cache after generation
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        print("FLUX image generation completed!")
        return image

    def _generate_with_stable_diffusion(self, prompt: str, settings: Dict) -> Image.Image:
        """Generate image using Stable Diffusion."""
        # Set seed for reproducibility
        seed = settings.get('seed', None)
        if seed is not None:
            generator = torch.Generator(device="cuda" if torch.cuda.is_available() else "cpu").manual_seed(seed)
        else:
            generator = None

        # Negative prompt for better quality
        negative_prompt = "blurry, low quality, cropped, cut off, text, watermark, signature, multiple objects, cluttered background, dark, shadows"

        # Stable Diffusion parameters (SD 1.5 works best with 512x512)
        image = self.pipeline(
            prompt=prompt,
            negative_prompt=negative_prompt,
            generator=generator,
            num_inference_steps=settings.get('num_inference_steps', 20),
            guidance_scale=settings.get('guidance_scale', 7.5),
            width=settings.get('width', 512),
            height=settings.get('height', 512),
        ).images[0]

        return image

    def _generate_with_sdxl_turbo(self, prompt: str, settings: Dict) -> Image.Image:
        """Generate image using SDXL Turbo for fast CPU generation."""
        print("Generating image with SDXL Turbo...")

        # Set seed for reproducibility
        seed = settings.get('seed', None)
        if seed is not None:
            generator = torch.Generator().manual_seed(seed)
        else:
            generator = None

        # SDXL Turbo is optimized for very few steps and no guidance
        # It works best with 1-4 steps and guidance_scale=0.0
        image = self.pipeline(
            prompt=prompt,
            generator=generator,
            num_inference_steps=1,  # SDXL Turbo works best with just 1 step!
            guidance_scale=0.0,     # No guidance needed for turbo
            width=512,              # Standard size for speed
            height=512,
        ).images[0]

        print("SDXL Turbo image generation completed!")
        return image

    def _generate_with_sdxl(self, prompt: str, settings: Dict) -> Image.Image:
        """Generate image using full SDXL for high quality generation."""
        print("Generating image with SDXL...")

        # Set seed for reproducibility
        seed = settings.get('seed', None)
        if seed is not None:
            generator = torch.Generator(device="cuda" if torch.cuda.is_available() else "cpu").manual_seed(seed)
        else:
            generator = None

        # Negative prompt for better quality
        negative_prompt = "blurry, low quality, cropped, cut off, text, watermark, signature, multiple objects, cluttered background, dark, shadows"

        # SDXL parameters for high quality
        image = self.pipeline(
            prompt=prompt,
            negative_prompt=negative_prompt,
            generator=generator,
            num_inference_steps=settings.get('num_inference_steps', 30),  # More steps for quality
            guidance_scale=settings.get('guidance_scale', 7.5),
            width=settings.get('width', 1024),  # SDXL native resolution
            height=settings.get('height', 1024),
        ).images[0]

        print("SDXL image generation completed!")
        return image

    def _generate_placeholder_image(self, prompt: str, settings: Dict) -> Image.Image:
        """Generate a placeholder image for testing the 3D pipeline."""
        print(f"Generating placeholder image for: {prompt}")

        from PIL import Image, ImageDraw, ImageFont
        import textwrap

        # Create a 512x512 white background image
        image = Image.new('RGB', (512, 512), 'white')
        draw = ImageDraw.Draw(image)

        # Try to use a default font, fallback to basic if not available
        try:
            font = ImageFont.truetype("arial.ttf", 24)
            small_font = ImageFont.truetype("arial.ttf", 16)
        except:
            try:
                font = ImageFont.load_default()
                small_font = ImageFont.load_default()
            except:
                font = None
                small_font = None

        # Draw a simple object representation
        # Draw a centered rectangle/box to represent the object
        box_size = 200
        x1 = (512 - box_size) // 2
        y1 = (512 - box_size) // 2
        x2 = x1 + box_size
        y2 = y1 + box_size

        # Draw the main object box
        draw.rectangle([x1, y1, x2, y2], outline='black', width=3, fill='lightgray')

        # Add some 3D effect lines
        offset = 20
        draw.line([x2, y1, x2 + offset, y1 - offset], fill='black', width=2)
        draw.line([x2, y2, x2 + offset, y2 - offset], fill='black', width=2)
        draw.line([x1, y2, x1 + offset, y2 - offset], fill='black', width=2)
        draw.line([x2 + offset, y1 - offset, x2 + offset, y2 - offset], fill='black', width=2)
        draw.line([x1 + offset, y2 - offset, x2 + offset, y2 - offset], fill='black', width=2)

        # Add text description
        if font:
            # Wrap the prompt text
            wrapped_text = textwrap.fill(prompt, width=30)
            lines = wrapped_text.split('\n')

            # Calculate text position
            text_y = y2 + 30
            for line in lines:
                bbox = draw.textbbox((0, 0), line, font=font)
                text_width = bbox[2] - bbox[0]
                text_x = (512 - text_width) // 2
                draw.text((text_x, text_y), line, fill='black', font=font)
                text_y += 30

            # Add instruction text
            instruction = "Add HF token for FLUX quality"
            if small_font:
                bbox = draw.textbbox((0, 0), instruction, font=small_font)
                text_width = bbox[2] - bbox[0]
                text_x = (512 - text_width) // 2
                draw.text((text_x, 450), instruction, fill='gray', font=small_font)

        print("Placeholder image generated successfully!")
        return image

    def _generate_with_cpu_optimized(self, prompt: str, settings: Dict) -> Image.Image:
        """Generate image using CPU-optimized pipeline with fast settings."""
        print("Generating image with CPU-optimized pipeline...")

        # Set seed for reproducibility
        seed = settings.get('seed', None)
        if seed is not None:
            generator = torch.Generator().manual_seed(seed)
        else:
            generator = None

        # Negative prompt for better quality
        negative_prompt = "blurry, low quality, cropped, cut off, text, watermark, signature, multiple objects, cluttered background, dark, shadows"

        # CPU-optimized parameters (much faster)
        image = self.pipeline(
            prompt=prompt,
            negative_prompt=negative_prompt,
            generator=generator,
            num_inference_steps=8,  # Much fewer steps for speed
            guidance_scale=6.0,     # Lower guidance for speed
            width=512,              # Standard size
            height=512,
        ).images[0]

        print("CPU-optimized image generation completed!")
        return image

    def _generate_with_subprocess(self, prompt: str, settings: Dict) -> Dict[str, str]:
        """Generate image using subprocess approach."""
        import subprocess
        import tempfile
        import json
        import sys

        try:
            # Path to the processor script
            processor_script = str(Path(__file__).parent / "text_to_image_processor.py")

            # Create temporary file for results
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                result_file = f.name

            # Use the current Python environment
            python_executable = sys.executable

            # Prepare command
            cmd = [
                python_executable,
                processor_script,
                '--prompt', prompt,
                '--output_dir', str(self.output_dir),
                '--settings', json.dumps(settings),
                '--result_file', result_file
            ]

            print(f"Running text-to-image processor: {' '.join(cmd)}")

            # Run the processor with longer timeout for first-time model downloads
            print("Starting text-to-image subprocess (this may take several minutes for first-time model download)...")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=1200)  # 20 minute timeout

            if result.returncode != 0:
                print(f"Text-to-image processor failed with return code {result.returncode}")
                print(f"STDOUT: {result.stdout}")
                print(f"STDERR: {result.stderr}")
                raise RuntimeError(f"Text-to-image processor failed: {result.stderr}")

            # Read the result
            with open(result_file, 'r') as f:
                output = json.load(f)

            # Clean up
            import os
            os.unlink(result_file)

            if 'error' in output:
                raise RuntimeError(output['error'])

            return output

        except subprocess.TimeoutExpired:
            raise RuntimeError("Text-to-image processing timed out")
        except Exception as e:
            print(f"Error in subprocess processing: {e}")
            raise RuntimeError(f"Failed to process with text-to-image subprocess: {str(e)}")


def create_text_to_image_pipeline(output_dir: str = "output", model_preference: str = "flux") -> TextToImagePipeline:
    """
    Factory function to create a TextToImagePipeline instance.

    Args:
        output_dir: Directory where generated images will be saved
        model_preference: "flux", "sdxl_turbo", "sdxl", or "stable_diffusion"

    Returns:
        TextToImagePipeline instance
    """
    return TextToImagePipeline(output_dir=output_dir, model_preference=model_preference)
