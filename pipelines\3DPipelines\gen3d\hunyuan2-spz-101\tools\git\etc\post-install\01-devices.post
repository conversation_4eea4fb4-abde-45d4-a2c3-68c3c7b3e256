maybe_create_devs ()
{
  local DEVDIR=/dev

  # Check for ${DEVDIR}/shm directory (for POSIX semaphores and POSIX shared mem)
  if [ -e "${DEVDIR}/shm" -a ! -d "${DEVDIR}/shm" ]
  then
    # No mercy.  Try to remove.
    rm -f "${DEVDIR}/shm"
    if [ -e "${DEVDIR}/shm" -a ! -d "${DEVDIR}/shm" ]
    then 
      echo
      echo "${DEVDIR}/shm is existant but not a directory."
      echo "POSIX semaphores and POSIX shared memory will not work"
      echo
    fi
  fi

  # Create it if necessary
  if [ ! -e "${DEVDIR}/shm" ]
  then
    mkdir "${DEVDIR}/shm"
    if [ ! -e "${DEVDIR}/shm" ]
    then
      echo
      echo "Creating ${DEVDIR}/shm directory failed."
      echo "POSIX semaphores and POSIX shared memory will not work"
      echo
    fi
  fi

  # Check for ${DEVDIR}/mqueue directory (for POSIX message queues)
  if [ -e "${DEVDIR}/mqueue" -a ! -d "${DEVDIR}/mqueue" ]
  then
    # No mercy.  Try to remove.
    rm -f "${DEVDIR}/mqueue"
    if [ -e "${DEVDIR}/mqueue" -a ! -d "${DEVDIR}/mqueue" ]
    then 
      echo
      echo "${DEVDIR}/mqueue is existant but not a directory."
      echo "POSIX message queues will not work"
      echo
    fi
  fi

  # Create it if necessary
  if [ ! -e "${DEVDIR}/mqueue" ]
  then
    mkdir "${DEVDIR}/mqueue"
    if [ ! -e "${DEVDIR}/mqueue" ]
    then
      echo
      echo "Creating ${DEVDIR}/mqueue directory failed."
      echo "POSIX message queues will not work"
      echo
    fi
  fi
}

maybe_create_devs
