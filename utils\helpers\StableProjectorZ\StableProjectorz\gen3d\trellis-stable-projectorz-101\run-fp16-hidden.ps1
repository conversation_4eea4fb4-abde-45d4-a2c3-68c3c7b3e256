# PowerShell script to run Trellis server completely hidden
# This prevents any command windows from appearing

$ErrorActionPreference = "Continue"

# Get the directory where this script is located
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

# Change to the script directory
Set-Location $ScriptDir

# Run the batch file with hidden window
$ProcessInfo = New-Object System.Diagnostics.ProcessStartInfo
$ProcessInfo.FileName = "cmd.exe"
$ProcessInfo.Arguments = "/c run-fp16.bat"
$ProcessInfo.WorkingDirectory = $ScriptDir
$ProcessInfo.WindowStyle = [System.Diagnostics.ProcessWindowStyle]::Hidden
$ProcessInfo.CreateNoWindow = $true
$ProcessInfo.UseShellExecute = $false
$ProcessInfo.RedirectStandardOutput = $true
$ProcessInfo.RedirectStandardError = $true

$Process = New-Object System.Diagnostics.Process
$Process.StartInfo = $ProcessInfo

# Start the process
$Process.Start() | Out-Null

# Read output and forward it (so logs still appear in main app)
$OutputReader = $Process.StandardOutput
$ErrorReader = $Process.StandardError

# Forward output to console so main app can capture it
while (!$Process.HasExited) {
    if (!$OutputReader.EndOfStream) {
        $Line = $OutputReader.ReadLine()
        if ($Line) { Write-Output $Line }
    }
    if (!$ErrorReader.EndOfStream) {
        $Line = $ErrorReader.ReadLine()
        if ($Line) { Write-Error $Line }
    }
    Start-Sleep -Milliseconds 100
}

# Read any remaining output
while (!$OutputReader.EndOfStream) {
    $Line = $OutputReader.ReadLine()
    if ($Line) { Write-Output $Line }
}
while (!$ErrorReader.EndOfStream) {
    $Line = $ErrorReader.ReadLine()
    if ($Line) { Write-Error $Line }
}

$Process.WaitForExit()
exit $Process.ExitCode
