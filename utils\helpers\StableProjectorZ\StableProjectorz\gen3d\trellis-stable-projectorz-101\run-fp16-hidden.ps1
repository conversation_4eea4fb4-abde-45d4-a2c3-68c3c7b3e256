# PowerShell script to run Trellis server completely hidden
# This prevents any command windows from appearing

# Get the directory where this script is located
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

# Change to the script directory
Set-Location $ScriptDir

# Simply run the batch file with Start-Process and hidden window
# This approach is simpler and more reliable
Start-Process -FilePath "cmd.exe" -ArgumentList "/c", "run-fp16.bat" -WorkingDirectory $ScriptDir -WindowStyle Hidden -Wait
