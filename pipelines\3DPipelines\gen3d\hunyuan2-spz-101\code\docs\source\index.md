---
hide-toc: true
---

# Welcome to Hunyuan3D

```{toctree}
:maxdepth: 3
:hidden: 

Installation <installation/index>
Get Started <started/index>
Model Zoo <modelzoo>
citation
```

```{toctree}
:caption: Useful Links
:hidden:
PyPI Page <https://pypi.org/project/hy3dgen/>
GitHub Repository <https://github.com/Tencent/Hunyuan3D-2>
Paper <https://arxiv.org/abs/2501.12202>
```

<br/>

☯️ Hunyuan3D 2.0 is an advanced large-scale 3D synthesis system for generating high-resolution textured 3D assets.

The system includes the following foundation components:

1. [Hunyuan3D-DiT](): a large-scale shape generation model.
2. [Hunyuan3D-Paint](): a large-scale texture synthesis model.
3. [Hunyuan3D-Studio](): a versatile, user-friendly production platform that simplifies the re-creation process of 3D
   assets. It allows both professional and amateur users to manipulate or even animate their meshes efficiently.
4. [FlashVDM](): a universal acceleration framework.

```{nbgallery}
```

<div class="toctree-wrapper compound">
<div class="nbsphinx-gallery">
<a class="reference internal" href="started/quicktour.html">
  <b>Quicktour</b>
  <p style="color:var(--color-content-foreground)">Learn the fundamentals of using ∇-Prox. We recommend starting here if you are using 🎉 ∇-Prox for the first time! </p>
</a>
<a class="reference internal" href="tutorials/index.html">
  <b>Tutorials</b>
  <p style="color:var(--color-content-foreground)">Understand the design of the library and the mathematics behind the code. </p>
</a>
<a class="reference internal" href="api/index.html">
  <b>API Documentation</b>
  <p style="color:var(--color-content-foreground)">Explore the complete reference guide. Useful if you want to develop programs with ∇-Prox. </p>
</a>
</div>
</div>