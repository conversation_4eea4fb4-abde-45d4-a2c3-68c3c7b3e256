import React, { useRef, useEffect, useState, Suspense } from 'react';
import { <PERSON>vas, useFrame, useLoader } from '@react-three/fiber';
import { OrbitControls, Stage, useGLTF, Environment } from '@react-three/drei';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { PLYLoader } from 'three/examples/jsm/loaders/PLYLoader.js';
import { Mesh, PointsMaterial, Points, Vector3, Color, Material } from 'three';
import { Info, X, Clock, Settings, Cpu, Zap, Image, Type, Lightbulb } from 'lucide-react';
import * as THREE from 'three';

interface ModelErrorBoundaryProps {
  isTextured: boolean;
  children: React.ReactNode;
}

interface ModelErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

interface RotatingMeshProps {
  isTextured: boolean;
  isDarkMode: boolean;
}

interface GLBModelProps {
  modelUrl: string;
  isTextured: boolean;
}

interface PLYModelProps {
  modelUrl: string;
  isTextured: boolean;
}

interface HSL {
  h: number;
  s: number;
  l: number;
}

// Lighting settings interface
interface LightingSettings {
  ambientIntensity: number;
  directionalIntensity: number;
  hemisphereIntensity: number;
  keyLightColor: string;
  fillLightColor: string;
  rimLightColor: string;
}

// Default lighting settings
const DEFAULT_LIGHTING: LightingSettings = {
  ambientIntensity: 1.0,
  directionalIntensity: 1.5,
  hemisphereIntensity: 1.0,
  keyLightColor: "#ffffff",
  fillLightColor: "#ffffff",
  rimLightColor: "#ffffff"
};

// Error boundary for 3D model loading
class ModelErrorBoundary extends React.Component<ModelErrorBoundaryProps, ModelErrorBoundaryState> {
  constructor(props: ModelErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    console.error('ModelErrorBoundary: Error caught:', error);
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ModelErrorBoundary caught an error:', error);
    console.error('Error info:', errorInfo);
    console.error('Stack trace:', error.stack);
  }

  render() {
    if (this.state.hasError) {
      console.error('Model loading error, showing fallback:', this.state.error);
      // Return fallback sphere with error indication
      return (
        <mesh>
          <sphereGeometry args={[1, 32, 32]} />
          <meshStandardMaterial color="red" wireframe={!this.props.isTextured} />
        </mesh>
      );
    }

    return this.props.children;
  }
}

// Rotating mesh component
const RotatingMesh: React.FC<RotatingMeshProps> = ({ isTextured, isDarkMode }) => {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame((state, delta) => {
    if (meshRef.current) {
      // Rotate slowly (0.3 radians per second)
      meshRef.current.rotation.y += delta * 0.3;
    }
  });

  return (
    <mesh ref={meshRef}>
      <sphereGeometry args={[1.0, 32, 32]} />
      <meshStandardMaterial
        color={isDarkMode ? '#303030' : '#404040'}
        wireframe={!isTextured}
      />
    </mesh>
  );
};

// Safe GLB Model component - memoized to prevent unnecessary re-renders
const GLBModel = React.memo<GLBModelProps>(({ modelUrl, isTextured }) => {
  console.log('GLBModel: Starting to load:', modelUrl);
  console.log('GLBModel: Model URL type:', typeof modelUrl);
  console.log('GLBModel: Model URL length:', modelUrl?.length);
  console.log('GLBModel: Model URL starts with data:', modelUrl?.startsWith('data:'));

  const modelRef = useRef<THREE.Group>(null);
  const [modelScale, setModelScale] = useState(1.0);
  const [modelPosition, setModelPosition] = useState<[number, number, number]>([0, 0, 0]);

  // Use useGLTF - this must be called unconditionally
  console.log('GLBModel: Calling useGLTF...');
  const gltf = useGLTF(modelUrl);
  const scene = gltf?.scene;

  console.log('GLBModel: useGLTF result:', { gltf, scene, hasScene: !!scene });
  if (gltf && gltf.scene) {
    console.log('GLBModel: Scene children count:', gltf.scene.children.length);
    console.log('GLBModel: Scene type:', gltf.scene.type);
  }

  // Auto-fit model to viewport (only run once when scene loads)
  useEffect(() => {
    if (scene) {
      const box = new THREE.Box3().setFromObject(scene);
      const center = box.getCenter(new THREE.Vector3());
      const size = box.getSize(new THREE.Vector3());
      const maxDim = Math.max(size.x, size.y, size.z);
      const targetSize = 2.0;
      const scale = maxDim > 0 ? targetSize / maxDim : 1.0;
      const position = [-center.x * scale, -center.y * scale, -center.z * scale] as [number, number, number];
      setModelScale(scale);
      setModelPosition(position);
    }
  }, [scene]);

  // Enhanced material handling for better lighting response
  useEffect(() => {
    if (scene) {
      scene.traverse((node: THREE.Object3D) => {
        if ((node as Mesh).isMesh) {
          const mesh = node as Mesh;
          mesh.castShadow = true;
          mesh.receiveShadow = true;
          
          const materials = Array.isArray(mesh.material) ? mesh.material : [mesh.material];
          
          materials.forEach((material) => {
            // Convert to MeshStandardMaterial if it's not already
            if (!(material instanceof THREE.MeshStandardMaterial)) {
              const standardMaterial = new THREE.MeshStandardMaterial();

              // Copy properties from original material
              if (material instanceof THREE.MeshBasicMaterial) {
                standardMaterial.color.copy(material.color);
                standardMaterial.map = material.map;
                standardMaterial.transparent = material.transparent;
                standardMaterial.opacity = material.opacity;
              } else if (material instanceof THREE.MeshPhongMaterial) {
                standardMaterial.color.copy(material.color);
                standardMaterial.map = material.map;
                standardMaterial.transparent = material.transparent;
                standardMaterial.opacity = material.opacity;
              }

              // Set standard material properties
              standardMaterial.roughness = 0.5;
              standardMaterial.metalness = 0.0;
              standardMaterial.envMapIntensity = 1.0;
              standardMaterial.wireframe = !isTextured; // Apply wireframe mode
              standardMaterial.needsUpdate = true;

              mesh.material = standardMaterial;
            } else {
              // Enhance existing MeshStandardMaterial
              material.roughness = Math.max(material.roughness, 0.1);
              material.metalness = Math.max(material.metalness, 0.0);
              material.envMapIntensity = 1.0;
              material.wireframe = !isTextured; // Apply wireframe mode
              material.needsUpdate = true;
            }

            // Adjust very dark materials
            if ('color' in material && material.color instanceof Color) {
              const hsl: HSL = { h: 0, s: 0, l: 0 };
              material.color.getHSL(hsl);
              if (hsl.l < 0.15) {
                material.color.setHSL(hsl.h, hsl.s, Math.max(hsl.l, 0.2));
              }
            }
          });
        }
      });
    }
  }, [scene, isTextured]);

  useFrame((state, delta) => {
    if (modelRef.current) {
      modelRef.current.rotation.y += delta * 0.3;
    }
  });

  // Check if scene is valid AFTER all hooks have been called
  if (!scene) {
    console.error('GLBModel: No scene found in GLTF, returning fallback sphere');
    console.error('GLBModel: GLTF object:', gltf);
    console.error('GLBModel: Model URL that failed:', modelUrl);
    return (
      <mesh>
        <sphereGeometry args={[1, 32, 32]} />
        <meshStandardMaterial color="yellow" wireframe={!isTextured} />
      </mesh>
    );
  }

  return (
    <primitive
      ref={modelRef}
      object={scene}
      scale={modelScale}
      position={modelPosition}
    />
  );
});

// PLY Model component (for point clouds) - memoized to prevent unnecessary re-renders
const PLYModel = React.memo<PLYModelProps>(({ modelUrl, isTextured }) => {
  console.log('PLYModel loading:', modelUrl);

  // Use useLoader with a function to ensure PLYLoader is instantiated correctly if needed
  const geometry = useLoader(PLYLoader, modelUrl);
  const pointsRef = useRef<THREE.Points>(null);
  const [pointsMaterial, setPointsMaterial] = useState<THREE.PointsMaterial | null>(null);

  // Initialize points material
  useEffect(() => {
    if (geometry) {
      const material = new THREE.PointsMaterial({
        size: 0.003,
        vertexColors: true,
        sizeAttenuation: true,
        alphaTest: 0.1,
        transparent: false,
        color: 0xffffff,
      });
      
      setPointsMaterial(material);
      
      // Auto-fit model
      geometry.computeBoundingSphere();
      if (geometry.boundingSphere && pointsRef.current) {
        const sphere = geometry.boundingSphere;
        const scale = 2.0 / sphere.radius;
        pointsRef.current.scale.set(scale, scale, scale);
        pointsRef.current.position.sub(sphere.center.clone().multiplyScalar(scale));
      }
    }
  }, [geometry]);

  // Handle auto-fitting and rotation
  useEffect(() => {
    if (pointsRef.current && geometry) {
      if (!geometry.boundingSphere) {
        geometry.computeBoundingSphere();
      }
      
      const sphere = geometry.boundingSphere;
      if (sphere) {
        const scale = 2.0 / sphere.radius;
        pointsRef.current.scale.set(scale, scale, scale);
        pointsRef.current.position.sub(sphere.center.clone().multiplyScalar(scale));
      }
    }
  }, [geometry]);

  useFrame((state, delta) => {
    if (pointsRef.current) {
      pointsRef.current.rotation.y += delta * 0.3;
    }
  });

  if (!geometry) {
    console.warn('PLYModel: Geometry not loaded yet, returning null.');
    return null;
  }
  
  return (
    <points ref={pointsRef} geometry={geometry} material={pointsMaterial || undefined} castShadow>
      {/* Fallback material if state is not ready */}
      {!pointsMaterial && <pointsMaterial attach="material" size={0.005} color="magenta" />}
    </points>
  );
});

interface GenerationStats {
  generationMode?: 'text-to-3d' | 'image-to-3d';
  prompt?: string;
  enhancedPrompt?: string;
  imageModel?: string;
  settings?: {
    ss_steps?: number;
    ss_cfg_strength?: number;
    slat_steps?: number;
    slat_cfg_strength?: number;
    seed?: number;
    simplify?: number;
    texture_size?: number;
    enable_lighting_optimizer?: boolean;
    enable_delighter?: boolean;
    delighter_quality?: string;
  };
  fileInfo?: {
    type?: string;
    size?: number;
    vertices?: number;
    faces?: number;
  };
  timing?: {
    totalTime?: number;
    textToImageTime?: number;
    modelGenerationTime?: number;
    delighterTime?: number;
  };
}

interface ModelViewerProps {
  modelUrl?: string | null;
  filePath?: string;
  videoUrl?: string;
  isTextured?: boolean;
  isDarkMode?: boolean;
  generationStats?: GenerationStats;
}

export const ModelViewer: React.FC<ModelViewerProps> = ({
  modelUrl,
  filePath,
  videoUrl,
  isTextured = true,
  isDarkMode = false,
  generationStats,
}) => {
  const [loadingError, setLoadingError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [fileType, setFileType] = useState<string | null>(null);
  const [isInfoExpanded, setIsInfoExpanded] = useState(false);
  const [isLightingMenuOpen, setIsLightingMenuOpen] = useState(false);
  const [showInfo, setShowInfo] = useState(false);
  const [activeTab, setActiveTab] = useState('info');
  const infoPanelRef = useRef<HTMLDivElement>(null);
  console.log("ModelViewer received modelUrl:", modelUrl);
  console.log("ModelViewer received filePath:", filePath);

  // Load saved lighting settings or use defaults
  const [lightingSettings, setLightingSettings] = useState<LightingSettings>(() => {
    const saved = localStorage.getItem('modelViewerLighting');
    return saved ? JSON.parse(saved) : DEFAULT_LIGHTING;
  });

  // Save lighting settings when they change
  useEffect(() => {
    localStorage.setItem('modelViewerLighting', JSON.stringify(lightingSettings));
  }, [lightingSettings]);

  const lightingMenuRef = useRef<HTMLDivElement>(null);

  // Close lighting menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (lightingMenuRef.current && !lightingMenuRef.current.contains(event.target as Node)) {
        setIsLightingMenuOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [lightingMenuRef]);

  // Determine file type
  const getFileExtension = (path: string | undefined) => {
    if (!path) return '';
    return path.split('.').pop()?.toLowerCase() || '';
  };

  const renderModel = () => {
    if (!modelUrl) {
      console.log('No modelUrl provided, showing default sphere');
      return <RotatingMesh isTextured={isTextured} isDarkMode={isDarkMode} />;
    }

    const extension = getFileExtension(filePath);
    console.log('Model URL:', modelUrl, 'File path:', filePath, 'Detected file type:', extension);

    try {
      console.log(`renderModel: Attempting to render ${filePath}`);

      if (extension === 'glb' || extension === 'gltf') {
        return (
          <React.Suspense fallback={<RotatingMesh isTextured={isTextured} isDarkMode={isDarkMode} />}>
            <ModelErrorBoundary isTextured={isTextured}>
              <GLBModel
                key={filePath}
                modelUrl={modelUrl}
                isTextured={isTextured}
              />
            </ModelErrorBoundary>
          </React.Suspense>
        );
      }

      if (extension === 'ply') {
        return (
          <React.Suspense fallback={<RotatingMesh isTextured={isTextured} isDarkMode={isDarkMode} />}>
            <PLYModel
              key={filePath}
              modelUrl={modelUrl}
              isTextured={isTextured}
            />
          </React.Suspense>
        );
      }

      console.warn(`renderModel: Unsupported file extension: ${extension} from path: ${filePath}`);
      return <RotatingMesh isTextured={isTextured} isDarkMode={isDarkMode} />;
    } catch (error) {
      console.error("Error in renderModel:", error);
      return <RotatingMesh isTextured={isTextured} isDarkMode={isDarkMode} />;
    }
  };

  // Show error state if there's a loading error
  if (loadingError) {
    return (
      <div className={`w-full h-full rounded-lg flex items-center justify-center ${
        isDarkMode
          ? 'bg-gradient-to-br from-gray-800 to-gray-900 text-white'
          : 'bg-gradient-to-br from-white to-gray-100 text-gray-800'
      }`}>
        <div className="text-center p-4">
          <div className="text-red-500 mb-2">⚠️ Model Loading Error</div>
          <div className="text-sm">{loadingError}</div>
          <div className="text-xs mt-2 opacity-70">URL: {modelUrl}</div>
        </div>
      </div>
    );
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className={`w-full h-full rounded-lg flex items-center justify-center ${
        isDarkMode
          ? 'bg-gradient-to-br from-gray-800 to-gray-900 text-white'
          : 'bg-gradient-to-br from-white to-gray-100 text-gray-800'
      }`}>
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
          <div className="text-sm">Testing model URL...</div>
        </div>
      </div>
    );
  }

  // Format file size
  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Unknown';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  // Format time duration
  const formatTime = (seconds?: number) => {
    if (!seconds) return 'Unknown';
    if (seconds < 60) return `${seconds.toFixed(1)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds.toFixed(0)}s`;
  };

  const controlsRef = useRef();
  const [lighting, setLighting] = useState(DEFAULT_LIGHTING);

  // For informational popover
  const [isInfoVisible, setIsInfoVisible] = useState(false);

  return (
    <div className={`relative w-full h-full rounded-lg ${isDarkMode ? 'dark' : ''}`}>
      <Canvas
        shadows
        camera={{ position: [0, 1, 4], fov: 50 }}
        className="w-full h-full rounded-lg"
      >
        <Suspense fallback={null}>
          <ambientLight intensity={lightingSettings.ambientIntensity} />
          <hemisphereLight 
            args={[new THREE.Color(0xffffff), new THREE.Color(0x444444), lightingSettings.hemisphereIntensity]}
          />
          <directionalLight
            castShadow
            position={[5, 5, 5]}
            intensity={lightingSettings.directionalIntensity}
            color={lightingSettings.keyLightColor}
            shadow-mapSize-width={2048}
            shadow-mapSize-height={2048}
            shadow-camera-far={50}
            shadow-camera-left={-10}
            shadow-camera-right={10}
            shadow-camera-top={10}
            shadow-camera-bottom={-10}
          />
          <directionalLight
            position={[-5, 3, -5]}
            intensity={lightingSettings.directionalIntensity * 0.5}
            color={lightingSettings.fillLightColor}
          />
           <directionalLight
            position={[0, 3, -5]}
            intensity={lightingSettings.directionalIntensity * 0.2}
            color={lightingSettings.rimLightColor}
          />

          <Environment preset={isDarkMode ? "night" : "city"} />

              {renderModel()}
        </Suspense>
        <OrbitControls 
          makeDefault 
          autoRotate 
          autoRotateSpeed={0.8}
          minDistance={1}
          maxDistance={10}
          enablePan={false}
        />
      </Canvas>

      {/* Top-left icons */}
      <div className="absolute top-4 left-4 flex flex-col space-y-2">
        <button
          onClick={() => setIsInfoExpanded(!isInfoExpanded)}
          className={`p-2 rounded-full transition-colors ${
            isInfoExpanded
              ? 'bg-blue-500 text-white'
              : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
          }`}
          title="Toggle Info Panel"
        >
          <Info size={20} />
        </button>
        <button
            onClick={() => setIsLightingMenuOpen(!isLightingMenuOpen)}
          className={`p-2 rounded-full transition-colors ${
            isLightingMenuOpen
              ? 'bg-blue-500 text-white'
              : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
          }`}
          title="Toggle Lighting Settings"
        >
          <Lightbulb size={20} />
        </button>
      </div>

      {/* Info Panel */}
      {isInfoExpanded && (
        <div
          ref={infoPanelRef}
          className="absolute top-4 left-16 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg w-64 text-sm text-gray-700 dark:text-gray-300"
        >
          <div className="flex justify-between items-center mb-2">
            <h3 className="font-bold text-lg text-gray-900 dark:text-white">Generation Info</h3>
            <button onClick={() => setIsInfoExpanded(false)} className="text-gray-500 hover:text-gray-800 dark:hover:text-white">
              <X size={18} />
            </button>
          </div>
          {/* Tabs */}
          <div className="flex border-b border-gray-200 dark:border-gray-700 mb-2">
            <button
              onClick={() => setActiveTab('info')}
              className={`px-4 py-2 text-sm font-medium ${
                activeTab === 'info'
                  ? 'border-b-2 border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'text-gray-500 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
            >
              Info
            </button>
            <button
              onClick={() => setActiveTab('stats')}
              className={`px-4 py-2 text-sm font-medium ${
                activeTab === 'stats'
                  ? 'border-b-2 border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'text-gray-500 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
            >
              Stats
            </button>
          </div>

          {/* Content */}
          {activeTab === 'info' && (
            <div className="space-y-2">
              <p><strong>Mode:</strong> {generationStats?.generationMode?.replace('-', ' ')}</p>
              {generationStats?.imageModel && <p><strong>Image Model:</strong> {generationStats.imageModel}</p>}
              <p><strong>Prompt:</strong></p>
              <p className="p-2 bg-gray-100 dark:bg-gray-700 rounded text-xs">{generationStats?.prompt || 'N/A'}</p>
            </div>
          )}
          {activeTab === 'stats' && (
            <div className="space-y-2">
              <p><strong>Total Time:</strong> {formatTime(generationStats?.timing?.totalTime)}</p>
              <p><strong>Vertices:</strong> {generationStats?.fileInfo?.vertices?.toLocaleString()}</p>
              <p><strong>Faces:</strong> {generationStats?.fileInfo?.faces?.toLocaleString()}</p>
              <p><strong>File Size:</strong> {formatFileSize(generationStats?.fileInfo?.size)}</p>
            </div>
          )}
          </div>
        )}

      {/* Lighting Settings Panel */}
        {isLightingMenuOpen && (
        <div 
          ref={lightingMenuRef}
          className="absolute top-4 right-4 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg w-72 text-sm text-gray-700 dark:text-gray-300"
        >
          <div className="flex justify-between items-center mb-3">
            <h3 className="font-bold text-lg text-gray-900 dark:text-white">Lighting Settings</h3>
            <button onClick={() => setIsLightingMenuOpen(false)} className="text-gray-500 hover:text-gray-800 dark:hover:text-white">
              <X size={18} />
            </button>
          </div>

            <div className="space-y-3">
            <LightingSlider
              label="Ambient"
                  value={lightingSettings.ambientIntensity}
              onChange={(val) => setLightingSettings(p => ({...p, ambientIntensity: val}))}
            />
            <LightingSlider
              label="Directional"
                  value={lightingSettings.directionalIntensity}
              onChange={(val) => setLightingSettings(p => ({...p, directionalIntensity: val}))}
            />
            <LightingSlider
              label="Hemisphere"
                  value={lightingSettings.hemisphereIntensity}
              onChange={(val) => setLightingSettings(p => ({...p, hemisphereIntensity: val}))}
            />
            <div className="grid grid-cols-2 gap-2 pt-2 border-t border-gray-200 dark:border-gray-700">
              <ColorPicker
                label="Key Light"
                color={lightingSettings.keyLightColor}
                onChange={(color) => setLightingSettings(p => ({...p, keyLightColor: color}))}
              />
              <ColorPicker
                label="Fill Light"
                color={lightingSettings.fillLightColor}
                onChange={(color) => setLightingSettings(p => ({...p, fillLightColor: color}))}
              />
               <ColorPicker
                label="Rim Light"
                color={lightingSettings.rimLightColor}
                onChange={(color) => setLightingSettings(p => ({...p, rimLightColor: color}))}
                />
              </div>
            </div>
          </div>
        )}
    </div>
  );
};

const LightingSlider = ({ label, value, onChange }: { label: string, value: number, onChange: (value: number) => void }) => (
  <div className="flex flex-col">
    <div className="flex justify-between items-center text-xs">
      <label className="font-medium text-gray-800 dark:text-gray-100">{label}</label>
      <span className="text-gray-600 dark:text-gray-400">{value.toFixed(2)}</span>
    </div>
    <input
      type="range"
      min="0"
      max="3"
      step="0.01"
      value={value}
      onChange={(e) => onChange(parseFloat(e.target.value))}
      className="w-full h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer"
    />
  </div>
);

const ColorPicker = ({ label, color, onChange }: { label: string, color: string, onChange: (color: string) => void }) => (
  <div className="flex flex-col items-center">
    <label className="text-xs font-medium mb-1 text-gray-800 dark:text-gray-100">{label}</label>
    <input
      type="color"
      value={color}
      onChange={(e) => onChange(e.target.value)}
      className="w-16 h-8 p-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md cursor-pointer"
    />
  </div>
);

export default ModelViewer;