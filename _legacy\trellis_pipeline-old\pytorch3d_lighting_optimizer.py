"""
PyTorch3D-based Differentiable Lighting Optimizer for 3D Models

This module uses PyTorch3D's differentiable rendering capabilities to optimize
lighting conditions for 3D models, providing more accurate and sophisticated
lighting enhancement compared to traditional image processing approaches.
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import trimesh
from PIL import Image
from typing import Dict, Tuple, Optional, List
import json
import os
from pathlib import Path

# Check if PyTorch3D is available
try:
    import pytorch3d
    from pytorch3d.structures import Meshes
    from pytorch3d.renderer import (
        FoVPerspectiveCameras,
        PointLights,
        DirectionalLights,
        RasterizationSettings,
        MeshRenderer,
        MeshRasterizer,
        SoftPhongShader,
        TexturesVertex,
        TexturesUV,
        Materials,
        BlendParams,
    )
    from pytorch3d.transforms import Rotate, Translate
    from pytorch3d.io import load_obj, save_obj
    from pytorch3d.loss import chamfer_distance
    PYTORCH3D_AVAILABLE = True
    print("PyTorch3D successfully imported for differentiable lighting optimization")
except ImportError as e:
    PYTORCH3D_AVAILABLE = False
    print(f"PyTorch3D not available: {e}")
    print("Falling back to traditional lighting enhancement")


class PyTorch3DLightingOptimizer:
    """
    Advanced lighting optimizer using PyTorch3D's differentiable rendering.
    """
    
    def __init__(self, device: Optional[str] = None):
        if not PYTORCH3D_AVAILABLE:
            raise ImportError("PyTorch3D is required for differentiable lighting optimization")
        
        self.device = device or ("cuda" if torch.cuda.is_available() else "cpu")
        print(f"PyTorch3D Lighting Optimizer initialized on device: {self.device}")
        
        # Default optimization settings
        self.default_settings = {
            'num_lights': 4,                    # Number of optimizable lights
            'optimization_steps': 100,         # Number of optimization iterations
            'learning_rate': 0.01,             # Learning rate for optimization
            'ambient_strength': 0.3,           # Ambient lighting strength
            'diffuse_strength': 0.7,           # Diffuse lighting strength
            'specular_strength': 0.2,          # Specular lighting strength
            'light_distance': 3.0,             # Distance of lights from model
            'target_brightness': 0.6,          # Target average brightness
            'shadow_softness': 0.1,            # Shadow softening factor
            'color_preservation': 0.8,         # How much to preserve original colors
        }
    
    def optimize_lighting(self, mesh: trimesh.Trimesh, settings: Optional[Dict] = None) -> trimesh.Trimesh:
        """
        Optimize lighting for a 3D model using differentiable rendering.
        
        Args:
            mesh: Input trimesh object
            settings: Optimization settings dictionary
            
        Returns:
            Enhanced trimesh object with optimized lighting
        """
        if settings is None:
            settings = self.default_settings.copy()
        
        print("Starting PyTorch3D differentiable lighting optimization...")
        
        try:
            # Convert trimesh to PyTorch3D format
            pytorch3d_mesh = self._trimesh_to_pytorch3d(mesh)
            
            # Setup camera and renderer
            cameras = self._setup_camera()
            renderer = self._setup_renderer(cameras)
            
            # Optimize lighting parameters
            optimized_lights, optimized_materials = self._optimize_lighting_parameters(
                pytorch3d_mesh, renderer, cameras, settings
            )
            
            # Apply optimized lighting to the mesh
            enhanced_mesh = self._apply_optimized_lighting(
                mesh, optimized_lights, optimized_materials, settings
            )
            
            print("✓ PyTorch3D lighting optimization completed successfully")
            return enhanced_mesh
            
        except Exception as e:
            print(f"PyTorch3D optimization failed: {e}")
            print("Falling back to traditional lighting enhancement...")
            # Fallback to traditional method
            from lighting_enhancement import enhance_model_lighting
            return enhance_model_lighting(mesh, settings)
    
    def _trimesh_to_pytorch3d(self, mesh: trimesh.Trimesh) -> Meshes:
        """Convert trimesh to PyTorch3D Meshes format."""
        # Extract vertices and faces
        vertices = torch.tensor(mesh.vertices, dtype=torch.float32, device=self.device)
        faces = torch.tensor(mesh.faces, dtype=torch.long, device=self.device)
        
        # Handle textures if available
        if hasattr(mesh.visual, 'vertex_colors') and mesh.visual.vertex_colors is not None:
            # Vertex colors
            vertex_colors = torch.tensor(
                mesh.visual.vertex_colors[:, :3] / 255.0, 
                dtype=torch.float32, 
                device=self.device
            )
            textures = TexturesVertex(verts_features=[vertex_colors])
        elif hasattr(mesh.visual, 'material') and hasattr(mesh.visual.material, 'baseColorTexture'):
            # UV textures (more complex, simplified for now)
            # For now, use a default white texture
            vertex_colors = torch.ones((vertices.shape[0], 3), dtype=torch.float32, device=self.device)
            textures = TexturesVertex(verts_features=[vertex_colors])
        else:
            # Default white texture
            vertex_colors = torch.ones((vertices.shape[0], 3), dtype=torch.float32, device=self.device)
            textures = TexturesVertex(verts_features=[vertex_colors])
        
        # Create PyTorch3D mesh
        pytorch3d_mesh = Meshes(
            verts=[vertices],
            faces=[faces],
            textures=textures
        )
        
        return pytorch3d_mesh
    
    def _setup_camera(self) -> FoVPerspectiveCameras:
        """Setup camera for rendering."""
        # Position camera to view the model
        R = torch.eye(3, device=self.device).unsqueeze(0)  # No rotation
        T = torch.tensor([[0.0, 0.0, 3.0]], device=self.device)  # Move camera back
        
        cameras = FoVPerspectiveCameras(
            device=self.device,
            R=R,
            T=T,
            fov=60.0
        )
        
        return cameras
    
    def _setup_renderer(self, cameras: FoVPerspectiveCameras) -> MeshRenderer:
        """Setup differentiable renderer."""
        # Rasterization settings
        raster_settings = RasterizationSettings(
            image_size=512,
            blur_radius=0.0,
            faces_per_pixel=1,
        )
        
        # Create renderer
        renderer = MeshRenderer(
            rasterizer=MeshRasterizer(
                cameras=cameras,
                raster_settings=raster_settings
            ),
            shader=SoftPhongShader(
                device=self.device,
                cameras=cameras,
                blend_params=BlendParams(background_color=(0.0, 0.0, 0.0))
            )
        )
        
        return renderer

    def _optimize_lighting_parameters(self, mesh: Meshes, renderer: MeshRenderer,
                                    cameras: FoVPerspectiveCameras, settings: Dict) -> Tuple[PointLights, Materials]:
        """Optimize lighting parameters using differentiable rendering."""
        print(f"Optimizing lighting with {settings['optimization_steps']} iterations...")

        # Initialize optimizable lighting parameters
        num_lights = settings['num_lights']
        light_distance = settings['light_distance']

        # Create initial light positions (arranged around the model)
        angles = torch.linspace(0, 2 * np.pi, num_lights, device=self.device)
        light_positions = torch.stack([
            light_distance * torch.cos(angles),
            torch.zeros_like(angles),  # Keep lights at same height
            light_distance * torch.sin(angles)
        ], dim=1)

        # Optimizable parameters
        light_positions = nn.Parameter(light_positions)
        light_intensities = nn.Parameter(torch.ones(num_lights, 3, device=self.device))
        ambient_color = nn.Parameter(torch.tensor([settings['ambient_strength']] * 3, device=self.device))
        diffuse_color = nn.Parameter(torch.tensor([settings['diffuse_strength']] * 3, device=self.device))
        specular_color = nn.Parameter(torch.tensor([settings['specular_strength']] * 3, device=self.device))

        # Optimizer
        optimizer = optim.Adam([
            light_positions, light_intensities, ambient_color, diffuse_color, specular_color
        ], lr=settings['learning_rate'])

        # Target brightness for optimization
        target_brightness = settings['target_brightness']

        # Optimization loop
        for step in range(settings['optimization_steps']):
            optimizer.zero_grad()

            # Create lights with current parameters
            lights = PointLights(
                device=self.device,
                location=light_positions,
                diffuse_color=light_intensities,
                specular_color=light_intensities * 0.5,  # Softer specular
                ambient_color=ambient_color.unsqueeze(0).expand(num_lights, -1)
            )

            # Create materials
            materials = Materials(
                device=self.device,
                ambient_color=ambient_color.unsqueeze(0),
                diffuse_color=diffuse_color.unsqueeze(0),
                specular_color=specular_color.unsqueeze(0),
                shininess=64.0
            )

            # Render the mesh
            rendered_images = renderer(mesh, cameras=cameras, lights=lights, materials=materials)

            # Extract RGB channels
            rgb_images = rendered_images[..., :3]

            # Calculate loss
            loss = self._calculate_lighting_loss(rgb_images, target_brightness, settings)

            # Backward pass
            loss.backward()
            optimizer.step()

            # Clamp parameters to reasonable ranges
            with torch.no_grad():
                light_intensities.clamp_(0.1, 2.0)
                ambient_color.clamp_(0.0, 1.0)
                diffuse_color.clamp_(0.0, 1.0)
                specular_color.clamp_(0.0, 1.0)

            if step % 20 == 0:
                print(f"  Step {step}/{settings['optimization_steps']}, Loss: {loss.item():.4f}")

        # Create final optimized lights and materials
        final_lights = PointLights(
            device=self.device,
            location=light_positions.detach(),
            diffuse_color=light_intensities.detach(),
            specular_color=light_intensities.detach() * 0.5,
            ambient_color=ambient_color.detach().unsqueeze(0).expand(num_lights, -1)
        )

        final_materials = Materials(
            device=self.device,
            ambient_color=ambient_color.detach().unsqueeze(0),
            diffuse_color=diffuse_color.detach().unsqueeze(0),
            specular_color=specular_color.detach().unsqueeze(0),
            shininess=64.0
        )

        print("✓ Lighting optimization completed")
        return final_lights, final_materials

    def _calculate_lighting_loss(self, rendered_images: torch.Tensor, target_brightness: float,
                               settings: Dict) -> torch.Tensor:
        """Calculate loss function for lighting optimization."""
        # Average brightness loss
        current_brightness = rendered_images.mean()
        brightness_loss = torch.abs(current_brightness - target_brightness)

        # Shadow softness loss (encourage even lighting)
        brightness_variance = rendered_images.var()
        shadow_loss = brightness_variance * settings['shadow_softness']

        # Color preservation loss (prevent oversaturation)
        color_std = rendered_images.std(dim=-1).mean()
        color_loss = torch.abs(color_std - 0.1) * (1.0 - settings['color_preservation'])

        # Total loss
        total_loss = brightness_loss + shadow_loss + color_loss

        return total_loss

    def _apply_optimized_lighting(self, mesh: trimesh.Trimesh, lights: PointLights,
                                materials: Materials, settings: Dict) -> trimesh.Trimesh:
        """Apply optimized lighting parameters to the original mesh."""
        print("Applying optimized lighting to mesh...")

        # Create a copy of the mesh
        enhanced_mesh = mesh.copy()

        # Extract optimized parameters
        ambient_color = materials.ambient_color[0].cpu().numpy()
        diffuse_color = materials.diffuse_color[0].cpu().numpy()
        specular_color = materials.specular_color[0].cpu().numpy()

        # Apply lighting enhancements to mesh materials
        if hasattr(enhanced_mesh.visual, 'material') and enhanced_mesh.visual.material:
            material = enhanced_mesh.visual.material

            # Update material properties based on optimized lighting
            if hasattr(material, 'baseColorFactor'):
                if material.baseColorFactor is not None:
                    base_color = np.array(material.baseColorFactor, dtype=np.float32)
                    # Apply diffuse color enhancement
                    if len(base_color) >= 3:
                        base_color[:3] = np.clip(base_color[:3] * diffuse_color, 0.0, 1.0)
                        material.baseColorFactor = base_color

            # Update emissive factor for ambient lighting
            if hasattr(material, 'emissiveFactor'):
                material.emissiveFactor = ambient_color.tolist()

            # Adjust roughness for better light interaction
            if hasattr(material, 'roughnessFactor'):
                # Slightly reduce roughness for better light reflection
                current_roughness = material.roughnessFactor or 0.8
                material.roughnessFactor = max(0.1, current_roughness * 0.9)

        # Apply lighting to vertex colors if available
        if hasattr(enhanced_mesh.visual, 'vertex_colors') and enhanced_mesh.visual.vertex_colors is not None:
            vertex_colors = enhanced_mesh.visual.vertex_colors.copy().astype(np.float32)

            # Normalize to 0-1 range if needed
            if vertex_colors.max() > 1.0:
                vertex_colors = vertex_colors / 255.0

            # Apply ambient and diffuse lighting
            vertex_colors[:, :3] = np.clip(
                vertex_colors[:, :3] * diffuse_color + ambient_color * 0.3,
                0.0, 1.0
            )

            # Convert back to original format
            if enhanced_mesh.visual.vertex_colors.max() > 1.0:
                vertex_colors = (vertex_colors * 255).astype(np.uint8)

            enhanced_mesh.visual.vertex_colors = vertex_colors

        print("✓ Optimized lighting applied to mesh")
        return enhanced_mesh


def load_pytorch3d_config(preset_name: str = "balanced") -> Dict:
    """
    Load PyTorch3D lighting optimization configuration.

    Args:
        preset_name: Name of the preset to load

    Returns:
        Dictionary with optimization settings
    """
    try:
        config_path = Path(__file__).parent / "pytorch3d_lighting_config.json"

        if not config_path.exists():
            print(f"Warning: PyTorch3D config file not found, using defaults")
            return PyTorch3DLightingOptimizer().default_settings

        with open(config_path, 'r') as f:
            config = json.load(f)

        presets = config.get('pytorch3d_optimization_settings', {}).get('presets', {})

        if preset_name in presets:
            return presets[preset_name]
        else:
            available_presets = list(presets.keys())
            print(f"Warning: Preset '{preset_name}' not found. Available presets: {available_presets}")
            return presets.get('balanced', PyTorch3DLightingOptimizer().default_settings)

    except Exception as e:
        print(f"Error loading PyTorch3D config: {e}")
        return PyTorch3DLightingOptimizer().default_settings


def optimize_model_lighting_pytorch3d(mesh: trimesh.Trimesh, settings: Optional[Dict] = None,
                                     preset: str = None) -> trimesh.Trimesh:
    """
    Convenience function to optimize lighting using PyTorch3D.

    Args:
        mesh: Input trimesh object
        settings: Optimization settings dictionary (overrides preset)
        preset: Preset name to use

    Returns:
        Enhanced trimesh object
    """
    if not PYTORCH3D_AVAILABLE:
        print("PyTorch3D not available, falling back to traditional lighting enhancement")
        from lighting_enhancement import enhance_model_lighting
        return enhance_model_lighting(mesh, settings, preset)

    optimizer = PyTorch3DLightingOptimizer()

    # Load preset if specified and no custom settings provided
    if preset and settings is None:
        settings = load_pytorch3d_config(preset)
    elif settings is None:
        settings = load_pytorch3d_config("balanced")

    return optimizer.optimize_lighting(mesh, settings)
