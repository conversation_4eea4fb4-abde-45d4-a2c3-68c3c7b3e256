<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hunyuan3D API Documentation</title>
    <style>
        :root {
            --primary-color: #c4b5fd;
            --text-color: #d1d5db;
            --bg-color: #0f172a;
            --code-bg: #1e293b;
            --border-color: #334155;
            --endpoint-bg: #1e293b;
            --nav-bg: #1e293b;
            --description-color: #94a3b8;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background: var(--bg-color);
            padding: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 2rem;
            color: var(--primary-color);
        }

        h2 {
            font-size: 1.8rem;
            margin: 2rem 0 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--border-color);
            color: var(--primary-color);
        }

        h3 {
            color: var(--text-color);
            margin: 1rem 0;
        }

        .endpoint {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--endpoint-bg);
        }

        .endpoint:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        }

        .workflow-option {
            margin: 1rem 0;
            padding: 1rem;
            background: var(--code-bg);
            border-radius: 4px;
            border: 1px solid var(--border-color);
        }

        .workflow-option h4 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .workflow-option ol {
            margin-left: 1.5rem;
            color: var(--text-color);
        }

        .workflow-option li {
            margin: 0.5rem 0;
        }

        .method {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 4px;
            font-weight: bold;
            margin-right: 1rem;
        }

        .get {
            background: #10B981;
            color: white;
        }

        .post {
            background: #3B82F6;
            color: white;
        }

        .endpoint-path {
            font-family: monospace;
            font-size: 1.1rem;
            color: var(--text-color);
        }

        .description {
            margin: 1rem 0;
            color: var(--description-color);
        }

        .parameters {
            margin: 1rem 0;
        }

        code {
            background: var(--code-bg);
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            font-family: monospace;
            color: var(--text-color);
        }

        pre {
            background: var(--code-bg);
            padding: 1rem;
            border-radius: 4px;
            overflow-x: auto;
            border: 1px solid var(--border-color);
        }

        pre code {
            background: none;
            padding: 0;
            color: var(--text-color);
        }

        .parameter {
            margin: 0.5rem 0;
            padding: 0.5rem;
            background: var(--code-bg);
            border-radius: 4px;
            border: 1px solid var(--border-color);
        }

        .parameter-name {
            font-weight: bold;
            color: var(--primary-color);
        }

        .top-nav {
            position: sticky;
            top: 0;
            background: var(--nav-bg);
            padding: 1rem 0;
            margin-bottom: 2rem;
            border-bottom: 1px solid var(--border-color);
            z-index: 1000;
        }

        .nav-list {
            list-style: none;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .nav-list a {
            color: var(--text-color);
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .nav-list a:hover {
            background: var(--code-bg);
            color: var(--primary-color);
        }

        .response {
            margin-top: 1rem;
            color: var(--text-color);
        }

        .response strong {
            color: var(--primary-color);
            display: block;
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
<div class="container">
        <h1>Hunyuan3D Generation API Documentation</h1>
        
        <div class="endpoint" style="margin-top: -1rem; margin-bottom: 2rem;">
            <p style="color: var(--description-color);">
                <strong style="color: var(--primary-color);">Last Updated:</strong> March 31, 2025
            </p>
        </div>

        <section id="workflow">
            <h2>Generation Workflows</h2>

            <div class="endpoint">
                <h3>Single Image Workflow</h3>
                <p class="description">Direct generation from a single image:</p>
                
                <div class="workflow-option">
                    <h4>Using the API directly</h4>
                    <ol>
                        <li>POST to <code>/generate_no_preview</code> with your base64 image and parameters</li>
                        <li>Poll <code>/status</code> until completion</li>
                        <li>GET <code>/download/model</code> to obtain the final GLB</li>
                    </ol>
                </div>

                <div class="workflow-option">
                    <h4>Using the simplified UI endpoint</h4>
                    <ol>
                        <li>POST to <code>/generate</code> with your base64 image and simplified parameters</li>
                        <li>Poll <code>/status</code> until completion</li>
                        <li>GET <code>/download/model</code> to obtain the final GLB</li>
                    </ol>
                </div>
            </div>

            <div class="endpoint">
                <h3>Multi-Image Workflow</h3>
                <p class="description">Direct generation from multiple images:</p>
                
                <div class="workflow-option">
                    <h4>Using multiple images (recommended for multiview models)</h4>
                    <ol>
                        <li>POST to <code>/generate_multi_no_preview</code> with your base64 images and parameters</li>
                        <li>For best results with mv models, provide front/side/back views when possible</li>
                        <li>The system automatically uses front view if only one image is provided</li>
                        <li>Poll <code>/status</code> until completion</li>
                        <li>GET <code>/download/model</code> to obtain the final GLB</li>
                    </ol>
                </div>
            </div>
        </section>
        
        <nav class="top-nav">
            <ul class="nav-list">
                <li><a href="#workflow">Workflows</a></li>
                <li><a href="#examples">Code Examples</a></li>
                <li><a href="#status-endpoints">Status Endpoints</a></li>
                <li><a href="#generation-endpoints">Generation Endpoints</a></li>
                <li><a href="#download-endpoints">Download Endpoints</a></li>
            </ul>
        </nav>
        
        <section id="examples">
            <h2>Code Examples</h2>
            
            <div class="endpoint">
                <h3>Single Image Generation (Python)</h3>
                <pre><code>import requests
import base64
import time

# API endpoint
BASE_URL = "http://127.0.0.1:7960"

def generate_from_image(image_path: str):
    """Generate 3D model from a single image.
    
    Args:
        image_path: Path to the image file
    """
    try:
        # Convert image to base64
        with open(image_path, "rb") as img_file:
            image_base64 = base64.b64encode(img_file.read()).decode('utf-8')
        
        # Set generation parameters
        params = {
            'image_base64': image_base64,
            'seed': 42,
            'guidance_scale': 5.0,
            'num_inference_steps': 20,
            'octree_resolution': 256,
            'num_chunks': 80,
            'mesh_simplify_ratio': 0.1,
            'apply_texture': True,
            'output_format': 'glb'
        }
        
        # Start generation
        print("Starting generation...")
        response = requests.post(f"{BASE_URL}/generate_no_preview", data=params)
        response.raise_for_status()
        
        # Poll status until complete
        while True:
            status = requests.get(f"{BASE_URL}/status").json()
            print(f"Progress: {status['progress']}%")
            
            if status['status'] == 'COMPLETE':
                break
            elif status['status'] == 'FAILED':
                raise Exception(f"Generation failed: {status['message']}")
            
            time.sleep(1)
        
        # Download the model
        print("Downloading model...")
        response = requests.get(f"{BASE_URL}/download/model")
        response.raise_for_status()
        
        # Save the model
        with open("output_model.glb", "wb") as f:
            f.write(response.content)
        
        print("Model saved as output_model.glb")
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return None</code></pre>
            </div>

            <div class="endpoint">
                <h3>Multi-Image Generation (Python)</h3>
                <pre><code>def generate_from_multiple_images(image_paths: list):
    """Generate 3D model from multiple images using multi-view model.
    
    Args:
        image_paths: List of paths to image files
    """
    try:
        # Convert images to base64
        image_base64_list = []
        for path in image_paths:
            with open(path, "rb") as img_file:
                image_base64_list.append(base64.b64encode(img_file.read()).decode('utf-8'))
        
        # Set generation parameters
        params = {
            'image_list_base64': image_base64_list,
            'seed': 42,
            'guidance_scale': 5.0,
            'num_inference_steps': 20,
            'octree_resolution': 256,
            'num_chunks': 80,
            'mesh_simplify_ratio': 0.1,
            'apply_texture': True,
            'output_format': 'glb'
        }
        
        # Start generation
        print("Starting multi-view generation...")
        response = requests.post(f"{BASE_URL}/generate_multi_no_preview", data=params)
        response.raise_for_status()
        
        # Poll status until complete
        while True:
            status = requests.get(f"{BASE_URL}/status").json()
            print(f"Progress: {status['progress']}%")
            
            if status['status'] == 'COMPLETE':
                break
            elif status['status'] == 'FAILED':
                raise Exception(f"Generation failed: {status['message']}")
            
            time.sleep(1)
        
        # Download final model
        print("Downloading model...")
        model = requests.get(f"{BASE_URL}/download/model")
        model.raise_for_status()
        
        # Save the model
        with open("output_model.glb", "wb") as f:
            f.write(model.content)
        
        print("Model saved as output_model.glb")
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return None</code></pre>
            </div>
        </section>

        <section id="status-endpoints">
            <h2>Status Endpoints</h2>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="endpoint-path">/ping</span>
                <p class="description">Check server status and availability.</p>
                <div class="response">
                    <strong>Response:</strong>
                    <pre><code>{
    "status": "running",
    "message": "API is operational",
    "busy": boolean
}</code></pre>
                </div>
            </div>

            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="endpoint-path">/status</span>
                <p class="description">Get the status of the current or last generation.</p>
                <div class="response">
                    <strong>Response:</strong>
                    <pre><code>{
    "status": string,  // "PROCESSING", "COMPLETE", "FAILED"
    "progress": number,  // 0-100
    "message": string,
    "busy": boolean
}</code></pre>
                </div>
            </div>
        </section>

        <section id="generation-endpoints">
            <h2>Generation Endpoints</h2>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="endpoint-path">/generate</span>
                <p class="description">Simplified endpoint for UI integration. Generate a 3D model from a single or multiple images.</p>
                <div class="parameters">
                    <strong>Parameters:</strong>
                    <div class="parameter">
                        <span class="parameter-name">single_multi_img_input</span>: list[string] (required)
                        <p>List of base64-encoded images</p>
                    </div>
                    <div class="parameter">
                        <span class="parameter-name">seed</span>: int (default: 1234)
                        <p>Random seed for generation</p>
                    </div>
                    <div class="parameter">
                        <span class="parameter-name">guidance_scale</span>: float (default: 5.0)
                        <p>Guidance scale for generation</p>
                    </div>
                    <div class="parameter">
                        <span class="parameter-name">num_inference_steps</span>: int (default: 20)
                        <p>Number of inference steps</p>
                    </div>
                    <div class="parameter">
                        <span class="parameter-name">octree_resolution</span>: int (default: 256)
                        <p>Resolution of the octree for mesh extraction</p>
                    </div>
                    <div class="parameter">
                        <span class="parameter-name">num_chunks</span>: int (default: 80)
                        <p>Number of chunks for mesh extraction (internally multiplied by 1000)</p>
                    </div>
                    <div class="parameter">
                        <span class="parameter-name">mesh_simplify</span>: float (default: 0.1)
                        <p>Ratio for mesh simplification (0-1 or 0-100)</p>
                    </div>
                    <div class="parameter">
                        <span class="parameter-name">apply_texture</span>: boolean (default: false)
                        <p>Whether to apply texture to the model</p>
                    </div>
                </div>
            </div>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="endpoint-path">/generate_no_preview</span>
                <p class="description">Generate a 3D model from a single image. Download GLB when complete.</p>
                <div class="parameters">
                    <strong>Parameters:</strong>
                    <div class="parameter">
                        <span class="parameter-name">file</span>: file or <span class="parameter-name">image_base64</span>: string
                        <p>Image file upload or base64-encoded image data</p>
                    </div>
                    <div class="parameter">
                        <span class="parameter-name">seed</span>: int (default: 1234)
                        <p>Random seed for generation</p>
                    </div>
                    <div class="parameter">
                        <span class="parameter-name">guidance_scale</span>: float (default: 5.0)
                        <p>Guidance scale for generation</p>
                    </div>
                    <div class="parameter">
                        <span class="parameter-name">num_inference_steps</span>: int (default: 20)
                        <p>Number of inference steps</p>
                    </div>
                    <div class="parameter">
                        <span class="parameter-name">octree_resolution</span>: int (default: 256)
                        <p>Resolution of the octree for mesh extraction</p>
                    </div>
                    <div class="parameter">
                        <span class="parameter-name">num_chunks</span>: int (default: 80)
                        <p>Number of chunks for mesh extraction (internally multiplied by 1000)</p>
                    </div>
                    <div class="parameter">
                        <span class="parameter-name">mesh_simplify_ratio</span>: float (default: 0.1)
                        <p>Ratio for mesh simplification (0-1 or 0-100)</p>
                    </div>
                    <div class="parameter">
                        <span class="parameter-name">apply_texture</span>: boolean (default: false)
                        <p>Whether to apply texture to the model</p>
                    </div>
                    <div class="parameter">
                        <span class="parameter-name">output_format</span>: string (default: "glb")
                        <p>Output format (glb or obj)</p>
                    </div>
                </div>
            </div>

            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="endpoint-path">/generate_multi_no_preview</span>
                <p class="description">Generate a 3D model using multiple images. Best used with multiview models.</p>
                <div class="parameters">
                    <strong>Parameters:</strong>
                    <div class="parameter">
                        <span class="parameter-name">file_list</span>: list[file] or <span class="parameter-name">image_list_base64</span>: list[string]
                        <p>List of image file uploads or list of base64-encoded images</p>
                    </div>
                    <!-- Other parameters are the same as above -->
                </div>
            </div>
        </section>

        <section id="control-endpoints">
            <h2>Control Endpoints</h2>
            
            <div class="endpoint">
                <span class="method post">POST</span>
                <span class="endpoint-path">/interrupt</span>
                <p class="description">Cancel the current generation process.</p>
                <div class="response">
                    <strong>Response:</strong>
                    <pre><code>{
    "status": "interrupt_requested" // or "no_generation_in_progress"
}</code></pre>
                </div>
            </div>
        </section>

        <section id="info-endpoints">
            <h2>Information Endpoints</h2>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="endpoint-path">/info/supported_operations</span>
                <p class="description">Get a list of supported operations.</p>
                <div class="response">
                    <strong>Response:</strong>
                    <pre><code>["make_meshes_and_tex"]</code></pre>
                </div>
            </div>

            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="endpoint-path">/download/spz-ui-layout/generation-3d-panel</span>
                <p class="description">Get the UI layout for StableProjectorz integration.</p>
            </div>
        </section>

        <section id="download-endpoints">
            <h2>Download Endpoints</h2>
            
            <div class="endpoint">
                <span class="method get">GET</span>
                <span class="endpoint-path">/download/model</span>
                <p class="description">Download the final 3D model (GLB format, with texture if requested).</p>
            </div>
        </section>

        <section id="parameter-details">
            <h2>Parameter Details</h2>
            <div class="endpoint">
                <div class="parameters">
                    <div class="parameter">
                        <span class="parameter-name">seed</span>: int (default: 1234)
                        <p>Random seed for reproducible generation</p>
                    </div>
                    <div class="parameter">
                        <span class="parameter-name">guidance_scale</span>: float (0-10, default: 5.0)
                        <p>Controls how closely the model follows the input image. Higher values produce more faithful results but may be less creative.</p>
                    </div>
                    <div class="parameter">
                        <span class="parameter-name">num_inference_steps</span>: int (1-50, default: 20)
                        <p>Number of denoising steps. More steps generally produce better quality but take longer.</p>
                    </div>
                    <div class="parameter">
                        <span class="parameter-name">octree_resolution</span>: int (128-512, default: 256)
                        <p>Resolution of the octree for mesh extraction. Higher values produce more detailed meshes but require more VRAM.</p>
                    </div>
                    <div class="parameter">
                        <span class="parameter-name">num_chunks</span>: int (1-200, default: 80)
                        <p>Number of chunks for mesh extraction (internally multiplied by 1000). Higher values use less VRAM but take longer.</p>
                    </div>
                    <div class="parameter">
                        <span class="parameter-name">mesh_simplify_ratio</span>: float (0-1 or 0-100, default: 0.1)
                        <p>Ratio for mesh simplification. Lower values create simpler meshes with fewer polygons.</p>
                    </div>
                    <div class="parameter">
                        <span class="parameter-name">apply_texture</span>: boolean (default: false)
                        <p>Whether to apply texture to the model. When true, texture generation will be performed after mesh creation.</p>
                    </div>
                    <div class="parameter">
                        <span class="parameter-name">output_format</span>: string (default: "glb")
                        <p>Output format. Currently supports "glb" or "obj".</p>
                    </div>
                </div>
            </div>
        </section>
    </div>
</body>
</html>