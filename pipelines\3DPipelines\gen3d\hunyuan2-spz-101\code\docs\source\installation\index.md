# Installation

```{toctree}
:hidden:
```

∇-Prox works with PyTorch. To install Pytorch, please follow the [PyTorch installation instructions](https://pytorch.org/get-started/locally/).


**Install with pip**

```bash
pip install dprox
```

**Install from source**

```bash
pip install git+https://github.com/princeton-computational-imaging/Delta-Prox.git
```

**Editable installation**

You will need an editable install if you would like to:

1. Use the main version of the source code.
2. Need to test changes in the code.

To do so, clone the repository and install 🎉 Delta Prox with the following commands:

```
git clone git+https://github.com/princeton-computational-imaging/Delta-Prox.git
cd DeltaProx
pip install -e .
```

```{caution}
Note that you must keep the DeltaProx folder for editable installation if you want to keep using the library.
```

