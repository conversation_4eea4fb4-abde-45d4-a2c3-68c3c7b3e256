import os
import sys
import imageio
from PIL import Image
import numpy as np
import torch
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime

# Set environment variables for Trellis
os.environ['SPCONV_ALGO'] = 'native'  # Use 'native' for single runs

def setup_trellis(log_level: str = 'INFO'):
    """
    Set up the Trellis pipeline for 2D to 3D conversion
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
    
    Returns:
        Tuple containing (pipeline, render_utils, postprocessing_utils)
    """
    # Set up logging
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('trellis_integration.log')
        ]
    )
    logger = logging.getLogger(__name__)

    try:
        # Use the current directory as Trellis path
        trellis_path = Path(__file__).parent.resolve()

        # Make sure the path exists
        if not trellis_path.exists():
            logger.error(f"Trellis path does not exist: {trellis_path}")
            return None, None, None

        # Add the current directory to the path
        if str(trellis_path) not in sys.path:
            sys.path.insert(0, str(trellis_path))

        logger.info(f"Setting up Trellis from: {trellis_path}")
        logger.debug(f"Python path: {sys.path}")

        # Check for CUDA availability
        cuda_available = torch.cuda.is_available()
        if not cuda_available:
            print("Warning: CUDA is not available. Trellis requires a CUDA-capable GPU for optimal performance.")
            print("Falling back to CPU mode, but this will be extremely slow.")

        # Print detailed information about the environment
        print("\n=== System Information ===")
        print(f"Python version: {sys.version}")
        print(f"Torch version: {torch.__version__}")
        print(f"CUDA available: {cuda_available}")
        if cuda_available:
            print(f"CUDA device: {torch.cuda.get_device_name(0)}")
            print(f"CUDA memory: {torch.cuda.get_device_properties(0).total_memory / (1024**3):.2f} GB")
        print("\n=== Module Availability ===")
        try:
            import open3d
            print("open3d is available")
        except ImportError:
            print("Warning: open3d is not available")

        try:
            import utils3d
            print("utils3d is available")
        except ImportError:
            print("Warning: utils3d is not available")

        try:
            import flexicubes
            print("flexicubes is available")
        except ImportError:
            print("Warning: flexicubes is not available")

        try:
            import trellis
            print("Trellis package is available")
        except ImportError:
            print("Warning: trellis package is not available")

        print("\n=== Directory Structure ===")
        print(f"Current directory: {os.getcwd()}")
        print(f"Trellis path: {trellis_path}")
        print("Directory contents:")
        for item in os.listdir(trellis_path):
            print(f"  {item}")

        # Import Trellis modules
        try:
            print("\n=== Importing Trellis Modules ===")
            from trellis.pipelines import TrellisImageTo3DPipeline
            from trellis.utils import render_utils, postprocessing_utils
            print("Successfully imported Trellis modules")
        except ImportError as e:
            print(f"Error importing Trellis modules: {e}")
            import traceback
            print("Detailed error:")
            traceback.print_exc()
            return None, None, None

        # Load the pipeline
        print("\n=== Loading Trellis Model ===")
        print("Loading Trellis model (this may take a while)...")
        try:
            # Create the pipeline manually since we can't use from_pretrained
            from trellis.models import ImageTo3DModel
            from trellis.pipelines.configuration import TrellisImageTo3DPipelineConfig
            
            # Initialize the model configuration
            config = TrellisImageTo3DPipelineConfig()
            
            # Create the model
            model = ImageTo3DModel(config)
            
            # Create the pipeline
            pipeline = TrellisImageTo3DPipeline(model=model, config=config)
            
            if cuda_available:
                pipeline.cuda()
            
            print("Successfully loaded and initialized the Trellis pipeline")
            return pipeline, render_utils, postprocessing_utils
        except Exception as e:
            print(f"Error loading Trellis model: {e}")
            import traceback
            print("Detailed error:")
            traceback.print_exc()
            return None, None, None

    except Exception as e:
        print(f"Error setting up Trellis: {e}")
        import traceback
        print("Detailed error:")
        traceback.print_exc()
        return None, None, None

        # Import types module for creating mock modules
        import types

        # Check for required dependencies
        try:
            import open3d
            print("open3d is available")
        except ImportError:
            print("Warning: open3d is not available")
            print("Creating a workaround for open3d...")

            # Create a mock open3d module
            open3d = types.ModuleType('open3d')
            open3d.utility = types.ModuleType('utility')
            open3d.utility.set_verbosity_level = lambda level: None
            open3d.geometry = types.ModuleType('geometry')

            class MockTriangleMesh:
                @staticmethod
                def create_from_tensors(vertices, triangles):
                    return MockTriangleMesh()

            open3d.geometry.TriangleMesh = MockTriangleMesh
            sys.modules['open3d'] = open3d

        # Check for utils3d (required by Trellis)
        try:
            import utils3d
        except ImportError:
            print("Creating mock utils3d module...")
            utils3d = types.ModuleType('utils3d')
            utils3d.torch = types.ModuleType('torch')
            utils3d.torch.rasterize_triangle_faces = lambda *args, **kwargs: {'mask': torch.zeros(1, 1, 1)}

            # Add the perspective_from_fov_xy function to utils3d.torch
            def perspective_from_fov_xy(fov_x, fov_y, znear, zfar):
                """
                Create a perspective projection matrix from field of view angles.

                Args:
                    fov_x: Field of view angle in the x direction, in radians.
                    fov_y: Field of view angle in the y direction, in radians.
                    znear: Distance to the near clipping plane.
                    zfar: Distance to the far clipping plane.

                Returns:
                    A 4x4 perspective projection matrix.
                """
                tan_x = torch.tan(fov_x / 2)
                tan_y = torch.tan(fov_y / 2)

                # Create the perspective matrix
                result = torch.zeros((4, 4), dtype=torch.float32)
                result[0, 0] = 1.0 / tan_x
                result[1, 1] = 1.0 / tan_y
                result[2, 2] = -(zfar + znear) / (zfar - znear)
                result[2, 3] = -2.0 * zfar * znear / (zfar - znear)
                result[3, 2] = -1.0

                return result

            utils3d.torch.perspective_from_fov_xy = perspective_from_fov_xy
            sys.modules['utils3d'] = utils3d
            print("Created mock utils3d module with perspective_from_fov_xy function")

        # Check for flexicubes (required by Trellis)
        try:
            import flexicubes
        except ImportError:
            print("Creating mock flexicubes module...")
            flexicubes = types.ModuleType('flexicubes')

            def extract_mesh(*args, **kwargs):
                # Create dummy vertices, faces, and L_dev
                vertices = torch.zeros((100, 3))
                faces = torch.zeros((100, 3), dtype=torch.int64)
                L_dev = torch.zeros(1)
                return vertices, faces, L_dev

            flexicubes.extract_mesh = extract_mesh
            sys.modules['flexicubes'] = flexicubes
            print("Created mock flexicubes module with extract_mesh function")

        # Import Trellis modules
        try:
            from trellis.pipelines import TrellisImageTo3DPipeline
            from trellis.utils import render_utils, postprocessing_utils
            print("Successfully imported Trellis modules")
        except ImportError as e:
            print(f"Error importing Trellis modules: {e}")
            return None, None, None

        # Load the pipeline
        print("Loading Trellis model (this may take a while)...")
        try:
            pipeline = TrellisImageTo3DPipeline.from_pretrained("JeffreyXiang/TRELLIS-image-large")
            if cuda_available:
                pipeline.cuda()
            print("Successfully loaded and initialized the Trellis pipeline")
            return pipeline, render_utils, postprocessing_utils
        except Exception as e:
            print(f"Error loading Trellis model: {e}")
            import traceback
            traceback.print_exc()
            return None, None, None

    except Exception as e:
        print(f"Error setting up Trellis: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None

def generate_3d_model(
    pipeline, render_utils, postprocessing_utils, 
    image_path: str, 
    output_dir: str = "output",
    settings: Optional[Dict] = None,
    progress_callback: Optional[callable] = None
) -> Optional[Dict[str, str]]:
    """
    Generate a 3D model from a 2D image using Trellis

    Args:
        pipeline: The Trellis pipeline
        render_utils: Trellis render utilities
        postprocessing_utils: Trellis postprocessing utilities
        image_path: Path to the input image
        output_dir: Directory to save the output files
        settings: Dictionary with generation settings (optional)
        progress_callback: Optional callback function for progress updates

    Returns:
        Dictionary containing paths to the generated files
    """
    # Check if we have a valid pipeline
    if pipeline is None or render_utils is None or postprocessing_utils is None:
        print("Error: Trellis pipeline or utilities are not available")
        return None

    # Default settings if none provided
    if settings is None:
        settings = {
            "seed": 42,
            "ss_guidance_strength": 7.5,
            "ss_sampling_steps": 25,
            "slat_guidance_strength": 3.0,
            "slat_sampling_steps": 25
        }

    # Extract settings
    seed = settings.get("seed", 42)
    ss_guidance_strength = settings.get("ss_guidance_strength", 7.5)
    ss_sampling_steps = settings.get("ss_sampling_steps", 25)
    slat_guidance_strength = settings.get("slat_guidance_strength", 3.0)
    slat_sampling_steps = settings.get("slat_sampling_steps", 25)

    print(f"Using settings: seed={seed}, ss_steps={ss_sampling_steps}, ss_strength={ss_guidance_strength}, "
          f"slat_steps={slat_sampling_steps}, slat_strength={slat_guidance_strength}")

    try:
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Normalize the image path
        image_path = os.path.normpath(os.path.abspath(image_path))

        # Check if the image exists
        if not os.path.exists(image_path):
            print(f"Error: Image file not found: {image_path}")
            return None

        # Load the image
        try:
            image = Image.open(image_path)
            print(f"Loaded image: {image_path} ({image.width}x{image.height}, {image.mode})")
        except Exception as e:
            print(f"Error loading image: {e}")
            return None

        # Generate base filename from input image
        base_filename = os.path.splitext(os.path.basename(image_path))[0]

        # Patch the FlexiCubes class to handle the missing voxelgrid_vertices parameter
        try:
            import types
            from trellis.representations.mesh.flexicubes.flexicubes import FlexiCubes

            # Store the original __call__ method
            original_call = FlexiCubes.__call__

            # Define a new __call__ method that handles additional parameters
            def patched_call(self, *args, **kwargs):
                # Just pass through all arguments to the original method
                # and ignore any additional parameters
                if len(args) >= 4:
                    # We have enough positional arguments, just pass them through
                    result = original_call(self, *args[:10], **{k: v for k, v in kwargs.items()
                                                            if k in ['beta_fx12', 'alpha_fx8', 'gamma_f',
                                                                    'training', 'output_tetmesh', 'grad_func']})
                else:
                    # Not enough positional arguments, try to extract them from the keyword arguments
                    print(f"Warning: Not enough positional arguments for FlexiCubes.__call__: {len(args)}")

                    # Extract the required parameters from the keyword arguments
                    if 'voxelgrid_vertices' in kwargs and 'scalar_field' in kwargs and 'cube_idx' in kwargs and 'resolution' in kwargs:
                        x_nx3 = kwargs['voxelgrid_vertices']
                        s_n = kwargs['scalar_field']
                        cube_fx8 = kwargs['cube_idx']
                        res = kwargs['resolution']

                        # Extract optional parameters
                        beta_fx12 = kwargs.get('beta', None)
                        alpha_fx8 = kwargs.get('alpha', None)
                        gamma_f = kwargs.get('gamma_f', None)
                        training = kwargs.get('training', False)
                        output_tetmesh = kwargs.get('output_tetmesh', False)
                        grad_func = kwargs.get('grad_func', None)

                        # Call the original method with the extracted parameters
                        result = original_call(self, x_nx3, s_n, cube_fx8, res, beta_fx12, alpha_fx8,
                                            gamma_f, training, output_tetmesh, grad_func)
                    else:
                        print(f"Error: Missing required parameters for FlexiCubes.__call__")
                        print(f"Arguments: {args}")
                        print(f"Keyword arguments: {kwargs}")

                        # Just use the original arguments and hope for the best
                        result = original_call(self, *args, **{k: v for k, v in kwargs.items()
                                                            if k in ['beta_fx12', 'alpha_fx8', 'gamma_f',
                                                                    'training', 'output_tetmesh', 'grad_func']})

                # Check if we need to add a fourth return value for colors
                if isinstance(result, tuple) and len(result) == 3:
                    vertices, faces, L_dev = result
                    # Create a dummy colors tensor with the same shape as vertices
                    colors = torch.zeros_like(vertices)
                    return vertices, faces, L_dev, colors
                else:
                    return result

            # Replace the __call__ method with our patched version
            FlexiCubes.__call__ = patched_call
            print("Successfully patched FlexiCubes.__call__ method")
        except Exception as e:
            print(f"Warning: Failed to patch FlexiCubes.__call__ method: {e}")

        # Run the pipeline with progress tracking
        logger.info(f"Running Trellis pipeline with image: {image_path}")
        try:
            if progress_callback:
                progress_callback(0, "Starting pipeline execution")
            
            outputs = pipeline.run(
                image,
                seed=settings["seed"],
                formats=settings["output_formats"],
                sparse_structure_sampler_params={
                    "steps": settings["ss_sampling_steps"],
                    "cfg_strength": settings["ss_guidance_strength"],
                },
                slat_sampler_params={
                    "steps": settings["slat_sampling_steps"],
                    "cfg_strength": settings["slat_guidance_strength"],
                },
            )
            
            if progress_callback:
                progress_callback(33, "Pipeline execution completed")
            print("Pipeline execution completed successfully")
        except Exception as e:
            print(f"Error running Trellis pipeline: {e}")
            import traceback
            traceback.print_exc()
            return None

        # Define output paths
        output_paths = {
            'gaussian_video': os.path.join(output_dir, f"{base_filename}_gaussian.mp4"),
            'radiance_field_video': os.path.join(output_dir, f"{base_filename}_radiance.mp4"),
            'mesh_video': os.path.join(output_dir, f"{base_filename}_mesh.mp4"),
            'glb': os.path.join(output_dir, f"{base_filename}.glb"),
            'obj': os.path.join(output_dir, f"{base_filename}.obj"),
            'ply': os.path.join(output_dir, f"{base_filename}.ply")
        }

        # Render and save videos with progress tracking
        try:
            if progress_callback:
                progress_callback(33, "Rendering Gaussian video")
            
            video = render_utils.render_video(outputs['gaussian'][0])['color']
            imageio.mimsave(output_paths['gaussian_video'], video, fps=settings["video_fps"])
            logger.info(f"Saved Gaussian video to: {output_paths['gaussian_video']}")
            
            if progress_callback:
                progress_callback(66, "Gaussian video rendered")
        except Exception as e:
            print(f"Error rendering gaussian video: {e}")
            output_paths['gaussian_video'] = None

        try:
            if progress_callback:
                progress_callback(33, "Rendering radiance field video")
            
            video = render_utils.render_video(outputs['radiance_field'][0])['color']
            imageio.mimsave(output_paths['radiance_field_video'], video, fps=settings["video_fps"])
            logger.info(f"Saved radiance field video to: {output_paths['radiance_field_video']}")
            
            if progress_callback:
                progress_callback(100, "Generation complete")
        except Exception as e:
            print(f"Error rendering radiance field video: {e}")
            output_paths['radiance_field_video'] = None

        try:
            if progress_callback:
                progress_callback(66, "Rendering mesh video")
            
            # Check if we're likely to encounter the cl.exe error
            cl_exe_exists = False
            try:
                import subprocess
                result = subprocess.run(['where', 'cl.exe'], capture_output=True, text=True)
                cl_exe_exists = result.returncode == 0
            except:
                logger.warning("Failed to check for Visual Studio C++ compiler")

            if cl_exe_exists:
                # Normal path - Visual Studio C++ compiler is available
                video = render_utils.render_video(outputs['mesh'][0])['normal']
                imageio.mimsave(output_paths['mesh_video'], video, fps=settings["video_fps"])
                logger.info(f"Saved mesh video to: {output_paths['mesh_video']}")
            else:
                # Fallback path - create a simple video from the gaussian video
                logger.warning("Visual Studio C++ compiler (cl.exe) not found. Using fallback for mesh video...")

                # Check if we have the gaussian video
                if 'gaussian_video' in output_paths and output_paths['gaussian_video'] is not None:
                    # Create a grayscale version of the gaussian video
                    try:
                        import cv2
                        import numpy as np

                        # Read the gaussian video
                        cap = cv2.VideoCapture(output_paths['gaussian_video'])
                        frames = []
                        while True:
                            ret, frame = cap.read()
                            if not ret:
                                break
                            # Convert to grayscale and back to RGB for consistent format
                            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                            gray_rgb = cv2.cvtColor(gray, cv2.COLOR_GRAY2RGB)
                            frames.append(gray_rgb)
                        cap.release()

                        # Save the mesh video
                        imageio.mimsave(output_paths['mesh_video'], frames, fps=30)
                        print(f"Saved fallback mesh video to: {output_paths['mesh_video']}")
                    except Exception as e:
                        print(f"Error creating fallback mesh video: {e}")
                        output_paths['mesh_video'] = None
                else:
                    print("Cannot create fallback mesh video: gaussian video not available")
                    output_paths['mesh_video'] = None
        except Exception as e:
            print(f"Error rendering mesh video: {e}")
            import traceback
            traceback.print_exc()
            output_paths['mesh_video'] = None

        # Generate and save GLB file
        try:
            print("Generating GLB file...")

            # Check if utils3d.torch has the required perspective function
            import sys
            if 'utils3d' in sys.modules and hasattr(sys.modules['utils3d'], 'torch'):
                utils3d_torch = sys.modules['utils3d'].torch

                if not hasattr(utils3d_torch, 'perspective_from_fov_xy'):
                    # Add the missing function to utils3d.torch
                    def perspective_from_fov_xy(fov_x, fov_y, znear, zfar):
                        """
                        Create a perspective projection matrix from field of view angles.

                        Args:
                            fov_x: Field of view angle in the x direction, in radians.
                            fov_y: Field of view angle in the y direction, in radians.
                            znear: Distance to the near clipping plane.
                            zfar: Distance to the far clipping plane.

                        Returns:
                            A 4x4 perspective projection matrix.
                        """
                        tan_x = torch.tan(fov_x / 2)
                        tan_y = torch.tan(fov_y / 2)

                        # Create the perspective matrix
                        result = torch.zeros((4, 4), dtype=torch.float32)
                        result[0, 0] = 1.0 / tan_x
                        result[1, 1] = 1.0 / tan_y
                        result[2, 2] = -(zfar + znear) / (zfar - znear)
                        result[2, 3] = -2.0 * zfar * znear / (zfar - znear)
                        result[3, 2] = -1.0

                        return result

                    # Add the function to utils3d.torch
                    utils3d_torch.perspective_from_fov_xy = perspective_from_fov_xy
                    print("Added missing perspective_from_fov_xy function to utils3d.torch")
            else:
                print("Warning: utils3d.torch module not found, GLB generation may fail")

            # Try to generate the GLB file
            try:
                glb = postprocessing_utils.to_glb(
                    outputs['gaussian'][0],
                    outputs['mesh'][0],
                    simplify=0.95,
                    texture_size=1024,
                )
                glb.export(output_paths['glb'])
                print(f"Saved GLB file to: {output_paths['glb']}")
            except Exception as e:
                print(f"Error in GLB generation: {e}")

                # Fallback: Try to save a simple OBJ file with the same name but .glb extension
                try:
                    print("Using fallback for GLB file (renamed OBJ)...")
                    mesh = outputs['mesh'][0]
                    vertices = mesh.vertices.detach().cpu().numpy()
                    faces = mesh.faces.detach().cpu().numpy()

                    # Save as OBJ but with GLB extension
                    with open(output_paths['glb'], 'w') as f:
                        # Write vertices
                        for v in vertices:
                            f.write(f"v {v[0]} {v[1]} {v[2]}\n")

                        # Write faces (OBJ is 1-indexed)
                        for face in faces:
                            f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")

                    print(f"Saved fallback GLB file to: {output_paths['glb']}")
                except Exception as e2:
                    print(f"Error in fallback GLB generation: {e2}")
                    output_paths['glb'] = None
        except Exception as e:
            print(f"Error generating GLB file: {e}")
            import traceback
            traceback.print_exc()
            output_paths['glb'] = None

        # Save OBJ file (for compatibility with more viewers)
        try:
            print("Saving OBJ file...")
            # Check if mesh has export_obj method
            mesh = outputs['mesh'][0]
            if hasattr(mesh, 'export_obj'):
                mesh.export_obj(output_paths['obj'])
                print(f"Saved OBJ file to: {output_paths['obj']}")
            else:
                # Manual OBJ export using mesh vertices and faces
                print("Using manual OBJ export...")
                vertices = mesh.vertices.detach().cpu().numpy()
                faces = mesh.faces.detach().cpu().numpy()

                with open(output_paths['obj'], 'w') as f:
                    # Write vertices
                    for v in vertices:
                        f.write(f"v {v[0]} {v[1]} {v[2]}\n")

                    # Write faces (OBJ is 1-indexed)
                    for face in faces:
                        f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")

                print(f"Saved OBJ file to: {output_paths['obj']}")
        except Exception as e:
            print(f"Error saving OBJ file: {e}")
            output_paths['obj'] = None

        # Save PLY file
        try:
            print("Saving PLY file...")
            outputs['gaussian'][0].save_ply(output_paths['ply'])
            print(f"Saved PLY file to: {output_paths['ply']}")
        except Exception as e:
            print(f"Error saving PLY file: {e}")
            output_paths['ply'] = None

        # Return the successful outputs
        successful_outputs = {k: v for k, v in output_paths.items() if v is not None}
        if not successful_outputs:
            print("Error: No output files were successfully generated")
            return None

        print(f"Successfully generated {len(successful_outputs)} output files")
        return {
            'outputs': outputs,
            'paths': successful_outputs
        }
    except Exception as e:
        print(f"Error generating 3D model: {e}")
        import traceback
        traceback.print_exc()
        return None

def generate_3d_from_multiple_images(pipeline, render_utils, postprocessing_utils, image_paths, output_dir="output", settings=None):
    """
    Generate a 3D model from multiple 2D images using Trellis

    Args:
        pipeline: The Trellis pipeline
        render_utils: Trellis render utilities
        postprocessing_utils: Trellis postprocessing utilities
        image_paths: List of paths to the input images
        output_dir: Directory to save the output files
        settings: Dictionary with generation settings (optional)

    Returns:
        Dictionary containing paths to the generated files
    """
    # Check if we have a valid pipeline
    if pipeline is None or render_utils is None or postprocessing_utils is None:
        print("Error: Trellis pipeline or utilities are not available")
        return None

    # Default settings if none provided
    if settings is None:
        settings = {
            "seed": 42,
            "ss_guidance_strength": 7.5,
            "ss_sampling_steps": 25,
            "slat_guidance_strength": 3.0,
            "slat_sampling_steps": 25
        }

    # Extract settings
    seed = settings.get("seed", 42)
    ss_guidance_strength = settings.get("ss_guidance_strength", 7.5)
    ss_sampling_steps = settings.get("ss_sampling_steps", 25)
    slat_guidance_strength = settings.get("slat_guidance_strength", 3.0)
    slat_sampling_steps = settings.get("slat_sampling_steps", 25)

    print(f"Using settings: seed={seed}, ss_steps={ss_sampling_steps}, ss_strength={ss_guidance_strength}, "
          f"slat_steps={slat_sampling_steps}, slat_strength={slat_guidance_strength}")

    try:
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Validate image paths
        valid_image_paths = []
        for path in image_paths:
            # Normalize the path
            norm_path = os.path.normpath(os.path.abspath(path))

            # Check if the image exists
            if not os.path.exists(norm_path):
                print(f"Warning: Image file not found: {norm_path}")
                continue

            valid_image_paths.append(norm_path)

        if not valid_image_paths:
            print("Error: No valid image files found")
            return None

        print(f"Processing {len(valid_image_paths)} images")

        # Load the images
        try:
            images = []
            for path in valid_image_paths:
                img = Image.open(path)
                print(f"Loaded image: {path} ({img.width}x{img.height}, {img.mode})")
                images.append(img)
        except Exception as e:
            print(f"Error loading images: {e}")
            return None

        # Generate base filename from input images
        base_filename = "multi_" + "_".join([os.path.splitext(os.path.basename(path))[0] for path in valid_image_paths])
        if len(base_filename) > 100:  # Avoid too long filenames
            base_filename = f"multi_{hash(tuple(valid_image_paths))}"

        print(f"Using base filename: {base_filename}")

        # Run the pipeline
        print(f"Running Trellis multi-image pipeline with {len(images)} images")
        try:
            outputs = pipeline.run_multi_image(
                images,
                seed=seed,
                formats=["mesh", "gaussian", "radiance_field"],
                sparse_structure_sampler_params={
                    "steps": ss_sampling_steps,
                    "cfg_strength": ss_guidance_strength,
                },
                slat_sampler_params={
                    "steps": slat_sampling_steps,
                    "cfg_strength": slat_guidance_strength,
                },
            )
            print("Pipeline execution completed successfully")
        except Exception as e:
            print(f"Error running Trellis pipeline: {e}")
            import traceback
            traceback.print_exc()
            return None

        # Define output paths
        output_paths = {
            'combined_video': os.path.join(output_dir, f"{base_filename}_combined.mp4"),
            'gaussian_video': os.path.join(output_dir, f"{base_filename}_gaussian.mp4"),
            'mesh_video': os.path.join(output_dir, f"{base_filename}_mesh.mp4"),
            'glb': os.path.join(output_dir, f"{base_filename}.glb"),
            'obj': os.path.join(output_dir, f"{base_filename}.obj"),
            'ply': os.path.join(output_dir, f"{base_filename}.ply")
        }

        # Render and save individual videos
        try:
            print("Rendering gaussian video...")
            video_gs = render_utils.render_video(outputs['gaussian'][0])['color']
            imageio.mimsave(output_paths['gaussian_video'], video_gs, fps=30)
            print(f"Saved gaussian video to: {output_paths['gaussian_video']}")
        except Exception as e:
            print(f"Error rendering gaussian video: {e}")
            output_paths['gaussian_video'] = None
            video_gs = None

        try:
            print("Rendering mesh video...")
            # Check if we're likely to encounter the cl.exe error
            cl_exe_exists = False
            try:
                import subprocess
                result = subprocess.run(['where', 'cl.exe'], capture_output=True, text=True)
                cl_exe_exists = result.returncode == 0
            except:
                pass

            if cl_exe_exists:
                # Normal path - Visual Studio C++ compiler is available
                video_mesh = render_utils.render_video(outputs['mesh'][0])['normal']
                imageio.mimsave(output_paths['mesh_video'], video_mesh, fps=30)
                print(f"Saved mesh video to: {output_paths['mesh_video']}")
            else:
                # Fallback path - create a simple video from the gaussian video
                print("Visual Studio C++ compiler (cl.exe) not found. Using fallback for mesh video...")

                # Check if we have the gaussian video
                if video_gs is not None:
                    # Create a grayscale version of the gaussian video
                    try:
                        import cv2

                        # Convert the gaussian frames to grayscale
                        video_mesh = []
                        for frame in video_gs:
                            # Convert to BGR for OpenCV
                            frame_bgr = frame[:, :, ::-1]
                            # Convert to grayscale and back to RGB
                            gray = cv2.cvtColor(frame_bgr, cv2.COLOR_BGR2GRAY)
                            gray_rgb = cv2.cvtColor(gray, cv2.COLOR_GRAY2RGB)
                            video_mesh.append(gray_rgb)

                        # Save the mesh video
                        imageio.mimsave(output_paths['mesh_video'], video_mesh, fps=30)
                        print(f"Saved fallback mesh video to: {output_paths['mesh_video']}")
                    except Exception as e:
                        print(f"Error creating fallback mesh video: {e}")
                        output_paths['mesh_video'] = None
                        video_mesh = None
                else:
                    print("Cannot create fallback mesh video: gaussian video not available")
                    output_paths['mesh_video'] = None
                    video_mesh = None
        except Exception as e:
            print(f"Error rendering mesh video: {e}")
            import traceback
            traceback.print_exc()
            output_paths['mesh_video'] = None
            video_mesh = None

        # Render and save combined video if both individual videos were created
        if video_gs is not None and video_mesh is not None:
            try:
                print("Creating combined video...")
                video = [np.concatenate([frame_gs, frame_mesh], axis=1) for frame_gs, frame_mesh in zip(video_gs, video_mesh)]
                imageio.mimsave(output_paths['combined_video'], video, fps=30)
                print(f"Saved combined video to: {output_paths['combined_video']}")
            except Exception as e:
                print(f"Error creating combined video: {e}")
                output_paths['combined_video'] = None
        else:
            print("Skipping combined video creation because individual videos failed")
            output_paths['combined_video'] = None

        # Generate and save GLB file
        try:
            print("Generating GLB file...")

            # Check if utils3d.torch has the required perspective function
            import sys
            if 'utils3d' in sys.modules and hasattr(sys.modules['utils3d'], 'torch'):
                utils3d_torch = sys.modules['utils3d'].torch

                if not hasattr(utils3d_torch, 'perspective_from_fov_xy'):
                    # Add the missing function to utils3d.torch
                    def perspective_from_fov_xy(fov_x, fov_y, znear, zfar):
                        """
                        Create a perspective projection matrix from field of view angles.

                        Args:
                            fov_x: Field of view angle in the x direction, in radians.
                            fov_y: Field of view angle in the y direction, in radians.
                            znear: Distance to the near clipping plane.
                            zfar: Distance to the far clipping plane.

                        Returns:
                            A 4x4 perspective projection matrix.
                        """
                        tan_x = torch.tan(fov_x / 2)
                        tan_y = torch.tan(fov_y / 2)

                        # Create the perspective matrix
                        result = torch.zeros((4, 4), dtype=torch.float32)
                        result[0, 0] = 1.0 / tan_x
                        result[1, 1] = 1.0 / tan_y
                        result[2, 2] = -(zfar + znear) / (zfar - znear)
                        result[2, 3] = -2.0 * zfar * znear / (zfar - znear)
                        result[3, 2] = -1.0

                        return result

                    # Add the function to utils3d.torch
                    utils3d_torch.perspective_from_fov_xy = perspective_from_fov_xy
                    print("Added missing perspective_from_fov_xy function to utils3d.torch")
            else:
                print("Warning: utils3d.torch module not found, GLB generation may fail")

            # Try to generate the GLB file
            try:
                glb = postprocessing_utils.to_glb(
                    outputs['gaussian'][0],
                    outputs['mesh'][0],
                    simplify=0.95,
                    texture_size=1024,
                )
                glb.export(output_paths['glb'])
                print(f"Saved GLB file to: {output_paths['glb']}")
            except Exception as e:
                print(f"Error in GLB generation: {e}")

                # Fallback: Try to save a simple OBJ file with the same name but .glb extension
                try:
                    print("Using fallback for GLB file (renamed OBJ)...")
                    mesh = outputs['mesh'][0]
                    vertices = mesh.vertices.detach().cpu().numpy()
                    faces = mesh.faces.detach().cpu().numpy()

                    # Save as OBJ but with GLB extension
                    with open(output_paths['glb'], 'w') as f:
                        # Write vertices
                        for v in vertices:
                            f.write(f"v {v[0]} {v[1]} {v[2]}\n")

                        # Write faces (OBJ is 1-indexed)
                        for face in faces:
                            f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")

                    print(f"Saved fallback GLB file to: {output_paths['glb']}")
                except Exception as e2:
                    print(f"Error in fallback GLB generation: {e2}")
                    output_paths['glb'] = None
        except Exception as e:
            print(f"Error generating GLB file: {e}")
            import traceback
            traceback.print_exc()
            output_paths['glb'] = None

        # Save OBJ file (for compatibility with more viewers)
        try:
            print("Saving OBJ file...")
            # Check if mesh has export_obj method
            mesh = outputs['mesh'][0]
            if hasattr(mesh, 'export_obj'):
                mesh.export_obj(output_paths['obj'])
                print(f"Saved OBJ file to: {output_paths['obj']}")
            else:
                # Manual OBJ export using mesh vertices and faces
                print("Using manual OBJ export...")
                vertices = mesh.vertices.detach().cpu().numpy()
                faces = mesh.faces.detach().cpu().numpy()

                with open(output_paths['obj'], 'w') as f:
                    # Write vertices
                    for v in vertices:
                        f.write(f"v {v[0]} {v[1]} {v[2]}\n")

                    # Write faces (OBJ is 1-indexed)
                    for face in faces:
                        f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")

                print(f"Saved OBJ file to: {output_paths['obj']}")
        except Exception as e:
            print(f"Error saving OBJ file: {e}")
            output_paths['obj'] = None

        # Save PLY file
        try:
            print("Saving PLY file...")
            outputs['gaussian'][0].save_ply(output_paths['ply'])
            print(f"Saved PLY file to: {output_paths['ply']}")
        except Exception as e:
            print(f"Error saving PLY file: {e}")
            output_paths['ply'] = None

        # Return the successful outputs
        successful_outputs = {k: v for k, v in output_paths.items() if v is not None}
        if not successful_outputs:
            print("Error: No output files were successfully generated")
            return None

        print(f"Successfully generated {len(successful_outputs)} output files")
        return {
            'outputs': outputs,
            'paths': successful_outputs
        }
    except Exception as e:
        print(f"Error generating 3D model from multiple images: {e}")
        return None

if __name__ == "__main__":
    # Example usage
    print("Setting up Trellis pipeline...")
    pipeline, render_utils, postprocessing_utils = setup_trellis()

    if pipeline:
        print("\n" + "="*50)
        print("Trellis pipeline setup successful!")
        print("="*50 + "\n")

        # Define test settings
        settings = {
            "seed": 42,
            "ss_guidance_strength": 7.5,
            "ss_sampling_steps": 25,
            "slat_guidance_strength": 3.0,
            "slat_sampling_steps": 25
        }

        # Check if example image exists
        example_image_path = os.path.join(
            os.path.dirname(os.path.abspath(__file__)),
            "trellis_official", "TRELLIS WINDOWS", "assets", "example_image", "T.png"
        )

        if os.path.exists(example_image_path):
            print("\n" + "="*50)
            print("Running single image example...")
            print("="*50)

            # Single image example
            result = generate_3d_model(
                pipeline,
                render_utils,
                postprocessing_utils,
                example_image_path,
                output_dir="output",
                settings=settings
            )

            if result:
                print("\nSingle image 3D generation completed successfully!")
                print("Output files:")
                for key, path in result['paths'].items():
                    print(f"  {key}: {path}")
            else:
                print("\nSingle image 3D generation failed.")
        else:
            print(f"\nExample image not found: {example_image_path}")

        # Check if multi-image examples exist
        multi_image_paths = [
            os.path.join(os.path.dirname(os.path.abspath(__file__)),
                        "trellis_official", "TRELLIS WINDOWS", "assets", "example_multi_image", "character_1.png"),
            os.path.join(os.path.dirname(os.path.abspath(__file__)),
                        "trellis_official", "TRELLIS WINDOWS", "assets", "example_multi_image", "character_2.png"),
            os.path.join(os.path.dirname(os.path.abspath(__file__)),
                        "trellis_official", "TRELLIS WINDOWS", "assets", "example_multi_image", "character_3.png")
        ]

        if all(os.path.exists(path) for path in multi_image_paths):
            print("\n" + "="*50)
            print("Running multi-image example...")
            print("="*50)

            # Multi-image example
            multi_result = generate_3d_from_multiple_images(
                pipeline,
                render_utils,
                postprocessing_utils,
                multi_image_paths,
                output_dir="output",
                settings=settings
            )

            if multi_result:
                print("\nMulti-image 3D generation completed successfully!")
                print("Output files:")
                for key, path in multi_result['paths'].items():
                    print(f"  {key}: {path}")
            else:
                print("\nMulti-image 3D generation failed.")
        else:
            print("\nOne or more multi-image example files not found.")
    else:
        print("\n" + "="*50)
        print("Failed to set up Trellis pipeline.")
        print("Please check the error messages above for more information.")
        print("="*50)
