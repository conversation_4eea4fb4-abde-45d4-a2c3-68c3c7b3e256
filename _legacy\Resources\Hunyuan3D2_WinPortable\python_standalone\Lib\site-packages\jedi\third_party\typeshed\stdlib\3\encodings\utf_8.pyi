import codecs
from typing import Tuple

class IncrementalEncoder(codecs.IncrementalEncoder):
    def encode(self, input: str, final: bool = ...) -> bytes: ...

class IncrementalDecoder(codecs.BufferedIncrementalDecoder):
    def _buffer_decode(self, input: bytes, errors: str, final: bool) -> Tuple[str, int]: ...

class StreamWriter(codecs.StreamWriter): ...
class StreamReader(codecs.StreamReader): ...

def getregentry() -> codecs.CodecInfo: ...
def encode(input: str, errors: str = ...) -> bytes: ...
def decode(input: bytes, errors: str = ...) -> str: ...
