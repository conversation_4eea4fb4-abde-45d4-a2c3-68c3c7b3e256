========================================
         3D AI Studio Launcher
========================================

[1/3] Checking Node.js installation...
Γ£ô Node.js found
[2/3] Checking for node_modules...
Γ£ô Dependencies ready
[3/3] Building frontend for production...
The CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.
vite v5.4.19 building for production...

(!) outDir N:\3D AI Studio\dist\renderer is not inside project root and will not be emptied.
Use --emptyOutDir to override.

src/renderer/components/ModelViewer.tsx (578:32): "sRGBEncoding" is not exported by "node_modules/three/build/three.module.js", imported by "src/renderer/components/ModelViewer.tsx".
✓ 2109 modules transformed.
../../dist/renderer/index.html                     0.21 kB │ gzip:   0.16 kB
../../dist/renderer/assets/index-chhQ5rKu.css     34.66 kB │ gzip:   6.05 kB
../../dist/renderer/assets/index-Bb6alhid.js   1,256.07 kB │ gzip: 346.54 kB

(!) Some chunks are larger than 500 kB after minification. Consider:
- Using dynamic import() to code-split the application
- Use build.rollupOptions.output.manualChunks to improve chunking: https://rollupjs.org/configuration-options/#output-manualchunks
- Adjust chunk size limit for this warning via build.chunkSizeWarningLimit.
✓ built in 12.69s
Γ£ô Frontend build complete

========================================
    Starting 3D AI Studio (Production)...
========================================


> 3d-ai-studio@1.0.0 start
> electron .


info: Registering pipelines... {"service":"user-service","timestamp":"2025-07-08 06:55:51"}
info: Logger initialized. {"service":"user-service","timestamp":"2025-07-08 06:55:51"}
info: [DEBUG] Found pipeline folders: {"0":"3DPipelines","1":"Core","2":"Hunyaun3d-2","3":"ImageGeneration","4":"TrellisSource","service":"user-service","timestamp":"2025-07-08 06:55:51"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\3DPipelines\config.json {"service":"user-service","timestamp":"2025-07-08 06:55:51"}
info: App is ready. {"service":"user-service","timestamp":"2025-07-08 06:55:51"}
[Cleanup] Checking for lingering Python processes...
warn: Could not register pipeline 3DPipelines: ENOENT: no such file or directory, open 'N:\3D AI Studio\pipelines\3DPipelines\config.json' {"service":"user-service","timestamp":"2025-07-08 06:55:51"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\Core\config.json {"service":"user-service","timestamp":"2025-07-08 06:55:51"}
info: Registered pipeline: Core {"service":"user-service","timestamp":"2025-07-08 06:55:51"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\Hunyaun3d-2\config.json {"service":"user-service","timestamp":"2025-07-08 06:55:51"}
info: Registered pipeline: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-08 06:55:51"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\ImageGeneration\config.json {"service":"user-service","timestamp":"2025-07-08 06:55:51"}
info: Registered pipeline: ImageGeneration {"service":"user-service","timestamp":"2025-07-08 06:55:51"}
info: [DEBUG] Checking config at: N:\3D AI Studio\pipelines\TrellisSource\config.json {"service":"user-service","timestamp":"2025-07-08 06:55:51"}
info: [DEBUG] TrellisSource config raw type: string {"service":"user-service","timestamp":"2025-07-08 06:55:51"}
info: [DEBUG] TrellisSource config raw (first 200 chars): {
  "name": "TrellisSource",
  "description": "Microsoft TRELLIS - Advanced 3D generation from images",
  "dependencies": {
    "python": [
      "torch>=2.7.1+cu128",
      "torchvision>=0.18.1+cu128 {"service":"user-service","timestamp":"2025-07-08 06:55:51"}
info: [DEBUG] TrellisSource config parsed keys: {"0":"name","1":"description","2":"dependencies","service":"user-service","timestamp":"2025-07-08 06:55:51"}
info: [DEBUG] TrellisSource config parsed: {"dependencies":{"models":[{"auth_error_message":"This model requires a Hugging Face account. Please ensure you have logged in with your Hugging Face token in Settings.","description":"Microsoft TRELLIS Large model (requires Hugging Face account access)","local_path":"TrellisSource/Trellis-Large","name":"Trellis-Large","repo_id":"microsoft/TRELLIS-image-large","required":true,"requires_auth":true}],"python":["torch>=2.7.1+cu128","torchvision>=0.18.1+cu128","numpy>=1.24.3","opencv-python>=********","pillow>=10.0.0","matplotlib>=3.7.1","tqdm>=4.66.1","ninja>=1.11.1","huggingface_hub>=0.20.3","trimesh>=4.0.0","imageio>=2.33.0","hf_transfer>=0.1.4","transformers>=4.36.0","safetensors>=0.4.0"]},"description":"Microsoft TRELLIS - Advanced 3D generation from images","name":"TrellisSource","service":"user-service","timestamp":"2025-07-08 06:55:51"}
info: Registered pipeline: TrellisSource {"service":"user-service","timestamp":"2025-07-08 06:55:51"}
info: [DEBUG] Final registered pipelines: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-08 06:55:51"}
info: PipelineLoader: Pipelines registered at startup: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-08 06:55:51"}
[Cleanup] Found Python processes, killing them...
[Cleanup] Processes found: python.exe                   26372 Console                    1     52,520 K
python.exe                   30676 Console                    1     50,080 K
python.exe                    9520 Console                    1 11,672,916 K
[Cleanup] Successfully killed Python processes
info: Python process cleanup completed on startup {"service":"user-service","timestamp":"2025-07-08 06:55:55"}
info: Production mode - Loading file: N:\3D AI Studio\dist\renderer\index.html {"service":"user-service","timestamp":"2025-07-08 06:55:56"}
info: Main window web contents started loading {"service":"user-service","timestamp":"2025-07-08 06:55:56"}
info: Splash screen initialization complete {"service":"user-service","timestamp":"2025-07-08 06:55:56"}
info: Splash Status: Loading pipeline configurations... {"service":"user-service","timestamp":"2025-07-08 06:55:56"}
info: Splash Status: Scanning pipelines... {"service":"user-service","timestamp":"2025-07-08 06:55:56"}
info: Loading pipelines from embedded configurations... {"service":"user-service","timestamp":"2025-07-08 06:55:56"}
info: Loaded pipeline: Core {"service":"user-service","timestamp":"2025-07-08 06:55:56"}
info: Loaded pipeline: ImageGeneration {"service":"user-service","timestamp":"2025-07-08 06:55:56"}
info: Loaded pipeline: TrellisSource {"service":"user-service","timestamp":"2025-07-08 06:55:56"}
info: Loaded pipeline: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-08 06:55:56"}
info: Ensured pipeline directory exists: Core {"service":"user-service","timestamp":"2025-07-08 06:55:56"}
info: Ensured pipeline directory exists: ImageGeneration {"service":"user-service","timestamp":"2025-07-08 06:55:56"}
info: Ensured pipeline directory exists: TrellisSource {"service":"user-service","timestamp":"2025-07-08 06:55:56"}
info: Ensured pipeline directory exists: Hunyaun3d-2 {"service":"user-service","timestamp":"2025-07-08 06:55:56"}
info: Splash Status: Checking core dependencies... {"service":"user-service","timestamp":"2025-07-08 06:55:56"}
info: Checking dependencies for Core {"service":"user-service","timestamp":"2025-07-08 06:55:56"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-08 06:55:56"}
info: Main window DOM ready {"service":"user-service","timestamp":"2025-07-08 06:55:56"}
info: Main window web contents finished loading {"service":"user-service","timestamp":"2025-07-08 06:55:56"}
info: Main window finished loading {"service":"user-service","timestamp":"2025-07-08 06:55:56"}
info: Splash screen displayed {"service":"user-service","timestamp":"2025-07-08 06:55:56"}
info: IPC: get-sample-images called {"service":"user-service","timestamp":"2025-07-08 06:55:57"}
info: Loading 20 sample images {"service":"user-service","timestamp":"2025-07-08 06:55:57"}
info: Loaded sample 1/20: Red Lantern With Black Top and Bottom {"service":"user-service","timestamp":"2025-07-08 06:55:57"}
info: Loaded sample 2/20: Wooden Crate With Metal Brackets {"service":"user-service","timestamp":"2025-07-08 06:55:57"}
info: Loaded sample 3/20: Chinese Wooden Lion {"service":"user-service","timestamp":"2025-07-08 06:55:57"}
info: Loaded sample 4/20: Green Flower Pot {"service":"user-service","timestamp":"2025-07-08 06:55:57"}
info: Loaded sample 5/20: Blue Couch {"service":"user-service","timestamp":"2025-07-08 06:55:57"}
info: Loaded sample 6/20: Little Girl {"service":"user-service","timestamp":"2025-07-08 06:55:57"}
info: Loaded sample 7/20: Wood Chair With Brown Cussions {"service":"user-service","timestamp":"2025-07-08 06:55:57"}
info: Loaded sample 8/20: Lantern {"service":"user-service","timestamp":"2025-07-08 06:55:57"}
info: Loaded sample 9/20: Wooden Barrel {"service":"user-service","timestamp":"2025-07-08 06:55:57"}
info: Loaded sample 10/20: Cut Log Piece {"service":"user-service","timestamp":"2025-07-08 06:55:57"}
info: Loaded sample 11/20: White Chair {"service":"user-service","timestamp":"2025-07-08 06:55:57"}
info: Loaded sample 12/20: Greek Statue {"service":"user-service","timestamp":"2025-07-08 06:55:57"}
info: Loaded sample 13/20: Woman With Short Hair And Red Pants {"service":"user-service","timestamp":"2025-07-08 06:55:57"}
info: Loaded sample 14/20: Stylized Tree {"service":"user-service","timestamp":"2025-07-08 06:55:57"}
info: Loaded sample 15/20: Chinese Stone Lion {"service":"user-service","timestamp":"2025-07-08 06:55:57"}
info: Loaded sample 16/20: White Backpack {"service":"user-service","timestamp":"2025-07-08 06:55:57"}
info: Loaded sample 17/20: Potted Plant {"service":"user-service","timestamp":"2025-07-08 06:55:57"}
info: Loaded sample 18/20: Red Myan Pot {"service":"user-service","timestamp":"2025-07-08 06:55:57"}
info: Loaded sample 19/20: Mayan Pot In The Form Of A Kneeling Figure 800 1200 Mayan Pot In The Form Of A Kneeling Figure {"service":"user-service","timestamp":"2025-07-08 06:55:57"}
info: Loaded sample 20/20: Golden Greek Pillar {"service":"user-service","timestamp":"2025-07-08 06:55:57"}
info: IPC: get-config called for key: huggingface-token {"service":"user-service","timestamp":"2025-07-08 06:55:58"}
info: IPC: get-available-pipelines called {"service":"user-service","timestamp":"2025-07-08 06:55:58"}
info: [DEBUG] getRegisteredPipelines returning: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-08 06:55:58"}
info: Final dependency check for Core: Python=true, Models=true, Overall=true {"service":"user-service","timestamp":"2025-07-08 06:56:00"}
info: Splash Status: Core dependencies verified {"service":"user-service","timestamp":"2025-07-08 06:56:00"}
info: Splash Status: Finalizing startup... {"service":"user-service","timestamp":"2025-07-08 06:56:00"}
[34244:0708/065603.569:ERROR:CONSOLE(1)] "Request Autofill.enable failed. {"code":-32601,"message":"'Autofill.enable' wasn't found"}", source: devtools://devtools/bundled/core/protocol_client/protocol_client.js (1)
[34244:0708/065603.570:ERROR:CONSOLE(1)] "Request Autofill.setAddresses failed. {"code":-32601,"message":"'Autofill.setAddresses' wasn't found"}", source: devtools://devtools/bundled/core/protocol_client/protocol_client.js (1)
info: Startup complete - showing main window {"service":"user-service","timestamp":"2025-07-08 06:56:03"}
info: Splash screen closed {"service":"user-service","timestamp":"2025-07-08 06:56:03"}
info: [upload-file] Received ΓÇô filename: 20250613_103426_Shopify_JPEG-Renzo_82G21259_SB_3qtr.jpg, size: 432552 bytes (Uint8Array) {"service":"user-service","timestamp":"2025-07-08 06:56:19"}
info: Buffer received (bytes): 432552 {"service":"user-service","timestamp":"2025-07-08 06:56:19"}
info: Uploaded original file saved: N:\3D AI Studio\uploads\f4e9976b-b5c1-437d-84af-eb04a5b5418c\20250613_103426_Shopify_JPEG-Renzo_82G21259_SB_3qtr.jpg {"service":"user-service","timestamp":"2025-07-08 06:56:19"}
info: Starting background removal for N:\3D AI Studio\uploads\f4e9976b-b5c1-437d-84af-eb04a5b5418c\20250613_103426_Shopify_JPEG-Renzo_82G21259_SB_3qtr.jpg... {"service":"user-service","timestamp":"2025-07-08 06:56:19"}
info: Spawning: N:\3D AI Studio\pipelines\Core\env\Scripts\python.exe N:\3D AI Studio\src\main\python_helpers\remove_background.py N:\3D AI Studio\uploads\f4e9976b-b5c1-437d-84af-eb04a5b5418c\20250613_103426_Shopify_JPEG-Renzo_82G21259_SB_3qtr.jpg N:\3D AI Studio\uploads\f4e9976b-b5c1-437d-84af-eb04a5b5418c\20250613_103426_Shopify_JPEG-Renzo_82G21259_SB_3qtr_processed.png {"service":"user-service","timestamp":"2025-07-08 06:56:19"}
info: [load-file] Received request for: uploads/f4e9976b-b5c1-437d-84af-eb04a5b5418c/20250613_103426_Shopify_JPEG-Renzo_82G21259_SB_3qtr_processed.png {"service":"user-service","timestamp":"2025-07-08 06:56:46"}
info: [load-file] Reading absolute path: N:\3D AI Studio\uploads\f4e9976b-b5c1-437d-84af-eb04a5b5418c\20250613_103426_Shopify_JPEG-Renzo_82G21259_SB_3qtr_processed.png {"service":"user-service","timestamp":"2025-07-08 06:56:46"}
info: IPC: run-pipeline called for: TrellisSource {"service":"user-service","timestamp":"2025-07-08 06:56:49"}
info: runPipeline request: TrellisSource {"service":"user-service","timestamp":"2025-07-08 06:56:49"}
info: runPipeline: Registered pipelines at call time: {"0":"Core","1":"Hunyaun3d-2","2":"ImageGeneration","3":"TrellisSource","service":"user-service","timestamp":"2025-07-08 06:56:49"}
info: Checking dependencies for TrellisSource {"service":"user-service","timestamp":"2025-07-08 06:56:49"}
info: TrellisSource initialization flag file found {"service":"user-service","timestamp":"2025-07-08 06:56:49"}
info: TrellisSource Python executable found {"service":"user-service","timestamp":"2025-07-08 06:56:49"}
info: TrellisSource installation validation successful {"service":"user-service","timestamp":"2025-07-08 06:56:49"}
info: TrellisSource dependency validation: PASSED {"service":"user-service","timestamp":"2025-07-08 06:56:49"}
info: Checking model Trellis-Large at path: N:\3D AI Studio\models\TrellisSource\Trellis-Large {"service":"user-service","timestamp":"2025-07-08 06:56:49"}
info: Model Trellis-Large found with 5 files/folders {"service":"user-service","timestamp":"2025-07-08 06:56:49"}
info: Model dependency check for TrellisSource: satisfied {"service":"user-service","timestamp":"2025-07-08 06:56:49"}
info: Final dependency check for TrellisSource: Python=true, Models=true, Overall=true {"service":"user-service","timestamp":"2025-07-08 06:56:49"}
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Launching Trellis server and generating 3D model...',
  description: 'Launching Trellis server and generating 3D model...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Launching Trellis server and generating 3D model...',
  description: 'Launching Trellis server and generating 3D model...'
}
[Trellis Server] generate3DModel called with imagePath: uploads/f4e9976b-b5c1-437d-84af-eb04a5b5418c/20250613_103426_Shopify_JPEG-Renzo_82G21259_SB_3qtr_processed.png
[Cleanup] Checking for lingering Python processes...
[Cleanup] Found Python processes, killing them...
[Cleanup] Processes found: python.exe                   31996 Console                    1     52,612 K
python.exe                    3148 Console                    1     49,424 K
[Cleanup] Successfully killed Python processes
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Trellis Server] isTrellisRunning() returned: false
[Trellis Server] Server not running, starting server...
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'preprocessing',
  progress: 0,
  message: 'Loading image and initializing pipeline',
  description: 'Loading image and initializing pipeline'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'preprocessing',
  progress: 0,
  message: 'Loading image and initializing pipeline',
  description: 'Loading image and initializing pipeline'
}
[Trellis Server] Starting server...
[Trellis Server] RUN_BAT: N:\3D AI Studio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\run-fp16.bat
[Trellis Server] Batch file exists: true
[Trellis Server] Working directory: N:\3D AI Studio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101
[Trellis Server] Command: cmd.exe /c run-fp16.bat
[Trellis Server] startTrellisServer() called successfully
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] sitecustomize.py applied
[Progress Debug] Parsing output with globalProgressCallback set
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Trellis Server] Server not running - connection failed (10 attempts): ECONNREFUSED
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] created virtual environment CPython3.11.9.final.0-64 in 19547ms
[Trellis Server] creator CPython3Windows(dest=N:\3D AI Studio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\code\venv, clear=False, no_vcs_ignore=False, global=False)
[Trellis Server] seeder FromAppData(download=False, pip=bundle, setuptools=bundle, wheel=bundle, via=copy, app_data_dir=C:\Users\<USER>\AppData\Local\pypa\virtualenv)
[Trellis Server] added seed packages: Deprecated==1.2.18, MarkupSafe==2.1.5, PyMatting==1.1.13, PyYAML==6.0.2, aiofiles==23.2.1, annotated_types==0.7.0, anyio==4.9.0, asttokens==3.0.0, attrs==25.3.0, blinker==1.9.0, ccimport==0.4.4, certifi==2025.1.31, charset_normalizer==3.4.1, click==8.1.8, colorama==0.4.6, coloredlogs==15.0.1, comm==0.2.2, contourpy==1.3.1, cumm_cu118==0.4.11, cumm_cu128==0.7.13, cycler==0.12.1, dataclasses_json==0.6.7, decorator==5.2.1, diff_gaussian_rasterization==0.0.0, diffoctreerast==0.0.0, easydict==1.13, entrypoints==0.4, executing==2.2.0, fastapi==0.115.6, ffmpy==0.5.0, filelock==3.18.0, fire==0.7.0, flask==3.1.0, flatbuffers==25.2.10, fonttools==4.56.0, fsspec==2025.3.2, glcontext==3.0.0, gradio==4.44.1, gradio_client==1.3.0, gradio_litmodel3d==0.0.1, h11==0.14.0, httpcore==1.0.7, httpx==0.28.1, huggingface_hub==0.30.1, humanfriendly==10.0, idna==3.10, igraph==0.11.8, imageio==2.37.0, imageio_ffmpeg==0.6.0, importlib_resources==6.5.2, ipycanvas==0.13.3, ipyevents==2.0.2, ipython==9.0.2, ipython_pygments_lexers==1.1.1, ipywidgets==8.1.5, itsdangerous==2.2.0, jedi==0.19.2, jinja2==3.1.4, jsonschema==4.23.0, jsonschema_specifications==2024.10.1, jupyter_client==7.4.9, jupyter_core==5.7.2, jupyterlab_widgets==3.0.13, kaolin==0.17.0, kiwisolver==1.4.8, lark==1.2.2, lazy_loader==0.4, llvmlite==0.44.0, markdown_it_py==3.0.0, marshmallow==3.26.1, matplotlib==3.10.1, matplotlib_inline==0.1.7, mdurl==0.1.2, moderngl==5.12.0, mpmath==1.3.0, mypy_extensions==1.0.0, nest_asyncio==1.6.0, networkx==3.4.2, ninja==********, numba==0.61.0, numpy==1.26.4, nvdiffrast==0.3.3, onnxruntime==1.21.0, opencv_python_headless==*********, orjson==3.10.16, packaging==24.2, pandas==2.2.3, parso==0.8.4, pccm==0.4.16, pillow==10.4.0, pip==25.1.1, platformdirs==4.3.7, plyfile==1.1, pooch==1.8.2, portalocker==3.1.1, prompt_toolkit==3.0.50, protobuf==6.30.2, pure_eval==0.2.3, pybind11==2.13.6, pydantic==2.10.5, pydantic_core==2.27.2, pydub==0.25.1, pygltflib==1.16.3, pygments==2.19.1, pymeshfix==0.17.0, pyparsing==3.2.3, pyreadline3==3.5.4, python_dateutil==2.9.0.post0, python_multipart==0.0.20, pytz==2025.2, pyvista==0.44.2, pywin32==310, pyzmq==26.3.0, referencing==0.36.2, regex==2024.11.6, rembg==2.0.65, requests==2.32.3, rich==14.0.0, rpds_py==0.24.0, ruff==0.11.2, safetensors==0.5.3, scikit_image==0.25.2, scipy==1.15.2, scooby==0.10.0, semantic_version==2.10.0, setuptools==80.9.0, shellingham==1.5.4, six==1.17.0, sniffio==1.3.1, spconv_cu118==2.3.6, spconv_cu128==2.3.8, stack_data==0.6.3, starlette==0.41.3, sympy==1.13.3, termcolor==3.0.1, texttable==1.7.0, tifffile==2025.3.30, tokenizers==0.21.1, tomlkit==0.12.0, torch==2.7.1+cu128, torchaudio==2.7.1+cu128, torchvision==0.22.1+cu128, tornado==6.5.1, tqdm==4.67.1, traitlets==5.14.3, transformers==4.50.3, trimesh==4.6.6, typer==0.15.2, typing_extensions==4.13.0, typing_inspect==0.9.0, typing_inspection==0.4.0, tzdata==2025.2, urllib3==2.3.0, usd_core==25.2.post1, utils3d==0.0.2, uvicorn==0.34.0, vtk==9.3.1, warp_lang==1.7.0, wcwidth==0.2.13, websockets==12.0, werkzeug==3.1.3, wheel==0.45.1, widgetsnbextension==4.0.13, wrapt==1.17.2, xatlas==0.0.10, xformers==0.0.31
[Trellis Server] activators BashActivator,BatchActivator,FishActivator,NushellActivator,PowerShellActivator,PythonActivator
[Progress Debug] Parsing output with globalProgressCallback set
[Trellis Server] 1 file(s) copied.
[Progress Debug] Parsing output with globalProgressCallback set
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Trellis Server] Server not running - connection failed (20 attempts): ECONNREFUSED
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
info: IPC: upload-sample-image called for: 678816fb26e14f19b76b25afbb59e87f.jpeg {"service":"user-service","timestamp":"2025-07-08 06:57:43"}
info: Successfully uploaded sample image: 1751975863782_678816fb26e14f19b76b25afbb59e87f.jpeg {"service":"user-service","timestamp":"2025-07-08 06:57:43"}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] [System Info] Python: 3.11.9   | PyTorch: 2.7.1+cu128 | CUDA: 12.8
[Progress Debug] Parsing output with globalProgressCallback set
[Progress Debug] Parsing output with globalProgressCallback set
[Progress Debug] Parsing output with globalProgressCallback set
[Trellis Server] 06:57:49 - INFO - Trellis API Server is starting up:
[Trellis Server] 06:57:49 - INFO - Touching this window will pause it.  If it happens, click inside it and press 'Enter' to unpause
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Trellis Server] Server not running - connection failed (30 attempts): ECONNREFUSED
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
info: IPC: update-sample-image called for: 1751975863782_678816fb26e14f19b76b25afbb59e87f.jpeg {"service":"user-service","timestamp":"2025-07-08 06:58:01"}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Trellis Server] Server not running - connection failed (40 attempts): ECONNREFUSED
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Trellis Server] Server not running - connection failed (50 attempts): ECONNREFUSED
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Trellis Server] Server not running - connection failed (60 attempts): ECONNREFUSED
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] [SPARSE] Backend: spconv, Attention: xformers
[Progress Debug] Parsing output with globalProgressCallback set
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Trellis Server] Server not running - connection failed (70 attempts): ECONNREFUSED
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] [SPARSE][CONV] spconv algo: native
[Progress Debug] Parsing output with globalProgressCallback set
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Trellis Server] Server not running - connection failed (80 attempts): ECONNREFUSED
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] [ATTENTION] Using backend: xformers
[Trellis Server] Please wait...
[Progress Debug] Parsing output with globalProgressCallback set
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Trellis Server] Server not running - connection failed (90 attempts): ECONNREFUSED
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Trellis Server] Server not running - connection failed (100 attempts): ECONNREFUSED
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] 07:00:11 - INFO - Initializing background removal session...
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] 07:00:13 - INFO - Background removal session initialized.
[Trellis Server] Using cache found in C:\Users\<USER>\torch\hub\facebookresearch_dinov2_main
[Trellis Server] C:\Users\<USER>\torch\hub\facebookresearch_dinov2_main\dinov2\layers\swiglu_ffn.py:43: UserWarning: xFormers is available (SwiGLU)
[Trellis Server] warnings.warn("xFormers is available (SwiGLU)")
[Trellis Server] C:\Users\<USER>\torch\hub\facebookresearch_dinov2_main\dinov2\layers\attention.py:27: UserWarning: xFormers is available (Attention)
[Trellis Server] warnings.warn("xFormers is available (Attention)")
[Trellis Server] C:\Users\<USER>\torch\hub\facebookresearch_dinov2_main\dinov2\layers\block.py:33: UserWarning: xFormers is available (Block)
[Trellis Server] warnings.warn("xFormers is available (Block)")
[Trellis Server] 07:00:14 - INFO - using MLP layer as FFN
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] used precision: 'half'.  Loading...
[Progress Debug] Parsing output with globalProgressCallback set
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Trellis Server] Server not running - connection failed (110 attempts): ECONNREFUSED
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'trellis',
  progress: 0,
  message: 'Waiting for Trellis server to start...',
  description: 'Waiting for Trellis server to start...'
}
[Progress Debug] Parsing output with globalProgressCallback set
[Trellis Server] 07:00:48 - INFO - Trellis API version 1.0.1
[Trellis Server] 07:00:48 - INFO - Trellis API Server is active and listening on 127.0.0.1:7960
[Trellis Server] 07:00:48 - INFO - Now in StableProjectorz, enter the 3D mode, click on the connection button and enter 127.0.0.1:7960
[Progress Debug] Parsing output with globalProgressCallback set
[Trellis Server] Checking if server is running on 127.0.0.1:7960
[Trellis Server] Server is running - connection successful
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'preprocessing',
  progress: 0,
  message: 'Loading image and initializing pipeline',
  description: 'Loading image and initializing pipeline'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'preprocessing',
  progress: 0,
  message: 'Loading image and initializing pipeline',
  description: 'Loading image and initializing pipeline'
}
[Trellis Server] Sending POST request to: http://127.0.0.1:7960/generate_no_preview
[Trellis Server] Form data keys: [ '0', '1', '2' ]
[Progress Debug] Parsing output with globalProgressCallback set
[Trellis Server] 07:00:50 - INFO - Client asked to generate with no previews
[Trellis Server] Sampling:   0%|          | 0/12 [00:00<?, ?it/s]
[Trellis Server] Sampling:   8%|8         | 1/12 [00:00<00:04,  2.21it/s]
[Trellis Server] Sampling:  17%|#6        | 2/12 [00:00<00:04,  2.00it/s]
[Trellis Server] Sampling:  25%|##5       | 3/12 [00:01<00:04,  1.89it/s]
[Trellis Server] Sampling:  33%|###3      | 4/12 [00:02<00:04,  1.88it/s]
[Trellis Server] Sampling:  42%|####1     | 5/12 [00:02<00:03,  1.89it/s]
[Trellis Server] Sampling:  50%|#####     | 6/12 [00:03<00:03,  1.88it/s]
[Trellis Server] Sampling:  58%|#####8    | 7/12 [00:03<00:02,  1.87it/s]
[Trellis Server] Sampling:  67%|######6   | 8/12 [00:04<00:02,  1.87it/s]
[Trellis Server] Sampling:  75%|#######5  | 9/12 [00:04<00:01,  1.86it/s]
[Trellis Server] Sampling:  83%|########3 | 10/12 [00:05<00:01,  1.87it/s]
[Trellis Server] Sampling:  92%|#########1| 11/12 [00:05<00:00,  2.19it/s]
Sampling: 100%|##########| 12/12 [00:05<00:00,  2.06it/s]00:00,  2.51it/s]
[Trellis Server] Sampling:   0%|          | 0/12 [00:00<?, ?it/s]
[Trellis Server] Sampling:   8%|8         | 1/12 [00:00<00:10,  1.10it/s]
[Trellis Server] Sampling:  17%|#6        | 2/12 [00:01<00:07,  1.35it/s]
[Trellis Server] Sampling:  25%|##5       | 3/12 [00:02<00:06,  1.46it/s]
[Trellis Server] Sampling:  33%|###3      | 4/12 [00:02<00:05,  1.51it/s]
[Trellis Server] Sampling:  42%|####1     | 5/12 [00:03<00:04,  1.54it/s]
[Trellis Server] Sampling:  50%|#####     | 6/12 [00:04<00:03,  1.56it/s]
[Trellis Server] Sampling:  58%|#####8    | 7/12 [00:04<00:03,  1.58it/s]
[Trellis Server] Sampling:  67%|######6   | 8/12 [00:05<00:02,  1.58it/s]
[Trellis Server] Sampling:  75%|#######5  | 9/12 [00:05<00:01,  1.59it/s]
[Trellis Server] Sampling:  83%|########3 | 10/12 [00:06<00:01,  1.59it/s]
[Trellis Server] Sampling:  92%|#########1| 11/12 [00:06<00:00,  1.88it/s]
Sampling: 100%|##########| 12/12 [00:07<00:00,  1.68it/s]00:00,  2.15it/s]
[Trellis Server] 07:01:08 - INFO - Decoding the SLAT, please wait...
[Trellis Server] Before postprocess: 355228 vertices, 710520 faces
[Progress Debug] Parsing output with globalProgressCallback set
Decimating Mesh:   0%|          [00:00<?]
[Trellis Server] Decimating Mesh:  10%|#         [00:00<00:03]
[Trellis Server] Decimating Mesh:  15%|#5        [00:00<00:03]
[Trellis Server] Decimating Mesh:  20%|##        [00:00<00:03]
[Trellis Server] Decimating Mesh:  25%|##4       [00:01<00:03]
[Trellis Server] Decimating Mesh:  27%|##6       [00:01<00:03]
[Trellis Server] Decimating Mesh:  29%|##9       [00:01<00:03]
[Trellis Server] Decimating Mesh:  31%|###1      [00:01<00:03]
[Trellis Server] Decimating Mesh:  34%|###3      [00:01<00:04]
[Trellis Server] Decimating Mesh:  36%|###5      [00:01<00:04]
[Trellis Server] Decimating Mesh:  38%|###8      [00:02<00:04]
[Trellis Server] Decimating Mesh:  40%|####      [00:02<00:04]
[Trellis Server] Decimating Mesh:  43%|####2     [00:02<00:04]
[Trellis Server] Decimating Mesh:  45%|####4     [00:02<00:03]
[Trellis Server] Decimating Mesh:  47%|####7     [00:02<00:03]
[Trellis Server] Decimating Mesh:  49%|####9     [00:02<00:03]
[Trellis Server] Decimating Mesh:  52%|#####1    [00:03<00:03]
[Trellis Server] Decimating Mesh:  54%|#####3    [00:03<00:03]
[Trellis Server] Decimating Mesh:  56%|#####6    [00:03<00:03]
[Trellis Server] Decimating Mesh:  58%|#####8    [00:03<00:02]
[Trellis Server] Decimating Mesh:  61%|######    [00:03<00:02]
[Trellis Server] Decimating Mesh:  63%|######2   [00:03<00:02]
[Trellis Server] Decimating Mesh:  65%|######5   [00:04<00:02]
[Trellis Server] Decimating Mesh:  67%|######7   [00:04<00:02]
[Trellis Server] Decimating Mesh:  70%|######9   [00:04<00:02]
[Trellis Server] Decimating Mesh:  72%|#######1  [00:04<00:02]
[Trellis Server] Decimating Mesh:  74%|#######4  [00:04<00:01]
[Trellis Server] Decimating Mesh:  76%|#######6  [00:04<00:01]
[Trellis Server] Decimating Mesh:  79%|#######8  [00:05<00:01]
[Trellis Server] Decimating Mesh:  81%|########  [00:05<00:01]
[Trellis Server] Decimating Mesh:  83%|########3 [00:05<00:01]
[Trellis Server] Decimating Mesh:  85%|########5 [00:05<00:01]
[Trellis Server] Decimating Mesh:  88%|########7 [00:05<00:00]
[Trellis Server] Decimating Mesh:  90%|########9 [00:05<00:00]
[Trellis Server] Decimating Mesh:  92%|#########2[00:05<00:00]
[Trellis Server] Decimating Mesh:  94%|#########4[00:06<00:00]
Decimating Mesh: 100%|##########[00:06<00:00]####[00:06<00:00]
[Trellis Server] Decimating Mesh: 100%|##########[00:06<00:00]
[Trellis Server] After decimate: 17725 vertices, 35526 faces
[Progress Debug] Parsing output with globalProgressCallback set
[Trellis Server] Rasterizing:   0%|          | 0/1000 [00:00<?, ?it/s]
[Trellis Server] Rasterizing:   2%|2         | 22/1000 [00:00<00:04, 215.69it/s]
[Trellis Server] Rasterizing:   5%|5         | 53/1000 [00:00<00:03, 267.59it/s]
[Trellis Server] Rasterizing:   8%|8         | 84/1000 [00:00<00:03, 285.46it/s]
[Trellis Server] Rasterizing:  12%|#1        | 115/1000 [00:00<00:03, 293.91it/s]
[Trellis Server] Rasterizing:  15%|#4        | 146/1000 [00:00<00:02, 298.59it/s]
[Trellis Server] Rasterizing:  18%|#7        | 177/1000 [00:00<00:02, 300.40it/s]
[Trellis Server] Rasterizing:  21%|##        | 208/1000 [00:00<00:02, 300.60it/s]
[Trellis Server] Rasterizing:  24%|##3       | 239/1000 [00:00<00:02, 299.78it/s]
[Trellis Server] Rasterizing:  27%|##6       | 269/1000 [00:00<00:02, 296.23it/s]
[Trellis Server] Rasterizing:  30%|##9       | 299/1000 [00:01<00:02, 294.69it/s]
[Trellis Server] Rasterizing:  33%|###2      | 329/1000 [00:01<00:02, 296.27it/s]
[Trellis Server] Rasterizing:  36%|###5      | 359/1000 [00:01<00:02, 296.51it/s]
[Trellis Server] Rasterizing:  39%|###9      | 390/1000 [00:01<00:02, 298.75it/s]
[Trellis Server] Rasterizing:  42%|####2     | 421/1000 [00:01<00:01, 300.31it/s]
[Trellis Server] Rasterizing:  45%|####5     | 452/1000 [00:01<00:01, 301.39it/s]
[Trellis Server] Rasterizing:  48%|####8     | 483/1000 [00:01<00:01, 303.06it/s]
[Trellis Server] Rasterizing:  51%|#####1    | 514/1000 [00:01<00:01, 302.43it/s]
[Trellis Server] Rasterizing:  55%|#####4    | 545/1000 [00:01<00:01, 299.36it/s]
[Trellis Server] Rasterizing:  57%|#####7    | 575/1000 [00:01<00:01, 287.71it/s]
[Trellis Server] Rasterizing:  60%|######    | 604/1000 [00:02<00:01, 286.74it/s]
[Trellis Server] Rasterizing:  63%|######3   | 633/1000 [00:02<00:01, 286.04it/s]
[Trellis Server] Rasterizing:  66%|######6   | 663/1000 [00:02<00:01, 289.35it/s]
[Trellis Server] Rasterizing:  69%|######9   | 694/1000 [00:02<00:01, 292.22it/s]
[Trellis Server] Rasterizing:  72%|#######2  | 725/1000 [00:02<00:00, 295.71it/s]
[Trellis Server] Rasterizing:  76%|#######5  | 756/1000 [00:02<00:00, 297.29it/s]
[Trellis Server] Rasterizing:  79%|#######8  | 787/1000 [00:02<00:00, 298.39it/s]
[Trellis Server] Rasterizing:  82%|########1 | 818/1000 [00:02<00:00, 300.92it/s]
[Trellis Server] Rasterizing:  85%|########4 | 849/1000 [00:02<00:00, 296.58it/s]
[Trellis Server] Rasterizing:  88%|########7 | 879/1000 [00:02<00:00, 294.15it/s]
[Trellis Server] Rasterizing:  91%|######### | 909/1000 [00:03<00:00, 294.15it/s]
[Trellis Server] Rasterizing:  94%|#########3| 939/1000 [00:03<00:00, 295.00it/s]
[Trellis Server] Rasterizing:  97%|#########7| 970/1000 [00:03<00:00, 297.68it/s]
[Trellis Server] Rasterizing: 100%|##########| 1000/1000 [00:03<00:00, 295.20it/s]
[Trellis Server] Found 16000 invisible faces
[Progress Debug] Parsing output with globalProgressCallback set
[Trellis Server] Dual graph: 53289 edges
[Progress Debug] Parsing output with globalProgressCallback set
[Trellis Server] Mincut solved, start checking the cut
[Progress Debug] Parsing output with globalProgressCallback set
[Trellis Server] Removed 21431 faces by mincut
[Progress Debug] Parsing output with globalProgressCallback set
[Trellis Server] Mesh cleaned with pymeshfix: 7161 vertices, 14318 faces
[Progress Debug] Parsing output with globalProgressCallback set
[Trellis Server] N:\3D AI Studio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\code\trellis\utils\postprocessing_utils.py:216: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.detach().clone() or sourceTensor.detach().clone().requires_grad_(True), rather than torch.tensor(sourceTensor).
[Trellis Server] verts = torch.tensor(verts, device='cuda', dtype=torch.float32)
[Trellis Server] N:\3D AI Studio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\code\trellis\utils\postprocessing_utils.py:217: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.detach().clone() or sourceTensor.detach().clone().requires_grad_(True), rather than torch.tensor(sourceTensor).
[Trellis Server] faces = torch.tensor(faces, device='cuda', dtype=torch.int32)
[Trellis Server] After remove invisible faces: 7161 vertices, 14318 faces
[Progress Debug] Parsing output with globalProgressCallback set
[Trellis Server] Rendering: 0it [00:00, ?it/s]
[Trellis Server] Rendering: 1it [00:00,  4.70it/s]
[Trellis Server] Rendering: 5it [00:00, 16.86it/s]
[Trellis Server] Rendering: 8it [00:00, 21.10it/s]
[Trellis Server] Rendering: 12it [00:00, 24.81it/s]
[Trellis Server] Rendering: 15it [00:00, 25.89it/s]
[Trellis Server] Rendering: 19it [00:00, 27.09it/s]
[Trellis Server] Rendering: 23it [00:00, 28.37it/s]
[Trellis Server] Rendering: 26it [00:01, 28.58it/s]
[Trellis Server] Rendering: 30it [00:01, 29.75it/s]
[Trellis Server] Rendering: 34it [00:01, 29.86it/s]
[Trellis Server] Rendering: 38it [00:01, 30.07it/s]
[Trellis Server] Rendering: 42it [00:01, 30.07it/s]
[Trellis Server] Rendering: 46it [00:01, 26.12it/s]
[Trellis Server] Rendering: 49it [00:01, 25.02it/s]
[Trellis Server] Rendering: 52it [00:02, 25.46it/s]
[Trellis Server] Rendering: 55it [00:02, 25.59it/s]
[Trellis Server] Rendering: 59it [00:02, 26.86it/s]
[Trellis Server] Rendering: 63it [00:02, 28.16it/s]
[Trellis Server] Rendering: 66it [00:02, 28.13it/s]
[Trellis Server] Rendering: 70it [00:02, 28.67it/s]
[Trellis Server] Rendering: 73it [00:02, 28.94it/s]
[Trellis Server] Rendering: 76it [00:02, 29.06it/s]
[Trellis Server] Rendering: 80it [00:02, 29.63it/s]
[Trellis Server] Rendering: 83it [00:03, 29.65it/s]
[Trellis Server] Rendering: 87it [00:03, 30.40it/s]
[Trellis Server] Rendering: 91it [00:03, 30.37it/s]
[Trellis Server] Rendering: 95it [00:03, 29.99it/s]
[Trellis Server] Rendering: 98it [00:03, 29.99it/s]
[Trellis Server] Rendering: 100it [00:03, 27.50it/s]
[Trellis Server] Texture baking (opt): UV:   0%|          | 0/100 [00:00<?, ?it/s]
[Trellis Server] Texture baking (opt): UV:  39%|###9      | 39/100 [00:00<00:00, 382.34it/s]
[Trellis Server] Texture baking (opt): UV:  78%|#######8  | 78/100 [00:00<00:00, 319.67it/s]
[Trellis Server] Texture baking (opt): UV: 100%|##########| 100/100 [00:00<00:00, 317.46it/s]
[Trellis Server] Texture baking (opt): optimizing:   0%|          | 0/2500 [00:00<?, ?it/s]
Texture baking (opt): optimizing:   0%|          | 1/2500 [00:00<06:54,  6.02it/s, loss=0.084]ss=0.084]
[Trellis Server] Texture baking (opt): optimizing:   0%|          | 1/2500 [00:00<06:54,  6.02it/s, loss=0.0766]
[Trellis Server] Texture baking (opt): optimizing:   0%|          | 2/2500 [00:00<06:54,  6.02it/s, loss=0.159]
[Trellis Server] Texture baking (opt): optimizing:   0%|          | 3/2500 [00:00<06:54,  6.02it/s, loss=0.0835]
[Trellis Server] Texture baking (opt): optimizing:   0%|          | 4/2500 [00:00<06:54,  6.02it/s, loss=0.124]
[Trellis Server] Texture baking (opt): optimizing:   0%|          | 5/2500 [00:00<06:54,  6.02it/s, loss=0.0632]
[Trellis Server] Texture baking (opt): optimizing:   0%|          | 6/2500 [00:00<06:54,  6.02it/s, loss=0.0683]
[Trellis Server] Texture baking (opt): optimizing:   0%|          | 7/2500 [00:00<06:53,  6.02it/s, loss=0.0599]
[Trellis Server] Texture baking (opt): optimizing:   0%|          | 8/2500 [00:00<06:53,  6.02it/s, loss=0.144]
Texture baking (opt): optimizing:   0%|          | 10/2500 [00:00<00:56, 43.84it/s, loss=0.0603]/s, loss=0.0603]
[Trellis Server] Texture baking (opt): optimizing:   0%|          | 10/2500 [00:00<00:56, 43.84it/s, loss=0.0294]
[Trellis Server] Texture baking (opt): optimizing:   0%|          | 11/2500 [00:00<00:56, 43.84it/s, loss=0.0555]
[Trellis Server] Texture baking (opt): optimizing:   0%|          | 12/2500 [00:00<00:56, 43.84it/s, loss=0.0809]
[Trellis Server] Texture baking (opt): optimizing:   1%|          | 13/2500 [00:00<00:56, 43.84it/s, loss=0.065]
[Trellis Server] Texture baking (opt): optimizing:   1%|          | 14/2500 [00:00<00:56, 43.84it/s, loss=0.0722]
[Trellis Server] Texture baking (opt): optimizing:   1%|          | 15/2500 [00:00<00:56, 43.84it/s, loss=0.0881]
[Trellis Server] Texture baking (opt): optimizing:   1%|          | 16/2500 [00:00<00:56, 43.84it/s, loss=0.0265]
[Trellis Server] Texture baking (opt): optimizing:   1%|          | 17/2500 [00:00<00:56, 43.84it/s, loss=0.053]
[Trellis Server] Texture baking (opt): optimizing:   1%|          | 18/2500 [00:00<00:56, 43.84it/s, loss=0.0983]
Texture baking (opt): optimizing:   1%|          | 20/2500 [00:00<00:37, 65.37it/s, loss=0.0616]t/s, loss=0.0616]
[Trellis Server] Texture baking (opt): optimizing:   1%|          | 20/2500 [00:00<00:37, 65.37it/s, loss=0.0326]
[Trellis Server] Texture baking (opt): optimizing:   1%|          | 21/2500 [00:00<00:37, 65.37it/s, loss=0.0604]
[Trellis Server] Texture baking (opt): optimizing:   1%|          | 22/2500 [00:00<00:37, 65.37it/s, loss=0.0769]
[Trellis Server] Texture baking (opt): optimizing:   1%|          | 23/2500 [00:00<00:37, 65.37it/s, loss=0.081]
[Trellis Server] Texture baking (opt): optimizing:   1%|          | 24/2500 [00:00<00:37, 65.37it/s, loss=0.085]
[Trellis Server] Texture baking (opt): optimizing:   1%|1         | 25/2500 [00:00<00:37, 65.37it/s, loss=0.019]
[Trellis Server] Texture baking (opt): optimizing:   1%|1         | 26/2500 [00:00<00:37, 65.37it/s, loss=0.0255]
[Trellis Server] Texture baking (opt): optimizing:   1%|1         | 27/2500 [00:00<00:37, 65.37it/s, loss=0.02]
[Trellis Server] Texture baking (opt): optimizing:   1%|1         | 28/2500 [00:00<00:37, 65.37it/s, loss=0.0688]
Texture baking (opt): optimizing:   1%|1         | 30/2500 [00:00<00:32, 76.92it/s, loss=0.0674]t/s, loss=0.0674]
[Trellis Server] Texture baking (opt): optimizing:   1%|1         | 30/2500 [00:00<00:32, 76.92it/s, loss=0.0487]
[Trellis Server] Texture baking (opt): optimizing:   1%|1         | 31/2500 [00:00<00:32, 76.92it/s, loss=0.0285]
[Trellis Server] Texture baking (opt): optimizing:   1%|1         | 32/2500 [00:00<00:32, 76.92it/s, loss=0.0525]
[Trellis Server] Texture baking (opt): optimizing:   1%|1         | 33/2500 [00:00<00:32, 76.92it/s, loss=0.0475]
[Trellis Server] Texture baking (opt): optimizing:   1%|1         | 34/2500 [00:00<00:32, 76.92it/s, loss=0.0411]
[Trellis Server] Texture baking (opt): optimizing:   1%|1         | 35/2500 [00:00<00:32, 76.92it/s, loss=0.0198]
[Trellis Server] Texture baking (opt): optimizing:   1%|1         | 36/2500 [00:00<00:32, 76.92it/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:   1%|1         | 37/2500 [00:00<00:32, 76.92it/s, loss=0.0105]
[Trellis Server] Texture baking (opt): optimizing:   2%|1         | 38/2500 [00:00<00:32, 76.92it/s, loss=0.0139]
Texture baking (opt): optimizing:   2%|1         | 40/2500 [00:00<00:29, 83.27it/s, loss=0.057]it/s, loss=0.057]
[Trellis Server] Texture baking (opt): optimizing:   2%|1         | 40/2500 [00:00<00:29, 83.27it/s, loss=0.0285]
[Trellis Server] Texture baking (opt): optimizing:   2%|1         | 41/2500 [00:00<00:29, 83.27it/s, loss=0.05]
[Trellis Server] Texture baking (opt): optimizing:   2%|1         | 42/2500 [00:00<00:29, 83.27it/s, loss=0.0483]
[Trellis Server] Texture baking (opt): optimizing:   2%|1         | 43/2500 [00:00<00:29, 83.27it/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:   2%|1         | 44/2500 [00:00<00:29, 83.27it/s, loss=0.0149]
[Trellis Server] Texture baking (opt): optimizing:   2%|1         | 45/2500 [00:00<00:29, 83.27it/s, loss=0.0123]
[Trellis Server] Texture baking (opt): optimizing:   2%|1         | 46/2500 [00:00<00:29, 83.27it/s, loss=0.0463]
[Trellis Server] Texture baking (opt): optimizing:   2%|1         | 47/2500 [00:00<00:29, 83.27it/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:   2%|1         | 48/2500 [00:00<00:29, 83.27it/s, loss=0.0174]
Texture baking (opt): optimizing:   2%|2         | 50/2500 [00:00<00:28, 86.63it/s, loss=0.0137]t/s, loss=0.0137]
[Trellis Server] Texture baking (opt): optimizing:   2%|2         | 50/2500 [00:00<00:28, 86.63it/s, loss=0.0444]
[Trellis Server] Texture baking (opt): optimizing:   2%|2         | 51/2500 [00:00<00:28, 86.63it/s, loss=0.0173]
[Trellis Server] Texture baking (opt): optimizing:   2%|2         | 52/2500 [00:00<00:28, 86.63it/s, loss=0.00992]
[Trellis Server] Texture baking (opt): optimizing:   2%|2         | 53/2500 [00:00<00:28, 86.63it/s, loss=0.00858]
[Trellis Server] Texture baking (opt): optimizing:   2%|2         | 54/2500 [00:00<00:28, 86.63it/s, loss=0.0432]
[Trellis Server] Texture baking (opt): optimizing:   2%|2         | 55/2500 [00:00<00:28, 86.63it/s, loss=0.0279]
[Trellis Server] Texture baking (opt): optimizing:   2%|2         | 56/2500 [00:00<00:28, 86.63it/s, loss=0.045]
[Trellis Server] Texture baking (opt): optimizing:   2%|2         | 57/2500 [00:00<00:28, 86.63it/s, loss=0.0103]
Texture baking (opt): optimizing:   2%|2         | 59/2500 [00:00<00:28, 85.29it/s, loss=0.0122]t/s, loss=0.0122]
[Trellis Server] Texture baking (opt): optimizing:   2%|2         | 59/2500 [00:00<00:28, 85.29it/s, loss=0.0179]
[Trellis Server] Texture baking (opt): optimizing:   2%|2         | 60/2500 [00:00<00:28, 85.29it/s, loss=0.0426]
[Trellis Server] Texture baking (opt): optimizing:   2%|2         | 61/2500 [00:00<00:28, 85.29it/s, loss=0.0293]
[Trellis Server] Texture baking (opt): optimizing:   2%|2         | 62/2500 [00:00<00:28, 85.29it/s, loss=0.0158]
[Trellis Server] Texture baking (opt): optimizing:   3%|2         | 63/2500 [00:00<00:28, 85.29it/s, loss=0.0319]
[Trellis Server] Texture baking (opt): optimizing:   3%|2         | 64/2500 [00:00<00:28, 85.29it/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:   3%|2         | 65/2500 [00:00<00:28, 85.29it/s, loss=0.0381]
[Trellis Server] Texture baking (opt): optimizing:   3%|2         | 66/2500 [00:00<00:28, 85.29it/s, loss=0.0316]
Texture baking (opt): optimizing:   3%|2         | 68/2500 [00:00<00:28, 83.88it/s, loss=0.0158]t/s, loss=0.0158]
[Trellis Server] Texture baking (opt): optimizing:   3%|2         | 68/2500 [00:00<00:28, 83.88it/s, loss=0.0164]
[Trellis Server] Texture baking (opt): optimizing:   3%|2         | 69/2500 [00:00<00:28, 83.88it/s, loss=0.0162]
[Trellis Server] Texture baking (opt): optimizing:   3%|2         | 70/2500 [00:00<00:28, 83.88it/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing:   3%|2         | 71/2500 [00:00<00:28, 83.88it/s, loss=0.0154]
[Trellis Server] Texture baking (opt): optimizing:   3%|2         | 72/2500 [00:00<00:28, 83.88it/s, loss=0.00939]
[Trellis Server] Texture baking (opt): optimizing:   3%|2         | 73/2500 [00:00<00:28, 83.88it/s, loss=0.0129]
[Trellis Server] Texture baking (opt): optimizing:   3%|2         | 74/2500 [00:00<00:28, 83.88it/s, loss=0.00804]
[Trellis Server] Texture baking (opt): optimizing:   3%|3         | 75/2500 [00:01<00:28, 83.88it/s, loss=0.041]
Texture baking (opt): optimizing:   3%|3         | 77/2500 [00:01<00:28, 84.76it/s, loss=0.0215]t/s, loss=0.0215]
[Trellis Server] Texture baking (opt): optimizing:   3%|3         | 77/2500 [00:01<00:28, 84.76it/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:   3%|3         | 78/2500 [00:01<00:28, 84.76it/s, loss=0.0205]
[Trellis Server] Texture baking (opt): optimizing:   3%|3         | 79/2500 [00:01<00:28, 84.76it/s, loss=0.0203]
[Trellis Server] Texture baking (opt): optimizing:   3%|3         | 80/2500 [00:01<00:28, 84.76it/s, loss=0.0254]
[Trellis Server] Texture baking (opt): optimizing:   3%|3         | 81/2500 [00:01<00:28, 84.76it/s, loss=0.0085]
[Trellis Server] Texture baking (opt): optimizing:   3%|3         | 82/2500 [00:01<00:28, 84.76it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:   3%|3         | 83/2500 [00:01<00:28, 84.76it/s, loss=0.0128]
[Trellis Server] Texture baking (opt): optimizing:   3%|3         | 84/2500 [00:01<00:28, 84.76it/s, loss=0.0306]
[Trellis Server] Texture baking (opt): optimizing:   3%|3         | 85/2500 [00:01<00:28, 84.76it/s, loss=0.0338]
Texture baking (opt): optimizing:   3%|3         | 87/2500 [00:01<00:27, 88.79it/s, loss=0.0262]t/s, loss=0.0262]
[Trellis Server] Texture baking (opt): optimizing:   3%|3         | 87/2500 [00:01<00:27, 88.79it/s, loss=0.0331]
[Trellis Server] Texture baking (opt): optimizing:   4%|3         | 88/2500 [00:01<00:27, 88.79it/s, loss=0.0197]
[Trellis Server] Texture baking (opt): optimizing:   4%|3         | 89/2500 [00:01<00:27, 88.79it/s, loss=0.0219]
[Trellis Server] Texture baking (opt): optimizing:   4%|3         | 90/2500 [00:01<00:27, 88.79it/s, loss=0.0295]
[Trellis Server] Texture baking (opt): optimizing:   4%|3         | 91/2500 [00:01<00:27, 88.79it/s, loss=0.0174]
[Trellis Server] Texture baking (opt): optimizing:   4%|3         | 92/2500 [00:01<00:27, 88.79it/s, loss=0.0245]
[Trellis Server] Texture baking (opt): optimizing:   4%|3         | 93/2500 [00:01<00:27, 88.79it/s, loss=0.0125]
[Trellis Server] Texture baking (opt): optimizing:   4%|3         | 94/2500 [00:01<00:27, 88.79it/s, loss=0.0105]
[Trellis Server] Texture baking (opt): optimizing:   4%|3         | 95/2500 [00:01<00:27, 88.79it/s, loss=0.0165]
Texture baking (opt): optimizing:   4%|3         | 97/2500 [00:01<00:26, 91.02it/s, loss=0.0175]t/s, loss=0.0175]
[Trellis Server] Texture baking (opt): optimizing:   4%|3         | 97/2500 [00:01<00:26, 91.02it/s, loss=0.0105]
[Trellis Server] Texture baking (opt): optimizing:   4%|3         | 98/2500 [00:01<00:26, 91.02it/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:   4%|3         | 99/2500 [00:01<00:26, 91.02it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:   4%|4         | 100/2500 [00:01<00:26, 91.02it/s, loss=0.0148]
[Trellis Server] Texture baking (opt): optimizing:   4%|4         | 101/2500 [00:01<00:26, 91.02it/s, loss=0.0248]
[Trellis Server] Texture baking (opt): optimizing:   4%|4         | 102/2500 [00:01<00:26, 91.02it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:   4%|4         | 103/2500 [00:01<00:26, 91.02it/s, loss=0.0221]
[Trellis Server] Texture baking (opt): optimizing:   4%|4         | 104/2500 [00:01<00:26, 91.02it/s, loss=0.0123]
[Trellis Server] Texture baking (opt): optimizing:   4%|4         | 105/2500 [00:01<00:26, 91.02it/s, loss=0.0342]
Texture baking (opt): optimizing:   4%|4         | 107/2500 [00:01<00:26, 89.96it/s, loss=0.0305]t/s, loss=0.0305]
[Trellis Server] Texture baking (opt): optimizing:   4%|4         | 107/2500 [00:01<00:26, 89.96it/s, loss=0.0239]
[Trellis Server] Texture baking (opt): optimizing:   4%|4         | 108/2500 [00:01<00:26, 89.96it/s, loss=0.0197]
[Trellis Server] Texture baking (opt): optimizing:   4%|4         | 109/2500 [00:01<00:26, 89.96it/s, loss=0.0237]
[Trellis Server] Texture baking (opt): optimizing:   4%|4         | 110/2500 [00:01<00:26, 89.96it/s, loss=0.023]
[Trellis Server] Texture baking (opt): optimizing:   4%|4         | 111/2500 [00:01<00:26, 89.96it/s, loss=0.0159]
[Trellis Server] Texture baking (opt): optimizing:   4%|4         | 112/2500 [00:01<00:26, 89.96it/s, loss=0.0239]
[Trellis Server] Texture baking (opt): optimizing:   5%|4         | 113/2500 [00:01<00:26, 89.96it/s, loss=0.0192]
[Trellis Server] Texture baking (opt): optimizing:   5%|4         | 114/2500 [00:01<00:26, 89.96it/s, loss=0.0215]
[Trellis Server] Texture baking (opt): optimizing:   5%|4         | 115/2500 [00:01<00:26, 89.96it/s, loss=0.0214]
Texture baking (opt): optimizing:   5%|4         | 117/2500 [00:01<00:25, 91.78it/s, loss=0.0171]t/s, loss=0.0171]
[Trellis Server] Texture baking (opt): optimizing:   5%|4         | 117/2500 [00:01<00:25, 91.78it/s, loss=0.0157]
[Trellis Server] Texture baking (opt): optimizing:   5%|4         | 118/2500 [00:01<00:25, 91.78it/s, loss=0.0208]
[Trellis Server] Texture baking (opt): optimizing:   5%|4         | 119/2500 [00:01<00:25, 91.78it/s, loss=0.0185]
[Trellis Server] Texture baking (opt): optimizing:   5%|4         | 120/2500 [00:01<00:25, 91.78it/s, loss=0.0221]
[Trellis Server] Texture baking (opt): optimizing:   5%|4         | 121/2500 [00:01<00:25, 91.78it/s, loss=0.0203]
[Trellis Server] Texture baking (opt): optimizing:   5%|4         | 122/2500 [00:01<00:25, 91.78it/s, loss=0.0197]
[Trellis Server] Texture baking (opt): optimizing:   5%|4         | 123/2500 [00:01<00:25, 91.78it/s, loss=0.0225]
[Trellis Server] Texture baking (opt): optimizing:   5%|4         | 124/2500 [00:01<00:25, 91.78it/s, loss=0.0166]
[Trellis Server] Texture baking (opt): optimizing:   5%|5         | 125/2500 [00:01<00:25, 91.78it/s, loss=0.0156]
[Trellis Server] Texture baking (opt): optimizing:   5%|5         | 126/2500 [00:01<00:25, 91.78it/s, loss=0.0144]
Texture baking (opt): optimizing:   5%|5         | 128/2500 [00:01<00:25, 94.32it/s, loss=0.0146]t/s, loss=0.0146]
[Trellis Server] Texture baking (opt): optimizing:   5%|5         | 128/2500 [00:01<00:25, 94.32it/s, loss=0.0154]
[Trellis Server] Texture baking (opt): optimizing:   5%|5         | 129/2500 [00:01<00:25, 94.32it/s, loss=0.0167]
[Trellis Server] Texture baking (opt): optimizing:   5%|5         | 130/2500 [00:01<00:25, 94.32it/s, loss=0.0123]
[Trellis Server] Texture baking (opt): optimizing:   5%|5         | 131/2500 [00:01<00:25, 94.32it/s, loss=0.0172]
[Trellis Server] Texture baking (opt): optimizing:   5%|5         | 132/2500 [00:01<00:25, 94.32it/s, loss=0.0166]
[Trellis Server] Texture baking (opt): optimizing:   5%|5         | 133/2500 [00:01<00:25, 94.32it/s, loss=0.0204]
[Trellis Server] Texture baking (opt): optimizing:   5%|5         | 134/2500 [00:01<00:25, 94.32it/s, loss=0.0174]
[Trellis Server] Texture baking (opt): optimizing:   5%|5         | 135/2500 [00:01<00:25, 94.32it/s, loss=0.014]
[Trellis Server] Texture baking (opt): optimizing:   5%|5         | 136/2500 [00:01<00:25, 94.32it/s, loss=0.0182]
Texture baking (opt): optimizing:   6%|5         | 138/2500 [00:01<00:24, 95.83it/s, loss=0.0148]t/s, loss=0.0148]
[Trellis Server] Texture baking (opt): optimizing:   6%|5         | 138/2500 [00:01<00:24, 95.83it/s, loss=0.0154]
[Trellis Server] Texture baking (opt): optimizing:   6%|5         | 139/2500 [00:01<00:24, 95.83it/s, loss=0.00963]
[Trellis Server] Texture baking (opt): optimizing:   6%|5         | 140/2500 [00:01<00:24, 95.83it/s, loss=0.0162]
[Trellis Server] Texture baking (opt): optimizing:   6%|5         | 141/2500 [00:01<00:24, 95.83it/s, loss=0.0169]
[Trellis Server] Texture baking (opt): optimizing:   6%|5         | 142/2500 [00:01<00:24, 95.83it/s, loss=0.0152]
[Trellis Server] Texture baking (opt): optimizing:   6%|5         | 143/2500 [00:01<00:24, 95.83it/s, loss=0.0122]
[Trellis Server] Texture baking (opt): optimizing:   6%|5         | 144/2500 [00:01<00:24, 95.83it/s, loss=0.0179]
[Trellis Server] Texture baking (opt): optimizing:   6%|5         | 145/2500 [00:01<00:24, 95.83it/s, loss=0.0205]
[Trellis Server] Texture baking (opt): optimizing:   6%|5         | 146/2500 [00:01<00:24, 95.83it/s, loss=0.0104]
[Trellis Server] Texture baking (opt): optimizing:   6%|5         | 147/2500 [00:01<00:24, 95.83it/s, loss=0.0159]
Texture baking (opt): optimizing:   6%|5         | 149/2500 [00:01<00:24, 97.12it/s, loss=0.0178]t/s, loss=0.0178]
[Trellis Server] Texture baking (opt): optimizing:   6%|5         | 149/2500 [00:01<00:24, 97.12it/s, loss=0.0179]
[Trellis Server] Texture baking (opt): optimizing:   6%|6         | 150/2500 [00:01<00:24, 97.12it/s, loss=0.0222]
[Trellis Server] Texture baking (opt): optimizing:   6%|6         | 151/2500 [00:01<00:24, 97.12it/s, loss=0.0147]
[Trellis Server] Texture baking (opt): optimizing:   6%|6         | 152/2500 [00:01<00:24, 97.12it/s, loss=0.0156]
[Trellis Server] Texture baking (opt): optimizing:   6%|6         | 153/2500 [00:01<00:24, 97.12it/s, loss=0.0156]
[Trellis Server] Texture baking (opt): optimizing:   6%|6         | 154/2500 [00:01<00:24, 97.12it/s, loss=0.014]
[Trellis Server] Texture baking (opt): optimizing:   6%|6         | 155/2500 [00:01<00:24, 97.12it/s, loss=0.0182]
[Trellis Server] Texture baking (opt): optimizing:   6%|6         | 156/2500 [00:01<00:24, 97.12it/s, loss=0.0133]
[Trellis Server] Texture baking (opt): optimizing:   6%|6         | 157/2500 [00:01<00:24, 97.12it/s, loss=0.0141]
Texture baking (opt): optimizing:   6%|6         | 159/2500 [00:01<00:24, 96.02it/s, loss=0.011]it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:   6%|6         | 159/2500 [00:01<00:24, 96.02it/s, loss=0.0118]
[Trellis Server] Texture baking (opt): optimizing:   6%|6         | 160/2500 [00:01<00:24, 96.02it/s, loss=0.0269]
[Trellis Server] Texture baking (opt): optimizing:   6%|6         | 161/2500 [00:01<00:24, 96.02it/s, loss=0.0183]
[Trellis Server] Texture baking (opt): optimizing:   6%|6         | 162/2500 [00:01<00:24, 96.02it/s, loss=0.0236]
[Trellis Server] Texture baking (opt): optimizing:   7%|6         | 163/2500 [00:01<00:24, 96.02it/s, loss=0.0208]
[Trellis Server] Texture baking (opt): optimizing:   7%|6         | 164/2500 [00:01<00:24, 96.02it/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:   7%|6         | 165/2500 [00:01<00:24, 96.02it/s, loss=0.0141]
[Trellis Server] Texture baking (opt): optimizing:   7%|6         | 166/2500 [00:01<00:24, 96.02it/s, loss=0.0107]
[Trellis Server] Texture baking (opt): optimizing:   7%|6         | 167/2500 [00:01<00:24, 96.02it/s, loss=0.0142]
[Trellis Server] Texture baking (opt): optimizing:   7%|6         | 168/2500 [00:01<00:24, 96.02it/s, loss=0.0169]
Texture baking (opt): optimizing:   7%|6         | 170/2500 [00:01<00:23, 97.24it/s, loss=0.0159]t/s, loss=0.0159]
[Trellis Server] Texture baking (opt): optimizing:   7%|6         | 170/2500 [00:01<00:23, 97.24it/s, loss=0.00945]
[Trellis Server] Texture baking (opt): optimizing:   7%|6         | 171/2500 [00:01<00:23, 97.24it/s, loss=0.00986]
[Trellis Server] Texture baking (opt): optimizing:   7%|6         | 172/2500 [00:02<00:23, 97.24it/s, loss=0.0249]
[Trellis Server] Texture baking (opt): optimizing:   7%|6         | 173/2500 [00:02<00:23, 97.24it/s, loss=0.0162]
[Trellis Server] Texture baking (opt): optimizing:   7%|6         | 174/2500 [00:02<00:23, 97.24it/s, loss=0.0151]
[Trellis Server] Texture baking (opt): optimizing:   7%|7         | 175/2500 [00:02<00:23, 97.24it/s, loss=0.0155]
[Trellis Server] Texture baking (opt): optimizing:   7%|7         | 176/2500 [00:02<00:23, 97.24it/s, loss=0.0187]
[Trellis Server] Texture baking (opt): optimizing:   7%|7         | 177/2500 [00:02<00:23, 97.24it/s, loss=0.0186]
[Trellis Server] Texture baking (opt): optimizing:   7%|7         | 178/2500 [00:02<00:23, 97.24it/s, loss=0.0166]
Texture baking (opt): optimizing:   7%|7         | 180/2500 [00:02<00:24, 95.58it/s, loss=0.0217]t/s, loss=0.0217]
[Trellis Server] Texture baking (opt): optimizing:   7%|7         | 180/2500 [00:02<00:24, 95.58it/s, loss=0.0317]
[Trellis Server] Texture baking (opt): optimizing:   7%|7         | 181/2500 [00:02<00:24, 95.58it/s, loss=0.0163]
[Trellis Server] Texture baking (opt): optimizing:   7%|7         | 182/2500 [00:02<00:24, 95.58it/s, loss=0.0175]
[Trellis Server] Texture baking (opt): optimizing:   7%|7         | 183/2500 [00:02<00:24, 95.58it/s, loss=0.0215]
[Trellis Server] Texture baking (opt): optimizing:   7%|7         | 184/2500 [00:02<00:24, 95.58it/s, loss=0.017]
[Trellis Server] Texture baking (opt): optimizing:   7%|7         | 185/2500 [00:02<00:24, 95.58it/s, loss=0.0159]
[Trellis Server] Texture baking (opt): optimizing:   7%|7         | 186/2500 [00:02<00:24, 95.58it/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:   7%|7         | 187/2500 [00:02<00:24, 95.58it/s, loss=0.0156]
[Trellis Server] Texture baking (opt): optimizing:   8%|7         | 188/2500 [00:02<00:24, 95.58it/s, loss=0.012]
Texture baking (opt): optimizing:   8%|7         | 190/2500 [00:02<00:24, 94.68it/s, loss=0.0073]t/s, loss=0.0073]
[Trellis Server] Texture baking (opt): optimizing:   8%|7         | 190/2500 [00:02<00:24, 94.68it/s, loss=0.0086]
[Trellis Server] Texture baking (opt): optimizing:   8%|7         | 191/2500 [00:02<00:24, 94.68it/s, loss=0.0149]
[Trellis Server] Texture baking (opt): optimizing:   8%|7         | 192/2500 [00:02<00:24, 94.68it/s, loss=0.0164]
[Trellis Server] Texture baking (opt): optimizing:   8%|7         | 193/2500 [00:02<00:24, 94.68it/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:   8%|7         | 194/2500 [00:02<00:24, 94.68it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:   8%|7         | 195/2500 [00:02<00:24, 94.68it/s, loss=0.0155]
[Trellis Server] Texture baking (opt): optimizing:   8%|7         | 196/2500 [00:02<00:24, 94.68it/s, loss=0.0138]
[Trellis Server] Texture baking (opt): optimizing:   8%|7         | 197/2500 [00:02<00:24, 94.68it/s, loss=0.0136]
[Trellis Server] Texture baking (opt): optimizing:   8%|7         | 198/2500 [00:02<00:24, 94.68it/s, loss=0.018]
[Trellis Server] Texture baking (opt): optimizing:   8%|7         | 199/2500 [00:02<00:24, 94.68it/s, loss=0.00976]
Texture baking (opt): optimizing:   8%|8         | 201/2500 [00:02<00:23, 96.57it/s, loss=0.0147]t/s, loss=0.0147]
[Trellis Server] Texture baking (opt): optimizing:   8%|8         | 201/2500 [00:02<00:23, 96.57it/s, loss=0.0095]
[Trellis Server] Texture baking (opt): optimizing:   8%|8         | 202/2500 [00:02<00:23, 96.57it/s, loss=0.0153]
[Trellis Server] Texture baking (opt): optimizing:   8%|8         | 203/2500 [00:02<00:23, 96.57it/s, loss=0.0212]
[Trellis Server] Texture baking (opt): optimizing:   8%|8         | 204/2500 [00:02<00:23, 96.57it/s, loss=0.00882]
[Trellis Server] Texture baking (opt): optimizing:   8%|8         | 205/2500 [00:02<00:23, 96.57it/s, loss=0.0191]
[Trellis Server] Texture baking (opt): optimizing:   8%|8         | 206/2500 [00:02<00:23, 96.57it/s, loss=0.0172]
[Trellis Server] Texture baking (opt): optimizing:   8%|8         | 207/2500 [00:02<00:23, 96.57it/s, loss=0.0165]
[Trellis Server] Texture baking (opt): optimizing:   8%|8         | 208/2500 [00:02<00:23, 96.57it/s, loss=0.022]
[Trellis Server] Texture baking (opt): optimizing:   8%|8         | 209/2500 [00:02<00:23, 96.57it/s, loss=0.0202]
Texture baking (opt): optimizing:   8%|8         | 211/2500 [00:02<00:24, 95.37it/s, loss=0.0151]t/s, loss=0.0151]
[Trellis Server] Texture baking (opt): optimizing:   8%|8         | 211/2500 [00:02<00:24, 95.37it/s, loss=0.017]
[Trellis Server] Texture baking (opt): optimizing:   8%|8         | 212/2500 [00:02<00:23, 95.37it/s, loss=0.0135]
[Trellis Server] Texture baking (opt): optimizing:   9%|8         | 213/2500 [00:02<00:23, 95.37it/s, loss=0.0167]
[Trellis Server] Texture baking (opt): optimizing:   9%|8         | 214/2500 [00:02<00:23, 95.37it/s, loss=0.00991]
[Trellis Server] Texture baking (opt): optimizing:   9%|8         | 215/2500 [00:02<00:23, 95.37it/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:   9%|8         | 216/2500 [00:02<00:23, 95.37it/s, loss=0.0204]
[Trellis Server] Texture baking (opt): optimizing:   9%|8         | 217/2500 [00:02<00:23, 95.37it/s, loss=0.0158]
[Trellis Server] Texture baking (opt): optimizing:   9%|8         | 218/2500 [00:02<00:23, 95.37it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:   9%|8         | 219/2500 [00:02<00:23, 95.37it/s, loss=0.00942]
Texture baking (opt): optimizing:   9%|8         | 221/2500 [00:02<00:24, 91.99it/s, loss=0.0116]t/s, loss=0.0116]
[Trellis Server] Texture baking (opt): optimizing:   9%|8         | 221/2500 [00:02<00:24, 91.99it/s, loss=0.0189]
[Trellis Server] Texture baking (opt): optimizing:   9%|8         | 222/2500 [00:02<00:24, 91.99it/s, loss=0.0288]
[Trellis Server] Texture baking (opt): optimizing:   9%|8         | 223/2500 [00:02<00:24, 91.99it/s, loss=0.0145]
[Trellis Server] Texture baking (opt): optimizing:   9%|8         | 224/2500 [00:02<00:24, 91.99it/s, loss=0.0162]
[Trellis Server] Texture baking (opt): optimizing:   9%|9         | 225/2500 [00:02<00:24, 91.99it/s, loss=0.0131]
[Trellis Server] Texture baking (opt): optimizing:   9%|9         | 226/2500 [00:02<00:24, 91.99it/s, loss=0.0254]
[Trellis Server] Texture baking (opt): optimizing:   9%|9         | 227/2500 [00:02<00:24, 91.99it/s, loss=0.0143]
[Trellis Server] Texture baking (opt): optimizing:   9%|9         | 228/2500 [00:02<00:24, 91.99it/s, loss=0.0116]
[Trellis Server] Texture baking (opt): optimizing:   9%|9         | 229/2500 [00:02<00:24, 91.99it/s, loss=0.0197]
Texture baking (opt): optimizing:   9%|9         | 231/2500 [00:02<00:24, 92.93it/s, loss=0.0175]t/s, loss=0.0175]
[Trellis Server] Texture baking (opt): optimizing:   9%|9         | 231/2500 [00:02<00:24, 92.93it/s, loss=0.0191]
[Trellis Server] Texture baking (opt): optimizing:   9%|9         | 232/2500 [00:02<00:24, 92.93it/s, loss=0.0142]
[Trellis Server] Texture baking (opt): optimizing:   9%|9         | 233/2500 [00:02<00:24, 92.93it/s, loss=0.0135]
[Trellis Server] Texture baking (opt): optimizing:   9%|9         | 234/2500 [00:02<00:24, 92.93it/s, loss=0.0203]
[Trellis Server] Texture baking (opt): optimizing:   9%|9         | 235/2500 [00:02<00:24, 92.93it/s, loss=0.0122]
[Trellis Server] Texture baking (opt): optimizing:   9%|9         | 236/2500 [00:02<00:24, 92.93it/s, loss=0.0207]
[Trellis Server] Texture baking (opt): optimizing:   9%|9         | 237/2500 [00:02<00:24, 92.93it/s, loss=0.0178]
[Trellis Server] Texture baking (opt): optimizing:  10%|9         | 238/2500 [00:02<00:24, 92.93it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  10%|9         | 239/2500 [00:02<00:24, 92.93it/s, loss=0.0179]
Texture baking (opt): optimizing:  10%|9         | 241/2500 [00:02<00:23, 94.38it/s, loss=0.0129]t/s, loss=0.0129]
[Trellis Server] Texture baking (opt): optimizing:  10%|9         | 241/2500 [00:02<00:23, 94.38it/s, loss=0.0145]
[Trellis Server] Texture baking (opt): optimizing:  10%|9         | 242/2500 [00:02<00:23, 94.38it/s, loss=0.0176]
[Trellis Server] Texture baking (opt): optimizing:  10%|9         | 243/2500 [00:02<00:23, 94.38it/s, loss=0.0118]
[Trellis Server] Texture baking (opt): optimizing:  10%|9         | 244/2500 [00:02<00:23, 94.38it/s, loss=0.0123]
[Trellis Server] Texture baking (opt): optimizing:  10%|9         | 245/2500 [00:02<00:23, 94.38it/s, loss=0.0223]
[Trellis Server] Texture baking (opt): optimizing:  10%|9         | 246/2500 [00:02<00:23, 94.38it/s, loss=0.0243]
[Trellis Server] Texture baking (opt): optimizing:  10%|9         | 247/2500 [00:02<00:23, 94.38it/s, loss=0.0218]
[Trellis Server] Texture baking (opt): optimizing:  10%|9         | 248/2500 [00:02<00:23, 94.38it/s, loss=0.0217]
[Trellis Server] Texture baking (opt): optimizing:  10%|9         | 249/2500 [00:02<00:23, 94.38it/s, loss=0.0104]
[Trellis Server] Texture baking (opt): optimizing:  10%|#         | 250/2500 [00:02<00:23, 94.38it/s, loss=0.0163]
[Trellis Server] Texture baking (opt): optimizing:  10%|#         | 251/2500 [00:02<00:23, 93.85it/s, loss=0.0163]
[Trellis Server] Texture baking (opt): optimizing:  10%|#         | 251/2500 [00:02<00:23, 93.85it/s, loss=0.0173]
[Trellis Server] Texture baking (opt): optimizing:  10%|#         | 252/2500 [00:02<00:23, 93.85it/s, loss=0.0185]
[Trellis Server] Texture baking (opt): optimizing:  10%|#         | 253/2500 [00:02<00:23, 93.85it/s, loss=0.0168]
[Trellis Server] Texture baking (opt): optimizing:  10%|#         | 254/2500 [00:02<00:23, 93.85it/s, loss=0.0155]
[Trellis Server] Texture baking (opt): optimizing:  10%|#         | 255/2500 [00:02<00:23, 93.85it/s, loss=0.0171]
[Trellis Server] Texture baking (opt): optimizing:  10%|#         | 256/2500 [00:02<00:23, 93.85it/s, loss=0.0173]
[Trellis Server] Texture baking (opt): optimizing:  10%|#         | 257/2500 [00:02<00:23, 93.85it/s, loss=0.0206]
[Trellis Server] Texture baking (opt): optimizing:  10%|#         | 258/2500 [00:02<00:23, 93.85it/s, loss=0.0151]
[Trellis Server] Texture baking (opt): optimizing:  10%|#         | 259/2500 [00:02<00:23, 93.85it/s, loss=0.00498]
Texture baking (opt): optimizing:  10%|#         | 261/2500 [00:02<00:24, 92.95it/s, loss=0.0148]t/s, loss=0.0148]
[Trellis Server] Texture baking (opt): optimizing:  10%|#         | 261/2500 [00:02<00:24, 92.95it/s, loss=0.0152]
[Trellis Server] Texture baking (opt): optimizing:  10%|#         | 262/2500 [00:02<00:24, 92.95it/s, loss=0.0147]
[Trellis Server] Texture baking (opt): optimizing:  11%|#         | 263/2500 [00:02<00:24, 92.95it/s, loss=0.0123]
[Trellis Server] Texture baking (opt): optimizing:  11%|#         | 264/2500 [00:02<00:24, 92.95it/s, loss=0.0176]
[Trellis Server] Texture baking (opt): optimizing:  11%|#         | 265/2500 [00:03<00:24, 92.95it/s, loss=0.00888]
[Trellis Server] Texture baking (opt): optimizing:  11%|#         | 266/2500 [00:03<00:24, 92.95it/s, loss=0.0164]
[Trellis Server] Texture baking (opt): optimizing:  11%|#         | 267/2500 [00:03<00:24, 92.95it/s, loss=0.0146]
[Trellis Server] Texture baking (opt): optimizing:  11%|#         | 268/2500 [00:03<00:24, 92.95it/s, loss=0.0222]
[Trellis Server] Texture baking (opt): optimizing:  11%|#         | 269/2500 [00:03<00:24, 92.95it/s, loss=0.0348]
Texture baking (opt): optimizing:  11%|#         | 271/2500 [00:03<00:23, 93.36it/s, loss=0.0175]t/s, loss=0.0175]
[Trellis Server] Texture baking (opt): optimizing:  11%|#         | 271/2500 [00:03<00:23, 93.36it/s, loss=0.0203]
[Trellis Server] Texture baking (opt): optimizing:  11%|#         | 272/2500 [00:03<00:23, 93.36it/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  11%|#         | 273/2500 [00:03<00:23, 93.36it/s, loss=0.0158]
[Trellis Server] Texture baking (opt): optimizing:  11%|#         | 274/2500 [00:03<00:23, 93.36it/s, loss=0.0145]
[Trellis Server] Texture baking (opt): optimizing:  11%|#1        | 275/2500 [00:03<00:23, 93.36it/s, loss=0.0204]
[Trellis Server] Texture baking (opt): optimizing:  11%|#1        | 276/2500 [00:03<00:23, 93.36it/s, loss=0.0179]
[Trellis Server] Texture baking (opt): optimizing:  11%|#1        | 277/2500 [00:03<00:23, 93.36it/s, loss=0.0165]
[Trellis Server] Texture baking (opt): optimizing:  11%|#1        | 278/2500 [00:03<00:23, 93.36it/s, loss=0.0172]
[Trellis Server] Texture baking (opt): optimizing:  11%|#1        | 279/2500 [00:03<00:23, 93.36it/s, loss=0.011]
Texture baking (opt): optimizing:  11%|#1        | 281/2500 [00:03<00:24, 92.36it/s, loss=0.0112]t/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  11%|#1        | 281/2500 [00:03<00:24, 92.36it/s, loss=0.0136]
[Trellis Server] Texture baking (opt): optimizing:  11%|#1        | 282/2500 [00:03<00:24, 92.36it/s, loss=0.0129]
[Trellis Server] Texture baking (opt): optimizing:  11%|#1        | 283/2500 [00:03<00:24, 92.36it/s, loss=0.0153]
[Trellis Server] Texture baking (opt): optimizing:  11%|#1        | 284/2500 [00:03<00:23, 92.36it/s, loss=0.0131]
[Trellis Server] Texture baking (opt): optimizing:  11%|#1        | 285/2500 [00:03<00:23, 92.36it/s, loss=0.0168]
[Trellis Server] Texture baking (opt): optimizing:  11%|#1        | 286/2500 [00:03<00:23, 92.36it/s, loss=0.0137]
[Trellis Server] Texture baking (opt): optimizing:  11%|#1        | 287/2500 [00:03<00:23, 92.36it/s, loss=0.0171]
[Trellis Server] Texture baking (opt): optimizing:  12%|#1        | 288/2500 [00:03<00:23, 92.36it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  12%|#1        | 289/2500 [00:03<00:23, 92.36it/s, loss=0.0151]
Texture baking (opt): optimizing:  12%|#1        | 291/2500 [00:03<00:23, 92.17it/s, loss=0.0136]t/s, loss=0.0136]
[Trellis Server] Texture baking (opt): optimizing:  12%|#1        | 291/2500 [00:03<00:23, 92.17it/s, loss=0.0202]
[Trellis Server] Texture baking (opt): optimizing:  12%|#1        | 292/2500 [00:03<00:23, 92.17it/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:  12%|#1        | 293/2500 [00:03<00:23, 92.17it/s, loss=0.0165]
[Trellis Server] Texture baking (opt): optimizing:  12%|#1        | 294/2500 [00:03<00:23, 92.17it/s, loss=0.0185]
[Trellis Server] Texture baking (opt): optimizing:  12%|#1        | 295/2500 [00:03<00:23, 92.17it/s, loss=0.00935]
[Trellis Server] Texture baking (opt): optimizing:  12%|#1        | 296/2500 [00:03<00:23, 92.17it/s, loss=0.0135]
[Trellis Server] Texture baking (opt): optimizing:  12%|#1        | 297/2500 [00:03<00:23, 92.17it/s, loss=0.00855]
[Trellis Server] Texture baking (opt): optimizing:  12%|#1        | 298/2500 [00:03<00:23, 92.17it/s, loss=0.0171]
[Trellis Server] Texture baking (opt): optimizing:  12%|#1        | 299/2500 [00:03<00:23, 92.17it/s, loss=0.0136]
Texture baking (opt): optimizing:  12%|#2        | 301/2500 [00:03<00:23, 92.72it/s, loss=0.0131]t/s, loss=0.0131]
[Trellis Server] Texture baking (opt): optimizing:  12%|#2        | 301/2500 [00:03<00:23, 92.72it/s, loss=0.0229]
[Trellis Server] Texture baking (opt): optimizing:  12%|#2        | 302/2500 [00:03<00:23, 92.72it/s, loss=0.0158]
[Trellis Server] Texture baking (opt): optimizing:  12%|#2        | 303/2500 [00:03<00:23, 92.72it/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  12%|#2        | 304/2500 [00:03<00:23, 92.72it/s, loss=0.0135]
[Trellis Server] Texture baking (opt): optimizing:  12%|#2        | 305/2500 [00:03<00:23, 92.72it/s, loss=0.0135]
[Trellis Server] Texture baking (opt): optimizing:  12%|#2        | 306/2500 [00:03<00:23, 92.72it/s, loss=0.0163]
[Trellis Server] Texture baking (opt): optimizing:  12%|#2        | 307/2500 [00:03<00:23, 92.72it/s, loss=0.0197]
[Trellis Server] Texture baking (opt): optimizing:  12%|#2        | 308/2500 [00:03<00:23, 92.72it/s, loss=0.00875]
[Trellis Server] Texture baking (opt): optimizing:  12%|#2        | 309/2500 [00:03<00:23, 92.72it/s, loss=0.011]
Texture baking (opt): optimizing:  12%|#2        | 311/2500 [00:03<00:23, 93.74it/s, loss=0.0274]t/s, loss=0.0274]
[Trellis Server] Texture baking (opt): optimizing:  12%|#2        | 311/2500 [00:03<00:23, 93.74it/s, loss=0.0162]
[Trellis Server] Texture baking (opt): optimizing:  12%|#2        | 312/2500 [00:03<00:23, 93.74it/s, loss=0.0105]
[Trellis Server] Texture baking (opt): optimizing:  13%|#2        | 313/2500 [00:03<00:23, 93.74it/s, loss=0.0133]
[Trellis Server] Texture baking (opt): optimizing:  13%|#2        | 314/2500 [00:03<00:23, 93.74it/s, loss=0.0292]
[Trellis Server] Texture baking (opt): optimizing:  13%|#2        | 315/2500 [00:03<00:23, 93.74it/s, loss=0.0104]
[Trellis Server] Texture baking (opt): optimizing:  13%|#2        | 316/2500 [00:03<00:23, 93.74it/s, loss=0.0197]
[Trellis Server] Texture baking (opt): optimizing:  13%|#2        | 317/2500 [00:03<00:23, 93.74it/s, loss=0.014]
[Trellis Server] Texture baking (opt): optimizing:  13%|#2        | 318/2500 [00:03<00:23, 93.74it/s, loss=0.0128]
[Trellis Server] Texture baking (opt): optimizing:  13%|#2        | 319/2500 [00:03<00:23, 93.74it/s, loss=0.00853]
Texture baking (opt): optimizing:  13%|#2        | 321/2500 [00:03<00:23, 94.72it/s, loss=0.0122]t/s, loss=0.0122]
[Trellis Server] Texture baking (opt): optimizing:  13%|#2        | 321/2500 [00:03<00:23, 94.72it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  13%|#2        | 322/2500 [00:03<00:22, 94.72it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  13%|#2        | 323/2500 [00:03<00:22, 94.72it/s, loss=0.0176]
[Trellis Server] Texture baking (opt): optimizing:  13%|#2        | 324/2500 [00:03<00:22, 94.72it/s, loss=0.015]
[Trellis Server] Texture baking (opt): optimizing:  13%|#3        | 325/2500 [00:03<00:22, 94.72it/s, loss=0.0169]
[Trellis Server] Texture baking (opt): optimizing:  13%|#3        | 326/2500 [00:03<00:22, 94.72it/s, loss=0.016]
[Trellis Server] Texture baking (opt): optimizing:  13%|#3        | 327/2500 [00:03<00:22, 94.72it/s, loss=0.0145]
[Trellis Server] Texture baking (opt): optimizing:  13%|#3        | 328/2500 [00:03<00:22, 94.72it/s, loss=0.0252]
[Trellis Server] Texture baking (opt): optimizing:  13%|#3        | 329/2500 [00:03<00:22, 94.72it/s, loss=0.031]
Texture baking (opt): optimizing:  13%|#3        | 331/2500 [00:03<00:22, 95.32it/s, loss=0.0134]t/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  13%|#3        | 331/2500 [00:03<00:22, 95.32it/s, loss=0.0136]
[Trellis Server] Texture baking (opt): optimizing:  13%|#3        | 332/2500 [00:03<00:22, 95.32it/s, loss=0.0257]
[Trellis Server] Texture baking (opt): optimizing:  13%|#3        | 333/2500 [00:03<00:22, 95.32it/s, loss=0.0188]
[Trellis Server] Texture baking (opt): optimizing:  13%|#3        | 334/2500 [00:03<00:22, 95.32it/s, loss=0.0164]
[Trellis Server] Texture baking (opt): optimizing:  13%|#3        | 335/2500 [00:03<00:22, 95.32it/s, loss=0.017]
[Trellis Server] Texture baking (opt): optimizing:  13%|#3        | 336/2500 [00:03<00:22, 95.32it/s, loss=0.0142]
[Trellis Server] Texture baking (opt): optimizing:  13%|#3        | 337/2500 [00:03<00:22, 95.32it/s, loss=0.0122]
[Trellis Server] Texture baking (opt): optimizing:  14%|#3        | 338/2500 [00:03<00:22, 95.32it/s, loss=0.00927]
[Trellis Server] Texture baking (opt): optimizing:  14%|#3        | 339/2500 [00:03<00:22, 95.32it/s, loss=0.0147]
Texture baking (opt): optimizing:  14%|#3        | 341/2500 [00:03<00:22, 95.02it/s, loss=0.0118]t/s, loss=0.0118]
[Trellis Server] Texture baking (opt): optimizing:  14%|#3        | 341/2500 [00:03<00:22, 95.02it/s, loss=0.00869]
[Trellis Server] Texture baking (opt): optimizing:  14%|#3        | 342/2500 [00:03<00:22, 95.02it/s, loss=0.016]
[Trellis Server] Texture baking (opt): optimizing:  14%|#3        | 343/2500 [00:03<00:22, 95.02it/s, loss=0.0107]
[Trellis Server] Texture baking (opt): optimizing:  14%|#3        | 344/2500 [00:03<00:22, 95.02it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  14%|#3        | 345/2500 [00:03<00:22, 95.02it/s, loss=0.0174]
[Trellis Server] Texture baking (opt): optimizing:  14%|#3        | 346/2500 [00:03<00:22, 95.02it/s, loss=0.0155]
[Trellis Server] Texture baking (opt): optimizing:  14%|#3        | 347/2500 [00:03<00:22, 95.02it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  14%|#3        | 348/2500 [00:03<00:22, 95.02it/s, loss=0.0103]
[Trellis Server] Texture baking (opt): optimizing:  14%|#3        | 349/2500 [00:03<00:22, 95.02it/s, loss=0.00968]
[Trellis Server] Texture baking (opt): optimizing:  14%|#4        | 350/2500 [00:03<00:22, 95.02it/s, loss=0.0138]
Texture baking (opt): optimizing:  14%|#4        | 352/2500 [00:03<00:22, 96.28it/s, loss=0.0106]t/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing:  14%|#4        | 352/2500 [00:03<00:22, 96.28it/s, loss=0.00925]
[Trellis Server] Texture baking (opt): optimizing:  14%|#4        | 353/2500 [00:03<00:22, 96.28it/s, loss=0.00767]
[Trellis Server] Texture baking (opt): optimizing:  14%|#4        | 354/2500 [00:03<00:22, 96.28it/s, loss=0.0179]
[Trellis Server] Texture baking (opt): optimizing:  14%|#4        | 355/2500 [00:03<00:22, 96.28it/s, loss=0.0279]
[Trellis Server] Texture baking (opt): optimizing:  14%|#4        | 356/2500 [00:03<00:22, 96.28it/s, loss=0.0171]
[Trellis Server] Texture baking (opt): optimizing:  14%|#4        | 357/2500 [00:03<00:22, 96.28it/s, loss=0.0188]
[Trellis Server] Texture baking (opt): optimizing:  14%|#4        | 358/2500 [00:03<00:22, 96.28it/s, loss=0.017]
[Trellis Server] Texture baking (opt): optimizing:  14%|#4        | 359/2500 [00:03<00:22, 96.28it/s, loss=0.0135]
[Trellis Server] Texture baking (opt): optimizing:  14%|#4        | 360/2500 [00:03<00:22, 96.28it/s, loss=0.0192]
[Trellis Server] Texture baking (opt): optimizing:  14%|#4        | 361/2500 [00:04<00:22, 96.28it/s, loss=0.0126]
Texture baking (opt): optimizing:  15%|#4        | 363/2500 [00:04<00:21, 97.70it/s, loss=0.0119]t/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  15%|#4        | 363/2500 [00:04<00:21, 97.70it/s, loss=0.0172]
[Trellis Server] Texture baking (opt): optimizing:  15%|#4        | 364/2500 [00:04<00:21, 97.70it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  15%|#4        | 365/2500 [00:04<00:21, 97.70it/s, loss=0.016]
[Trellis Server] Texture baking (opt): optimizing:  15%|#4        | 366/2500 [00:04<00:21, 97.70it/s, loss=0.0133]
[Trellis Server] Texture baking (opt): optimizing:  15%|#4        | 367/2500 [00:04<00:21, 97.70it/s, loss=0.0154]
[Trellis Server] Texture baking (opt): optimizing:  15%|#4        | 368/2500 [00:04<00:21, 97.70it/s, loss=0.015]
[Trellis Server] Texture baking (opt): optimizing:  15%|#4        | 369/2500 [00:04<00:21, 97.70it/s, loss=0.0149]
[Trellis Server] Texture baking (opt): optimizing:  15%|#4        | 370/2500 [00:04<00:21, 97.70it/s, loss=0.016]
[Trellis Server] Texture baking (opt): optimizing:  15%|#4        | 371/2500 [00:04<00:21, 97.70it/s, loss=0.0277]
Texture baking (opt): optimizing:  15%|#4        | 373/2500 [00:04<00:22, 96.16it/s, loss=0.0204]t/s, loss=0.0204]
[Trellis Server] Texture baking (opt): optimizing:  15%|#4        | 373/2500 [00:04<00:22, 96.16it/s, loss=0.0148]
[Trellis Server] Texture baking (opt): optimizing:  15%|#4        | 374/2500 [00:04<00:22, 96.16it/s, loss=0.0155]
[Trellis Server] Texture baking (opt): optimizing:  15%|#5        | 375/2500 [00:04<00:22, 96.16it/s, loss=0.0187]
[Trellis Server] Texture baking (opt): optimizing:  15%|#5        | 376/2500 [00:04<00:22, 96.16it/s, loss=0.0121]
[Trellis Server] Texture baking (opt): optimizing:  15%|#5        | 377/2500 [00:04<00:22, 96.16it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  15%|#5        | 378/2500 [00:04<00:22, 96.16it/s, loss=0.0223]
[Trellis Server] Texture baking (opt): optimizing:  15%|#5        | 379/2500 [00:04<00:22, 96.16it/s, loss=0.0175]
[Trellis Server] Texture baking (opt): optimizing:  15%|#5        | 380/2500 [00:04<00:22, 96.16it/s, loss=0.0178]
[Trellis Server] Texture baking (opt): optimizing:  15%|#5        | 381/2500 [00:04<00:22, 96.16it/s, loss=0.0107]
Texture baking (opt): optimizing:  15%|#5        | 383/2500 [00:04<00:21, 96.70it/s, loss=0.0144]t/s, loss=0.0144]
[Trellis Server] Texture baking (opt): optimizing:  15%|#5        | 383/2500 [00:04<00:21, 96.70it/s, loss=0.00991]
[Trellis Server] Texture baking (opt): optimizing:  15%|#5        | 384/2500 [00:04<00:21, 96.70it/s, loss=0.017]
[Trellis Server] Texture baking (opt): optimizing:  15%|#5        | 385/2500 [00:04<00:21, 96.70it/s, loss=0.0257]
[Trellis Server] Texture baking (opt): optimizing:  15%|#5        | 386/2500 [00:04<00:21, 96.70it/s, loss=0.0118]
[Trellis Server] Texture baking (opt): optimizing:  15%|#5        | 387/2500 [00:04<00:21, 96.70it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  16%|#5        | 388/2500 [00:04<00:21, 96.70it/s, loss=0.0149]
[Trellis Server] Texture baking (opt): optimizing:  16%|#5        | 389/2500 [00:04<00:21, 96.70it/s, loss=0.0146]
[Trellis Server] Texture baking (opt): optimizing:  16%|#5        | 390/2500 [00:04<00:21, 96.70it/s, loss=0.0132]
[Trellis Server] Texture baking (opt): optimizing:  16%|#5        | 391/2500 [00:04<00:21, 96.70it/s, loss=0.0153]
[Trellis Server] Texture baking (opt): optimizing:  16%|#5        | 392/2500 [00:04<00:21, 96.70it/s, loss=0.023]
Texture baking (opt): optimizing:  16%|#5        | 394/2500 [00:04<00:21, 97.45it/s, loss=0.0179]t/s, loss=0.0179]
[Trellis Server] Texture baking (opt): optimizing:  16%|#5        | 394/2500 [00:04<00:21, 97.45it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  16%|#5        | 395/2500 [00:04<00:21, 97.45it/s, loss=0.0135]
[Trellis Server] Texture baking (opt): optimizing:  16%|#5        | 396/2500 [00:04<00:21, 97.45it/s, loss=0.0128]
[Trellis Server] Texture baking (opt): optimizing:  16%|#5        | 397/2500 [00:04<00:21, 97.45it/s, loss=0.0197]
[Trellis Server] Texture baking (opt): optimizing:  16%|#5        | 398/2500 [00:04<00:21, 97.45it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  16%|#5        | 399/2500 [00:04<00:21, 97.45it/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  16%|#6        | 400/2500 [00:04<00:21, 97.45it/s, loss=0.0144]
[Trellis Server] Texture baking (opt): optimizing:  16%|#6        | 401/2500 [00:04<00:21, 97.45it/s, loss=0.0144]
[Trellis Server] Texture baking (opt): optimizing:  16%|#6        | 402/2500 [00:04<00:21, 97.45it/s, loss=0.0145]
Texture baking (opt): optimizing:  16%|#6        | 404/2500 [00:04<00:21, 97.07it/s, loss=0.0166]t/s, loss=0.0166]
[Trellis Server] Texture baking (opt): optimizing:  16%|#6        | 404/2500 [00:04<00:21, 97.07it/s, loss=0.0131]
[Trellis Server] Texture baking (opt): optimizing:  16%|#6        | 405/2500 [00:04<00:21, 97.07it/s, loss=0.0132]
[Trellis Server] Texture baking (opt): optimizing:  16%|#6        | 406/2500 [00:04<00:21, 97.07it/s, loss=0.0125]
[Trellis Server] Texture baking (opt): optimizing:  16%|#6        | 407/2500 [00:04<00:21, 97.07it/s, loss=0.0169]
[Trellis Server] Texture baking (opt): optimizing:  16%|#6        | 408/2500 [00:04<00:21, 97.07it/s, loss=0.0136]
[Trellis Server] Texture baking (opt): optimizing:  16%|#6        | 409/2500 [00:04<00:21, 97.07it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  16%|#6        | 410/2500 [00:04<00:21, 97.07it/s, loss=0.0125]
[Trellis Server] Texture baking (opt): optimizing:  16%|#6        | 411/2500 [00:04<00:21, 97.07it/s, loss=0.0295]
[Trellis Server] Texture baking (opt): optimizing:  16%|#6        | 412/2500 [00:04<00:21, 97.07it/s, loss=0.0183]
Texture baking (opt): optimizing:  17%|#6        | 414/2500 [00:04<00:21, 95.71it/s, loss=0.0185]t/s, loss=0.0185]
[Trellis Server] Texture baking (opt): optimizing:  17%|#6        | 414/2500 [00:04<00:21, 95.71it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  17%|#6        | 415/2500 [00:04<00:21, 95.71it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  17%|#6        | 416/2500 [00:04<00:21, 95.71it/s, loss=0.0155]
[Trellis Server] Texture baking (opt): optimizing:  17%|#6        | 417/2500 [00:04<00:21, 95.71it/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing:  17%|#6        | 418/2500 [00:04<00:21, 95.71it/s, loss=0.0207]
[Trellis Server] Texture baking (opt): optimizing:  17%|#6        | 419/2500 [00:04<00:21, 95.71it/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:  17%|#6        | 420/2500 [00:04<00:21, 95.71it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  17%|#6        | 421/2500 [00:04<00:21, 95.71it/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  17%|#6        | 422/2500 [00:04<00:21, 95.71it/s, loss=0.0169]
[Trellis Server] Texture baking (opt): optimizing:  17%|#6        | 423/2500 [00:04<00:21, 95.71it/s, loss=0.0149]
Texture baking (opt): optimizing:  17%|#7        | 425/2500 [00:04<00:21, 95.43it/s, loss=0.0144]t/s, loss=0.0144]
[Trellis Server] Texture baking (opt): optimizing:  17%|#7        | 425/2500 [00:04<00:21, 95.43it/s, loss=0.0123]
[Trellis Server] Texture baking (opt): optimizing:  17%|#7        | 426/2500 [00:04<00:21, 95.43it/s, loss=0.0133]
[Trellis Server] Texture baking (opt): optimizing:  17%|#7        | 427/2500 [00:04<00:21, 95.43it/s, loss=0.0235]
[Trellis Server] Texture baking (opt): optimizing:  17%|#7        | 428/2500 [00:04<00:21, 95.43it/s, loss=0.012]
[Trellis Server] Texture baking (opt): optimizing:  17%|#7        | 429/2500 [00:04<00:21, 95.43it/s, loss=0.0162]
[Trellis Server] Texture baking (opt): optimizing:  17%|#7        | 430/2500 [00:04<00:21, 95.43it/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  17%|#7        | 431/2500 [00:04<00:21, 95.43it/s, loss=0.0123]
[Trellis Server] Texture baking (opt): optimizing:  17%|#7        | 432/2500 [00:04<00:21, 95.43it/s, loss=0.0186]
[Trellis Server] Texture baking (opt): optimizing:  17%|#7        | 433/2500 [00:04<00:21, 95.43it/s, loss=0.0176]
Texture baking (opt): optimizing:  17%|#7        | 435/2500 [00:04<00:21, 95.64it/s, loss=0.0166]t/s, loss=0.0166]
[Trellis Server] Texture baking (opt): optimizing:  17%|#7        | 435/2500 [00:04<00:21, 95.64it/s, loss=0.0242]
[Trellis Server] Texture baking (opt): optimizing:  17%|#7        | 436/2500 [00:04<00:21, 95.64it/s, loss=0.0163]
[Trellis Server] Texture baking (opt): optimizing:  17%|#7        | 437/2500 [00:04<00:21, 95.64it/s, loss=0.0265]
[Trellis Server] Texture baking (opt): optimizing:  18%|#7        | 438/2500 [00:04<00:21, 95.64it/s, loss=0.00882]
[Trellis Server] Texture baking (opt): optimizing:  18%|#7        | 439/2500 [00:04<00:21, 95.64it/s, loss=0.0105]
[Trellis Server] Texture baking (opt): optimizing:  18%|#7        | 440/2500 [00:04<00:21, 95.64it/s, loss=0.0227]
[Trellis Server] Texture baking (opt): optimizing:  18%|#7        | 441/2500 [00:04<00:21, 95.64it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  18%|#7        | 442/2500 [00:04<00:21, 95.64it/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  18%|#7        | 443/2500 [00:04<00:21, 95.64it/s, loss=0.0117]
Texture baking (opt): optimizing:  18%|#7        | 445/2500 [00:04<00:21, 96.33it/s, loss=0.017]it/s, loss=0.017]
[Trellis Server] Texture baking (opt): optimizing:  18%|#7        | 445/2500 [00:04<00:21, 96.33it/s, loss=0.00844]
[Trellis Server] Texture baking (opt): optimizing:  18%|#7        | 446/2500 [00:04<00:21, 96.33it/s, loss=0.014]
[Trellis Server] Texture baking (opt): optimizing:  18%|#7        | 447/2500 [00:04<00:21, 96.33it/s, loss=0.0205]
[Trellis Server] Texture baking (opt): optimizing:  18%|#7        | 448/2500 [00:04<00:21, 96.33it/s, loss=0.0184]
[Trellis Server] Texture baking (opt): optimizing:  18%|#7        | 449/2500 [00:04<00:21, 96.33it/s, loss=0.00698]
[Trellis Server] Texture baking (opt): optimizing:  18%|#8        | 450/2500 [00:04<00:21, 96.33it/s, loss=0.0151]
[Trellis Server] Texture baking (opt): optimizing:  18%|#8        | 451/2500 [00:04<00:21, 96.33it/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:  18%|#8        | 452/2500 [00:04<00:21, 96.33it/s, loss=0.0151]
[Trellis Server] Texture baking (opt): optimizing:  18%|#8        | 453/2500 [00:04<00:21, 96.33it/s, loss=0.0185]
Texture baking (opt): optimizing:  18%|#8        | 455/2500 [00:04<00:20, 97.38it/s, loss=0.0141]t/s, loss=0.0141]
[Trellis Server] Texture baking (opt): optimizing:  18%|#8        | 455/2500 [00:04<00:20, 97.38it/s, loss=0.0152]
[Trellis Server] Texture baking (opt): optimizing:  18%|#8        | 456/2500 [00:04<00:20, 97.38it/s, loss=0.0138]
[Trellis Server] Texture baking (opt): optimizing:  18%|#8        | 457/2500 [00:05<00:20, 97.38it/s, loss=0.0143]
[Trellis Server] Texture baking (opt): optimizing:  18%|#8        | 458/2500 [00:05<00:20, 97.38it/s, loss=0.0132]
[Trellis Server] Texture baking (opt): optimizing:  18%|#8        | 459/2500 [00:05<00:20, 97.38it/s, loss=0.0166]
[Trellis Server] Texture baking (opt): optimizing:  18%|#8        | 460/2500 [00:05<00:20, 97.38it/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  18%|#8        | 461/2500 [00:05<00:20, 97.38it/s, loss=0.0177]
[Trellis Server] Texture baking (opt): optimizing:  18%|#8        | 462/2500 [00:05<00:20, 97.38it/s, loss=0.0138]
[Trellis Server] Texture baking (opt): optimizing:  19%|#8        | 463/2500 [00:05<00:20, 97.38it/s, loss=0.0163]
Texture baking (opt): optimizing:  19%|#8        | 465/2500 [00:05<00:20, 97.30it/s, loss=0.0266]t/s, loss=0.0266]
[Trellis Server] Texture baking (opt): optimizing:  19%|#8        | 465/2500 [00:05<00:20, 97.30it/s, loss=0.0193]
[Trellis Server] Texture baking (opt): optimizing:  19%|#8        | 466/2500 [00:05<00:20, 97.30it/s, loss=0.0131]
[Trellis Server] Texture baking (opt): optimizing:  19%|#8        | 467/2500 [00:05<00:20, 97.30it/s, loss=0.0185]
[Trellis Server] Texture baking (opt): optimizing:  19%|#8        | 468/2500 [00:05<00:20, 97.30it/s, loss=0.0185]
[Trellis Server] Texture baking (opt): optimizing:  19%|#8        | 469/2500 [00:05<00:20, 97.30it/s, loss=0.0162]
[Trellis Server] Texture baking (opt): optimizing:  19%|#8        | 470/2500 [00:05<00:20, 97.30it/s, loss=0.0156]
[Trellis Server] Texture baking (opt): optimizing:  19%|#8        | 471/2500 [00:05<00:20, 97.30it/s, loss=0.0143]
[Trellis Server] Texture baking (opt): optimizing:  19%|#8        | 472/2500 [00:05<00:20, 97.30it/s, loss=0.0158]
[Trellis Server] Texture baking (opt): optimizing:  19%|#8        | 473/2500 [00:05<00:20, 97.30it/s, loss=0.0245]
Texture baking (opt): optimizing:  19%|#9        | 475/2500 [00:05<00:20, 96.96it/s, loss=0.012]it/s, loss=0.012]
[Trellis Server] Texture baking (opt): optimizing:  19%|#9        | 475/2500 [00:05<00:20, 96.96it/s, loss=0.0157]
[Trellis Server] Texture baking (opt): optimizing:  19%|#9        | 476/2500 [00:05<00:20, 96.96it/s, loss=0.0141]
[Trellis Server] Texture baking (opt): optimizing:  19%|#9        | 477/2500 [00:05<00:20, 96.96it/s, loss=0.015]
[Trellis Server] Texture baking (opt): optimizing:  19%|#9        | 478/2500 [00:05<00:20, 96.96it/s, loss=0.0195]
[Trellis Server] Texture baking (opt): optimizing:  19%|#9        | 479/2500 [00:05<00:20, 96.96it/s, loss=0.0131]
[Trellis Server] Texture baking (opt): optimizing:  19%|#9        | 480/2500 [00:05<00:20, 96.96it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  19%|#9        | 481/2500 [00:05<00:20, 96.96it/s, loss=0.0135]
[Trellis Server] Texture baking (opt): optimizing:  19%|#9        | 482/2500 [00:05<00:20, 96.96it/s, loss=0.0141]
[Trellis Server] Texture baking (opt): optimizing:  19%|#9        | 483/2500 [00:05<00:20, 96.96it/s, loss=0.0142]
Texture baking (opt): optimizing:  19%|#9        | 485/2500 [00:05<00:20, 97.55it/s, loss=0.0171]t/s, loss=0.0171]
[Trellis Server] Texture baking (opt): optimizing:  19%|#9        | 485/2500 [00:05<00:20, 97.55it/s, loss=0.00871]
[Trellis Server] Texture baking (opt): optimizing:  19%|#9        | 486/2500 [00:05<00:20, 97.55it/s, loss=0.0255]
[Trellis Server] Texture baking (opt): optimizing:  19%|#9        | 487/2500 [00:05<00:20, 97.55it/s, loss=0.0147]
[Trellis Server] Texture baking (opt): optimizing:  20%|#9        | 488/2500 [00:05<00:20, 97.55it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  20%|#9        | 489/2500 [00:05<00:20, 97.55it/s, loss=0.0101]
[Trellis Server] Texture baking (opt): optimizing:  20%|#9        | 490/2500 [00:05<00:20, 97.55it/s, loss=0.0195]
[Trellis Server] Texture baking (opt): optimizing:  20%|#9        | 491/2500 [00:05<00:20, 97.55it/s, loss=0.0132]
[Trellis Server] Texture baking (opt): optimizing:  20%|#9        | 492/2500 [00:05<00:20, 97.55it/s, loss=0.00987]
[Trellis Server] Texture baking (opt): optimizing:  20%|#9        | 493/2500 [00:05<00:20, 97.55it/s, loss=0.0201]
Texture baking (opt): optimizing:  20%|#9        | 495/2500 [00:05<00:20, 97.99it/s, loss=0.00855]/s, loss=0.00855]
[Trellis Server] Texture baking (opt): optimizing:  20%|#9        | 495/2500 [00:05<00:20, 97.99it/s, loss=0.0116]
[Trellis Server] Texture baking (opt): optimizing:  20%|#9        | 496/2500 [00:05<00:20, 97.99it/s, loss=0.022]
[Trellis Server] Texture baking (opt): optimizing:  20%|#9        | 497/2500 [00:05<00:20, 97.99it/s, loss=0.017]
[Trellis Server] Texture baking (opt): optimizing:  20%|#9        | 498/2500 [00:05<00:20, 97.99it/s, loss=0.017]
[Trellis Server] Texture baking (opt): optimizing:  20%|#9        | 499/2500 [00:05<00:20, 97.99it/s, loss=0.0137]
[Trellis Server] Texture baking (opt): optimizing:  20%|##        | 500/2500 [00:05<00:20, 97.99it/s, loss=0.0162]
[Trellis Server] Texture baking (opt): optimizing:  20%|##        | 501/2500 [00:05<00:20, 97.99it/s, loss=0.0142]
[Trellis Server] Texture baking (opt): optimizing:  20%|##        | 502/2500 [00:05<00:20, 97.99it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  20%|##        | 503/2500 [00:05<00:20, 97.99it/s, loss=0.0162]
Texture baking (opt): optimizing:  20%|##        | 505/2500 [00:05<00:20, 98.00it/s, loss=0.0194]t/s, loss=0.0194]
[Trellis Server] Texture baking (opt): optimizing:  20%|##        | 505/2500 [00:05<00:20, 98.00it/s, loss=0.018]
[Trellis Server] Texture baking (opt): optimizing:  20%|##        | 506/2500 [00:05<00:20, 98.00it/s, loss=0.0214]
[Trellis Server] Texture baking (opt): optimizing:  20%|##        | 507/2500 [00:05<00:20, 98.00it/s, loss=0.0128]
[Trellis Server] Texture baking (opt): optimizing:  20%|##        | 508/2500 [00:05<00:20, 98.00it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  20%|##        | 509/2500 [00:05<00:20, 98.00it/s, loss=0.0172]
[Trellis Server] Texture baking (opt): optimizing:  20%|##        | 510/2500 [00:05<00:20, 98.00it/s, loss=0.00787]
[Trellis Server] Texture baking (opt): optimizing:  20%|##        | 511/2500 [00:05<00:20, 98.00it/s, loss=0.0141]
[Trellis Server] Texture baking (opt): optimizing:  20%|##        | 512/2500 [00:05<00:20, 98.00it/s, loss=0.0147]
[Trellis Server] Texture baking (opt): optimizing:  21%|##        | 513/2500 [00:05<00:20, 98.00it/s, loss=0.0177]
Texture baking (opt): optimizing:  21%|##        | 515/2500 [00:05<00:20, 97.16it/s, loss=0.017]it/s, loss=0.017]
[Trellis Server] Texture baking (opt): optimizing:  21%|##        | 515/2500 [00:05<00:20, 97.16it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  21%|##        | 516/2500 [00:05<00:20, 97.16it/s, loss=0.0171]
[Trellis Server] Texture baking (opt): optimizing:  21%|##        | 517/2500 [00:05<00:20, 97.16it/s, loss=0.0118]
[Trellis Server] Texture baking (opt): optimizing:  21%|##        | 518/2500 [00:05<00:20, 97.16it/s, loss=0.0146]
[Trellis Server] Texture baking (opt): optimizing:  21%|##        | 519/2500 [00:05<00:20, 97.16it/s, loss=0.0157]
[Trellis Server] Texture baking (opt): optimizing:  21%|##        | 520/2500 [00:05<00:20, 97.16it/s, loss=0.0158]
[Trellis Server] Texture baking (opt): optimizing:  21%|##        | 521/2500 [00:05<00:20, 97.16it/s, loss=0.0147]
[Trellis Server] Texture baking (opt): optimizing:  21%|##        | 522/2500 [00:05<00:20, 97.16it/s, loss=0.0157]
[Trellis Server] Texture baking (opt): optimizing:  21%|##        | 523/2500 [00:05<00:20, 97.16it/s, loss=0.0129]
Texture baking (opt): optimizing:  21%|##1       | 525/2500 [00:05<00:21, 93.07it/s, loss=0.0171]t/s, loss=0.0171]
[Trellis Server] Texture baking (opt): optimizing:  21%|##1       | 525/2500 [00:05<00:21, 93.07it/s, loss=0.0172]
[Trellis Server] Texture baking (opt): optimizing:  21%|##1       | 526/2500 [00:05<00:21, 93.07it/s, loss=0.0143]
[Trellis Server] Texture baking (opt): optimizing:  21%|##1       | 527/2500 [00:05<00:21, 93.07it/s, loss=0.0142]
[Trellis Server] Texture baking (opt): optimizing:  21%|##1       | 528/2500 [00:05<00:21, 93.07it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  21%|##1       | 529/2500 [00:05<00:21, 93.07it/s, loss=0.0131]
[Trellis Server] Texture baking (opt): optimizing:  21%|##1       | 530/2500 [00:05<00:21, 93.07it/s, loss=0.0141]
[Trellis Server] Texture baking (opt): optimizing:  21%|##1       | 531/2500 [00:05<00:21, 93.07it/s, loss=0.0155]
[Trellis Server] Texture baking (opt): optimizing:  21%|##1       | 532/2500 [00:05<00:21, 93.07it/s, loss=0.0103]
[Trellis Server] Texture baking (opt): optimizing:  21%|##1       | 533/2500 [00:05<00:21, 93.07it/s, loss=0.00934]
Texture baking (opt): optimizing:  21%|##1       | 535/2500 [00:05<00:21, 92.45it/s, loss=0.025]it/s, loss=0.025]
[Trellis Server] Texture baking (opt): optimizing:  21%|##1       | 535/2500 [00:05<00:21, 92.45it/s, loss=0.0157]
[Trellis Server] Texture baking (opt): optimizing:  21%|##1       | 536/2500 [00:05<00:21, 92.45it/s, loss=0.00907]
[Trellis Server] Texture baking (opt): optimizing:  21%|##1       | 537/2500 [00:05<00:21, 92.45it/s, loss=0.0121]
[Trellis Server] Texture baking (opt): optimizing:  22%|##1       | 538/2500 [00:05<00:21, 92.45it/s, loss=0.0142]
[Trellis Server] Texture baking (opt): optimizing:  22%|##1       | 539/2500 [00:05<00:21, 92.45it/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:  22%|##1       | 540/2500 [00:05<00:21, 92.45it/s, loss=0.0143]
[Trellis Server] Texture baking (opt): optimizing:  22%|##1       | 541/2500 [00:05<00:21, 92.45it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  22%|##1       | 542/2500 [00:05<00:21, 92.45it/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  22%|##1       | 543/2500 [00:05<00:21, 92.45it/s, loss=0.0134]
Texture baking (opt): optimizing:  22%|##1       | 545/2500 [00:05<00:20, 94.11it/s, loss=0.0178]t/s, loss=0.0178]
[Trellis Server] Texture baking (opt): optimizing:  22%|##1       | 545/2500 [00:05<00:20, 94.11it/s, loss=0.0174]
[Trellis Server] Texture baking (opt): optimizing:  22%|##1       | 546/2500 [00:05<00:20, 94.11it/s, loss=0.0149]
[Trellis Server] Texture baking (opt): optimizing:  22%|##1       | 547/2500 [00:05<00:20, 94.11it/s, loss=0.0167]
[Trellis Server] Texture baking (opt): optimizing:  22%|##1       | 548/2500 [00:05<00:20, 94.11it/s, loss=0.0194]
[Trellis Server] Texture baking (opt): optimizing:  22%|##1       | 549/2500 [00:05<00:20, 94.11it/s, loss=0.0163]
[Trellis Server] Texture baking (opt): optimizing:  22%|##2       | 550/2500 [00:05<00:20, 94.11it/s, loss=0.0161]
[Trellis Server] Texture baking (opt): optimizing:  22%|##2       | 551/2500 [00:05<00:20, 94.11it/s, loss=0.0177]
[Trellis Server] Texture baking (opt): optimizing:  22%|##2       | 552/2500 [00:06<00:20, 94.11it/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  22%|##2       | 553/2500 [00:06<00:20, 94.11it/s, loss=0.0167]
Texture baking (opt): optimizing:  22%|##2       | 555/2500 [00:06<00:20, 93.92it/s, loss=0.0123]t/s, loss=0.0123]
[Trellis Server] Texture baking (opt): optimizing:  22%|##2       | 555/2500 [00:06<00:20, 93.92it/s, loss=0.0156]
[Trellis Server] Texture baking (opt): optimizing:  22%|##2       | 556/2500 [00:06<00:20, 93.92it/s, loss=0.0175]
[Trellis Server] Texture baking (opt): optimizing:  22%|##2       | 557/2500 [00:06<00:20, 93.92it/s, loss=0.0118]
[Trellis Server] Texture baking (opt): optimizing:  22%|##2       | 558/2500 [00:06<00:20, 93.92it/s, loss=0.0143]
[Trellis Server] Texture baking (opt): optimizing:  22%|##2       | 559/2500 [00:06<00:20, 93.92it/s, loss=0.0145]
[Trellis Server] Texture baking (opt): optimizing:  22%|##2       | 560/2500 [00:06<00:20, 93.92it/s, loss=0.0138]
[Trellis Server] Texture baking (opt): optimizing:  22%|##2       | 561/2500 [00:06<00:20, 93.92it/s, loss=0.0189]
[Trellis Server] Texture baking (opt): optimizing:  22%|##2       | 562/2500 [00:06<00:20, 93.92it/s, loss=0.0158]
[Trellis Server] Texture baking (opt): optimizing:  23%|##2       | 563/2500 [00:06<00:20, 93.92it/s, loss=0.0111]
Texture baking (opt): optimizing:  23%|##2       | 565/2500 [00:06<00:20, 95.39it/s, loss=0.025]it/s, loss=0.025]
[Trellis Server] Texture baking (opt): optimizing:  23%|##2       | 565/2500 [00:06<00:20, 95.39it/s, loss=0.0147]
[Trellis Server] Texture baking (opt): optimizing:  23%|##2       | 566/2500 [00:06<00:20, 95.39it/s, loss=0.0132]
[Trellis Server] Texture baking (opt): optimizing:  23%|##2       | 567/2500 [00:06<00:20, 95.39it/s, loss=0.0159]
[Trellis Server] Texture baking (opt): optimizing:  23%|##2       | 568/2500 [00:06<00:20, 95.39it/s, loss=0.0221]
[Trellis Server] Texture baking (opt): optimizing:  23%|##2       | 569/2500 [00:06<00:20, 95.39it/s, loss=0.0146]
[Trellis Server] Texture baking (opt): optimizing:  23%|##2       | 570/2500 [00:06<00:20, 95.39it/s, loss=0.0209]
[Trellis Server] Texture baking (opt): optimizing:  23%|##2       | 571/2500 [00:06<00:20, 95.39it/s, loss=0.0163]
[Trellis Server] Texture baking (opt): optimizing:  23%|##2       | 572/2500 [00:06<00:20, 95.39it/s, loss=0.0164]
[Trellis Server] Texture baking (opt): optimizing:  23%|##2       | 573/2500 [00:06<00:20, 95.39it/s, loss=0.0238]
Texture baking (opt): optimizing:  23%|##3       | 575/2500 [00:06<00:20, 95.07it/s, loss=0.0171]t/s, loss=0.0171]
[Trellis Server] Texture baking (opt): optimizing:  23%|##3       | 575/2500 [00:06<00:20, 95.07it/s, loss=0.0172]
[Trellis Server] Texture baking (opt): optimizing:  23%|##3       | 576/2500 [00:06<00:20, 95.07it/s, loss=0.0153]
[Trellis Server] Texture baking (opt): optimizing:  23%|##3       | 577/2500 [00:06<00:20, 95.07it/s, loss=0.0159]
[Trellis Server] Texture baking (opt): optimizing:  23%|##3       | 578/2500 [00:06<00:20, 95.07it/s, loss=0.0125]
[Trellis Server] Texture baking (opt): optimizing:  23%|##3       | 579/2500 [00:06<00:20, 95.07it/s, loss=0.0133]
[Trellis Server] Texture baking (opt): optimizing:  23%|##3       | 580/2500 [00:06<00:20, 95.07it/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  23%|##3       | 581/2500 [00:06<00:20, 95.07it/s, loss=0.0118]
[Trellis Server] Texture baking (opt): optimizing:  23%|##3       | 582/2500 [00:06<00:20, 95.07it/s, loss=0.0221]
[Trellis Server] Texture baking (opt): optimizing:  23%|##3       | 583/2500 [00:06<00:20, 95.07it/s, loss=0.0191]
Texture baking (opt): optimizing:  23%|##3       | 585/2500 [00:06<00:20, 95.13it/s, loss=0.0146]t/s, loss=0.0146]
[Trellis Server] Texture baking (opt): optimizing:  23%|##3       | 585/2500 [00:06<00:20, 95.13it/s, loss=0.0172]
[Trellis Server] Texture baking (opt): optimizing:  23%|##3       | 586/2500 [00:06<00:20, 95.13it/s, loss=0.0177]
[Trellis Server] Texture baking (opt): optimizing:  23%|##3       | 587/2500 [00:06<00:20, 95.13it/s, loss=0.0193]
[Trellis Server] Texture baking (opt): optimizing:  24%|##3       | 588/2500 [00:06<00:20, 95.13it/s, loss=0.0132]
[Trellis Server] Texture baking (opt): optimizing:  24%|##3       | 589/2500 [00:06<00:20, 95.13it/s, loss=0.0101]
[Trellis Server] Texture baking (opt): optimizing:  24%|##3       | 590/2500 [00:06<00:20, 95.13it/s, loss=0.0146]
[Trellis Server] Texture baking (opt): optimizing:  24%|##3       | 591/2500 [00:06<00:20, 95.13it/s, loss=0.0162]
[Trellis Server] Texture baking (opt): optimizing:  24%|##3       | 592/2500 [00:06<00:20, 95.13it/s, loss=0.0165]
[Trellis Server] Texture baking (opt): optimizing:  24%|##3       | 593/2500 [00:06<00:20, 95.13it/s, loss=0.0112]
Texture baking (opt): optimizing:  24%|##3       | 595/2500 [00:06<00:20, 95.16it/s, loss=0.0168]t/s, loss=0.0168]
[Trellis Server] Texture baking (opt): optimizing:  24%|##3       | 595/2500 [00:06<00:20, 95.16it/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing:  24%|##3       | 596/2500 [00:06<00:20, 95.16it/s, loss=0.0144]
[Trellis Server] Texture baking (opt): optimizing:  24%|##3       | 597/2500 [00:06<00:19, 95.16it/s, loss=0.0138]
[Trellis Server] Texture baking (opt): optimizing:  24%|##3       | 598/2500 [00:06<00:19, 95.16it/s, loss=0.0178]
[Trellis Server] Texture baking (opt): optimizing:  24%|##3       | 599/2500 [00:06<00:19, 95.16it/s, loss=0.0132]
[Trellis Server] Texture baking (opt): optimizing:  24%|##4       | 600/2500 [00:06<00:19, 95.16it/s, loss=0.0124]
[Trellis Server] Texture baking (opt): optimizing:  24%|##4       | 601/2500 [00:06<00:19, 95.16it/s, loss=0.0171]
[Trellis Server] Texture baking (opt): optimizing:  24%|##4       | 602/2500 [00:06<00:19, 95.16it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  24%|##4       | 603/2500 [00:06<00:19, 95.16it/s, loss=0.0284]
Texture baking (opt): optimizing:  24%|##4       | 605/2500 [00:06<00:20, 94.10it/s, loss=0.0139]t/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:  24%|##4       | 605/2500 [00:06<00:20, 94.10it/s, loss=0.0132]
[Trellis Server] Texture baking (opt): optimizing:  24%|##4       | 606/2500 [00:06<00:20, 94.10it/s, loss=0.0091]
[Trellis Server] Texture baking (opt): optimizing:  24%|##4       | 607/2500 [00:06<00:20, 94.10it/s, loss=0.0204]
[Trellis Server] Texture baking (opt): optimizing:  24%|##4       | 608/2500 [00:06<00:20, 94.10it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  24%|##4       | 609/2500 [00:06<00:20, 94.10it/s, loss=0.0125]
[Trellis Server] Texture baking (opt): optimizing:  24%|##4       | 610/2500 [00:06<00:20, 94.10it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  24%|##4       | 611/2500 [00:06<00:20, 94.10it/s, loss=0.015]
[Trellis Server] Texture baking (opt): optimizing:  24%|##4       | 612/2500 [00:06<00:20, 94.10it/s, loss=0.0147]
[Trellis Server] Texture baking (opt): optimizing:  25%|##4       | 613/2500 [00:06<00:20, 94.10it/s, loss=0.0179]
Texture baking (opt): optimizing:  25%|##4       | 615/2500 [00:06<00:19, 94.70it/s, loss=0.0128]t/s, loss=0.0128]
[Trellis Server] Texture baking (opt): optimizing:  25%|##4       | 615/2500 [00:06<00:19, 94.70it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  25%|##4       | 616/2500 [00:06<00:19, 94.70it/s, loss=0.0248]
[Trellis Server] Texture baking (opt): optimizing:  25%|##4       | 617/2500 [00:06<00:19, 94.70it/s, loss=0.0174]
[Trellis Server] Texture baking (opt): optimizing:  25%|##4       | 618/2500 [00:06<00:19, 94.70it/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  25%|##4       | 619/2500 [00:06<00:19, 94.70it/s, loss=0.01]
[Trellis Server] Texture baking (opt): optimizing:  25%|##4       | 620/2500 [00:06<00:19, 94.70it/s, loss=0.0142]
[Trellis Server] Texture baking (opt): optimizing:  25%|##4       | 621/2500 [00:06<00:19, 94.70it/s, loss=0.0145]
[Trellis Server] Texture baking (opt): optimizing:  25%|##4       | 622/2500 [00:06<00:19, 94.70it/s, loss=0.032]
[Trellis Server] Texture baking (opt): optimizing:  25%|##4       | 623/2500 [00:06<00:19, 94.70it/s, loss=0.0138]
[Trellis Server] Texture baking (opt): optimizing:  25%|##4       | 624/2500 [00:06<00:19, 94.70it/s, loss=0.0142]
Texture baking (opt): optimizing:  25%|##5       | 626/2500 [00:06<00:19, 96.84it/s, loss=0.0153]t/s, loss=0.0153]
[Trellis Server] Texture baking (opt): optimizing:  25%|##5       | 626/2500 [00:06<00:19, 96.84it/s, loss=0.0141]
[Trellis Server] Texture baking (opt): optimizing:  25%|##5       | 627/2500 [00:06<00:19, 96.84it/s, loss=0.0154]
[Trellis Server] Texture baking (opt): optimizing:  25%|##5       | 628/2500 [00:06<00:19, 96.84it/s, loss=0.0192]
[Trellis Server] Texture baking (opt): optimizing:  25%|##5       | 629/2500 [00:06<00:19, 96.84it/s, loss=0.021]
[Trellis Server] Texture baking (opt): optimizing:  25%|##5       | 630/2500 [00:06<00:19, 96.84it/s, loss=0.0133]
[Trellis Server] Texture baking (opt): optimizing:  25%|##5       | 631/2500 [00:06<00:19, 96.84it/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  25%|##5       | 632/2500 [00:06<00:19, 96.84it/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:  25%|##5       | 633/2500 [00:06<00:19, 96.84it/s, loss=0.0176]
[Trellis Server] Texture baking (opt): optimizing:  25%|##5       | 634/2500 [00:06<00:19, 96.84it/s, loss=0.0149]
Texture baking (opt): optimizing:  25%|##5       | 636/2500 [00:06<00:19, 97.14it/s, loss=0.0103]t/s, loss=0.0103]
[Trellis Server] Texture baking (opt): optimizing:  25%|##5       | 636/2500 [00:06<00:19, 97.14it/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  25%|##5       | 637/2500 [00:06<00:19, 97.14it/s, loss=0.0148]
[Trellis Server] Texture baking (opt): optimizing:  26%|##5       | 638/2500 [00:06<00:19, 97.14it/s, loss=0.0136]
[Trellis Server] Texture baking (opt): optimizing:  26%|##5       | 639/2500 [00:06<00:19, 97.14it/s, loss=0.0154]
[Trellis Server] Texture baking (opt): optimizing:  26%|##5       | 640/2500 [00:06<00:19, 97.14it/s, loss=0.0149]
[Trellis Server] Texture baking (opt): optimizing:  26%|##5       | 641/2500 [00:06<00:19, 97.14it/s, loss=0.0197]
[Trellis Server] Texture baking (opt): optimizing:  26%|##5       | 642/2500 [00:06<00:19, 97.14it/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:  26%|##5       | 643/2500 [00:06<00:19, 97.14it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  26%|##5       | 644/2500 [00:06<00:19, 97.14it/s, loss=0.0177]
[Trellis Server] Texture baking (opt): optimizing:  26%|##5       | 645/2500 [00:06<00:19, 97.14it/s, loss=0.016]
Texture baking (opt): optimizing:  26%|##5       | 647/2500 [00:06<00:18, 98.02it/s, loss=0.0113]t/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  26%|##5       | 647/2500 [00:06<00:18, 98.02it/s, loss=0.0151]
[Trellis Server] Texture baking (opt): optimizing:  26%|##5       | 648/2500 [00:06<00:18, 98.02it/s, loss=0.0187]
[Trellis Server] Texture baking (opt): optimizing:  26%|##5       | 649/2500 [00:07<00:18, 98.02it/s, loss=0.0167]
[Trellis Server] Texture baking (opt): optimizing:  26%|##6       | 650/2500 [00:07<00:18, 98.02it/s, loss=0.015]
[Trellis Server] Texture baking (opt): optimizing:  26%|##6       | 651/2500 [00:07<00:18, 98.02it/s, loss=0.0129]
[Trellis Server] Texture baking (opt): optimizing:  26%|##6       | 652/2500 [00:07<00:18, 98.02it/s, loss=0.0152]
[Trellis Server] Texture baking (opt): optimizing:  26%|##6       | 653/2500 [00:07<00:18, 98.02it/s, loss=0.00994]
[Trellis Server] Texture baking (opt): optimizing:  26%|##6       | 654/2500 [00:07<00:18, 98.02it/s, loss=0.0167]
[Trellis Server] Texture baking (opt): optimizing:  26%|##6       | 655/2500 [00:07<00:18, 98.02it/s, loss=0.0143]
Texture baking (opt): optimizing:  26%|##6       | 657/2500 [00:07<00:19, 95.31it/s, loss=0.0159]t/s, loss=0.0159]
[Trellis Server] Texture baking (opt): optimizing:  26%|##6       | 657/2500 [00:07<00:19, 95.31it/s, loss=0.0158]
[Trellis Server] Texture baking (opt): optimizing:  26%|##6       | 658/2500 [00:07<00:19, 95.31it/s, loss=0.00871]
[Trellis Server] Texture baking (opt): optimizing:  26%|##6       | 659/2500 [00:07<00:19, 95.31it/s, loss=0.0166]
[Trellis Server] Texture baking (opt): optimizing:  26%|##6       | 660/2500 [00:07<00:19, 95.31it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  26%|##6       | 661/2500 [00:07<00:19, 95.31it/s, loss=0.0141]
[Trellis Server] Texture baking (opt): optimizing:  26%|##6       | 662/2500 [00:07<00:19, 95.31it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  27%|##6       | 663/2500 [00:07<00:19, 95.31it/s, loss=0.0163]
[Trellis Server] Texture baking (opt): optimizing:  27%|##6       | 664/2500 [00:07<00:19, 95.31it/s, loss=0.0162]
[Trellis Server] Texture baking (opt): optimizing:  27%|##6       | 665/2500 [00:07<00:19, 95.31it/s, loss=0.0175]
Texture baking (opt): optimizing:  27%|##6       | 667/2500 [00:07<00:19, 96.37it/s, loss=0.00883]/s, loss=0.00883]
[Trellis Server] Texture baking (opt): optimizing:  27%|##6       | 667/2500 [00:07<00:19, 96.37it/s, loss=0.0218]
[Trellis Server] Texture baking (opt): optimizing:  27%|##6       | 668/2500 [00:07<00:19, 96.37it/s, loss=0.0168]
[Trellis Server] Texture baking (opt): optimizing:  27%|##6       | 669/2500 [00:07<00:18, 96.37it/s, loss=0.0167]
[Trellis Server] Texture baking (opt): optimizing:  27%|##6       | 670/2500 [00:07<00:18, 96.37it/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:  27%|##6       | 671/2500 [00:07<00:18, 96.37it/s, loss=0.0081]
[Trellis Server] Texture baking (opt): optimizing:  27%|##6       | 672/2500 [00:07<00:18, 96.37it/s, loss=0.0163]
[Trellis Server] Texture baking (opt): optimizing:  27%|##6       | 673/2500 [00:07<00:18, 96.37it/s, loss=0.0149]
[Trellis Server] Texture baking (opt): optimizing:  27%|##6       | 674/2500 [00:07<00:18, 96.37it/s, loss=0.0124]
[Trellis Server] Texture baking (opt): optimizing:  27%|##7       | 675/2500 [00:07<00:18, 96.37it/s, loss=0.0108]
Texture baking (opt): optimizing:  27%|##7       | 677/2500 [00:07<00:18, 96.86it/s, loss=0.0203]t/s, loss=0.0203]
[Trellis Server] Texture baking (opt): optimizing:  27%|##7       | 677/2500 [00:07<00:18, 96.86it/s, loss=0.0105]
[Trellis Server] Texture baking (opt): optimizing:  27%|##7       | 678/2500 [00:07<00:18, 96.86it/s, loss=0.0151]
[Trellis Server] Texture baking (opt): optimizing:  27%|##7       | 679/2500 [00:07<00:18, 96.86it/s, loss=0.0154]
[Trellis Server] Texture baking (opt): optimizing:  27%|##7       | 680/2500 [00:07<00:18, 96.86it/s, loss=0.0165]
[Trellis Server] Texture baking (opt): optimizing:  27%|##7       | 681/2500 [00:07<00:18, 96.86it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  27%|##7       | 682/2500 [00:07<00:18, 96.86it/s, loss=0.0147]
[Trellis Server] Texture baking (opt): optimizing:  27%|##7       | 683/2500 [00:07<00:18, 96.86it/s, loss=0.00934]
[Trellis Server] Texture baking (opt): optimizing:  27%|##7       | 684/2500 [00:07<00:18, 96.86it/s, loss=0.0101]
[Trellis Server] Texture baking (opt): optimizing:  27%|##7       | 685/2500 [00:07<00:18, 96.86it/s, loss=0.0155]
Texture baking (opt): optimizing:  27%|##7       | 687/2500 [00:07<00:18, 97.20it/s, loss=0.0199]t/s, loss=0.0199]
[Trellis Server] Texture baking (opt): optimizing:  27%|##7       | 687/2500 [00:07<00:18, 97.20it/s, loss=0.0155]
[Trellis Server] Texture baking (opt): optimizing:  28%|##7       | 688/2500 [00:07<00:18, 97.20it/s, loss=0.0187]
[Trellis Server] Texture baking (opt): optimizing:  28%|##7       | 689/2500 [00:07<00:18, 97.20it/s, loss=0.0153]
[Trellis Server] Texture baking (opt): optimizing:  28%|##7       | 690/2500 [00:07<00:18, 97.20it/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  28%|##7       | 691/2500 [00:07<00:18, 97.20it/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  28%|##7       | 692/2500 [00:07<00:18, 97.20it/s, loss=0.0202]
[Trellis Server] Texture baking (opt): optimizing:  28%|##7       | 693/2500 [00:07<00:18, 97.20it/s, loss=0.00998]
[Trellis Server] Texture baking (opt): optimizing:  28%|##7       | 694/2500 [00:07<00:18, 97.20it/s, loss=0.0121]
[Trellis Server] Texture baking (opt): optimizing:  28%|##7       | 695/2500 [00:07<00:18, 97.20it/s, loss=0.0111]
Texture baking (opt): optimizing:  28%|##7       | 697/2500 [00:07<00:18, 97.29it/s, loss=0.021]it/s, loss=0.021]
[Trellis Server] Texture baking (opt): optimizing:  28%|##7       | 697/2500 [00:07<00:18, 97.29it/s, loss=0.0144]
[Trellis Server] Texture baking (opt): optimizing:  28%|##7       | 698/2500 [00:07<00:18, 97.29it/s, loss=0.0125]
[Trellis Server] Texture baking (opt): optimizing:  28%|##7       | 699/2500 [00:07<00:18, 97.29it/s, loss=0.0124]
[Trellis Server] Texture baking (opt): optimizing:  28%|##8       | 700/2500 [00:07<00:18, 97.29it/s, loss=0.0176]
[Trellis Server] Texture baking (opt): optimizing:  28%|##8       | 701/2500 [00:07<00:18, 97.29it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  28%|##8       | 702/2500 [00:07<00:18, 97.29it/s, loss=0.0145]
[Trellis Server] Texture baking (opt): optimizing:  28%|##8       | 703/2500 [00:07<00:18, 97.29it/s, loss=0.016]
[Trellis Server] Texture baking (opt): optimizing:  28%|##8       | 704/2500 [00:07<00:18, 97.29it/s, loss=0.0155]
[Trellis Server] Texture baking (opt): optimizing:  28%|##8       | 705/2500 [00:07<00:18, 97.29it/s, loss=0.0141]
[Trellis Server] Texture baking (opt): optimizing:  28%|##8       | 706/2500 [00:07<00:18, 97.29it/s, loss=0.016]
Texture baking (opt): optimizing:  28%|##8       | 708/2500 [00:07<00:18, 98.42it/s, loss=0.0114]t/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  28%|##8       | 708/2500 [00:07<00:18, 98.42it/s, loss=0.0132]
[Trellis Server] Texture baking (opt): optimizing:  28%|##8       | 709/2500 [00:07<00:18, 98.42it/s, loss=0.0108]
[Trellis Server] Texture baking (opt): optimizing:  28%|##8       | 710/2500 [00:07<00:18, 98.42it/s, loss=0.0154]
[Trellis Server] Texture baking (opt): optimizing:  28%|##8       | 711/2500 [00:07<00:18, 98.42it/s, loss=0.0151]
[Trellis Server] Texture baking (opt): optimizing:  28%|##8       | 712/2500 [00:07<00:18, 98.42it/s, loss=0.0132]
[Trellis Server] Texture baking (opt): optimizing:  29%|##8       | 713/2500 [00:07<00:18, 98.42it/s, loss=0.0128]
[Trellis Server] Texture baking (opt): optimizing:  29%|##8       | 714/2500 [00:07<00:18, 98.42it/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  29%|##8       | 715/2500 [00:07<00:18, 98.42it/s, loss=0.0205]
[Trellis Server] Texture baking (opt): optimizing:  29%|##8       | 716/2500 [00:07<00:18, 98.42it/s, loss=0.0171]
Texture baking (opt): optimizing:  29%|##8       | 718/2500 [00:07<00:18, 98.87it/s, loss=0.0142]t/s, loss=0.0142]
[Trellis Server] Texture baking (opt): optimizing:  29%|##8       | 718/2500 [00:07<00:18, 98.87it/s, loss=0.0152]
[Trellis Server] Texture baking (opt): optimizing:  29%|##8       | 719/2500 [00:07<00:18, 98.87it/s, loss=0.0148]
[Trellis Server] Texture baking (opt): optimizing:  29%|##8       | 720/2500 [00:07<00:18, 98.87it/s, loss=0.0181]
[Trellis Server] Texture baking (opt): optimizing:  29%|##8       | 721/2500 [00:07<00:17, 98.87it/s, loss=0.0155]
[Trellis Server] Texture baking (opt): optimizing:  29%|##8       | 722/2500 [00:07<00:17, 98.87it/s, loss=0.0165]
[Trellis Server] Texture baking (opt): optimizing:  29%|##8       | 723/2500 [00:07<00:17, 98.87it/s, loss=0.0137]
[Trellis Server] Texture baking (opt): optimizing:  29%|##8       | 724/2500 [00:07<00:17, 98.87it/s, loss=0.018]
[Trellis Server] Texture baking (opt): optimizing:  29%|##9       | 725/2500 [00:07<00:17, 98.87it/s, loss=0.0168]
[Trellis Server] Texture baking (opt): optimizing:  29%|##9       | 726/2500 [00:07<00:17, 98.87it/s, loss=0.011]
Texture baking (opt): optimizing:  29%|##9       | 728/2500 [00:07<00:17, 99.15it/s, loss=0.0136]t/s, loss=0.0136]
[Trellis Server] Texture baking (opt): optimizing:  29%|##9       | 728/2500 [00:07<00:17, 99.15it/s, loss=0.0146]
[Trellis Server] Texture baking (opt): optimizing:  29%|##9       | 729/2500 [00:07<00:17, 99.15it/s, loss=0.0146]
[Trellis Server] Texture baking (opt): optimizing:  29%|##9       | 730/2500 [00:07<00:17, 99.15it/s, loss=0.0153]
[Trellis Server] Texture baking (opt): optimizing:  29%|##9       | 731/2500 [00:07<00:17, 99.15it/s, loss=0.0238]
[Trellis Server] Texture baking (opt): optimizing:  29%|##9       | 732/2500 [00:07<00:17, 99.15it/s, loss=0.0162]
[Trellis Server] Texture baking (opt): optimizing:  29%|##9       | 733/2500 [00:07<00:17, 99.15it/s, loss=0.0166]
[Trellis Server] Texture baking (opt): optimizing:  29%|##9       | 734/2500 [00:07<00:17, 99.15it/s, loss=0.0158]
[Trellis Server] Texture baking (opt): optimizing:  29%|##9       | 735/2500 [00:07<00:17, 99.15it/s, loss=0.016]
[Trellis Server] Texture baking (opt): optimizing:  29%|##9       | 736/2500 [00:07<00:17, 99.15it/s, loss=0.0106]
Texture baking (opt): optimizing:  30%|##9       | 738/2500 [00:07<00:17, 99.40it/s, loss=0.00943]/s, loss=0.00943]
[Trellis Server] Texture baking (opt): optimizing:  30%|##9       | 738/2500 [00:07<00:17, 99.40it/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  30%|##9       | 739/2500 [00:07<00:17, 99.40it/s, loss=0.02]
[Trellis Server] Texture baking (opt): optimizing:  30%|##9       | 740/2500 [00:07<00:17, 99.40it/s, loss=0.0149]
[Trellis Server] Texture baking (opt): optimizing:  30%|##9       | 741/2500 [00:07<00:17, 99.40it/s, loss=0.0159]
[Trellis Server] Texture baking (opt): optimizing:  30%|##9       | 742/2500 [00:07<00:17, 99.40it/s, loss=0.0223]
[Trellis Server] Texture baking (opt): optimizing:  30%|##9       | 743/2500 [00:07<00:17, 99.40it/s, loss=0.0147]
[Trellis Server] Texture baking (opt): optimizing:  30%|##9       | 744/2500 [00:07<00:17, 99.40it/s, loss=0.0101]
[Trellis Server] Texture baking (opt): optimizing:  30%|##9       | 745/2500 [00:07<00:17, 99.40it/s, loss=0.02]
[Trellis Server] Texture baking (opt): optimizing:  30%|##9       | 746/2500 [00:07<00:17, 99.40it/s, loss=0.017]
Texture baking (opt): optimizing:  30%|##9       | 748/2500 [00:08<00:17, 99.58it/s, loss=0.0107]t/s, loss=0.0107]
[Trellis Server] Texture baking (opt): optimizing:  30%|##9       | 748/2500 [00:08<00:17, 99.58it/s, loss=0.0107]
[Trellis Server] Texture baking (opt): optimizing:  30%|##9       | 749/2500 [00:08<00:17, 99.58it/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  30%|###       | 750/2500 [00:08<00:17, 99.58it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  30%|###       | 751/2500 [00:08<00:17, 99.58it/s, loss=0.0173]
[Trellis Server] Texture baking (opt): optimizing:  30%|###       | 752/2500 [00:08<00:17, 99.58it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  30%|###       | 753/2500 [00:08<00:17, 99.58it/s, loss=0.015]
[Trellis Server] Texture baking (opt): optimizing:  30%|###       | 754/2500 [00:08<00:17, 99.58it/s, loss=0.0146]
[Trellis Server] Texture baking (opt): optimizing:  30%|###       | 755/2500 [00:08<00:17, 99.58it/s, loss=0.0166]
[Trellis Server] Texture baking (opt): optimizing:  30%|###       | 756/2500 [00:08<00:17, 99.58it/s, loss=0.0118]
Texture baking (opt): optimizing:  30%|###       | 758/2500 [00:08<00:17, 97.68it/s, loss=0.0166]t/s, loss=0.0166]
[Trellis Server] Texture baking (opt): optimizing:  30%|###       | 758/2500 [00:08<00:17, 97.68it/s, loss=0.0169]
[Trellis Server] Texture baking (opt): optimizing:  30%|###       | 759/2500 [00:08<00:17, 97.68it/s, loss=0.0147]
[Trellis Server] Texture baking (opt): optimizing:  30%|###       | 760/2500 [00:08<00:17, 97.68it/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  30%|###       | 761/2500 [00:08<00:17, 97.68it/s, loss=0.0141]
[Trellis Server] Texture baking (opt): optimizing:  30%|###       | 762/2500 [00:08<00:17, 97.68it/s, loss=0.00853]
[Trellis Server] Texture baking (opt): optimizing:  31%|###       | 763/2500 [00:08<00:17, 97.68it/s, loss=0.0147]
[Trellis Server] Texture baking (opt): optimizing:  31%|###       | 764/2500 [00:08<00:17, 97.68it/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:  31%|###       | 765/2500 [00:08<00:17, 97.68it/s, loss=0.0129]
[Trellis Server] Texture baking (opt): optimizing:  31%|###       | 766/2500 [00:08<00:17, 97.68it/s, loss=0.00937]
Texture baking (opt): optimizing:  31%|###       | 768/2500 [00:08<00:18, 95.79it/s, loss=0.022]it/s, loss=0.022]
[Trellis Server] Texture baking (opt): optimizing:  31%|###       | 768/2500 [00:08<00:18, 95.79it/s, loss=0.0142]
[Trellis Server] Texture baking (opt): optimizing:  31%|###       | 769/2500 [00:08<00:18, 95.79it/s, loss=0.0159]
[Trellis Server] Texture baking (opt): optimizing:  31%|###       | 770/2500 [00:08<00:18, 95.79it/s, loss=0.0162]
[Trellis Server] Texture baking (opt): optimizing:  31%|###       | 771/2500 [00:08<00:18, 95.79it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  31%|###       | 772/2500 [00:08<00:18, 95.79it/s, loss=0.0088]
[Trellis Server] Texture baking (opt): optimizing:  31%|###       | 773/2500 [00:08<00:18, 95.79it/s, loss=0.0108]
[Trellis Server] Texture baking (opt): optimizing:  31%|###       | 774/2500 [00:08<00:18, 95.79it/s, loss=0.0164]
[Trellis Server] Texture baking (opt): optimizing:  31%|###1      | 775/2500 [00:08<00:18, 95.79it/s, loss=0.0172]
[Trellis Server] Texture baking (opt): optimizing:  31%|###1      | 776/2500 [00:08<00:17, 95.79it/s, loss=0.00849]
Texture baking (opt): optimizing:  31%|###1      | 778/2500 [00:08<00:18, 95.01it/s, loss=0.00593]/s, loss=0.00593]
[Trellis Server] Texture baking (opt): optimizing:  31%|###1      | 778/2500 [00:08<00:18, 95.01it/s, loss=0.0144]
[Trellis Server] Texture baking (opt): optimizing:  31%|###1      | 779/2500 [00:08<00:18, 95.01it/s, loss=0.0167]
[Trellis Server] Texture baking (opt): optimizing:  31%|###1      | 780/2500 [00:08<00:18, 95.01it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  31%|###1      | 781/2500 [00:08<00:18, 95.01it/s, loss=0.0229]
[Trellis Server] Texture baking (opt): optimizing:  31%|###1      | 782/2500 [00:08<00:18, 95.01it/s, loss=0.0172]
[Trellis Server] Texture baking (opt): optimizing:  31%|###1      | 783/2500 [00:08<00:18, 95.01it/s, loss=0.00872]
[Trellis Server] Texture baking (opt): optimizing:  31%|###1      | 784/2500 [00:08<00:18, 95.01it/s, loss=0.0258]
[Trellis Server] Texture baking (opt): optimizing:  31%|###1      | 785/2500 [00:08<00:18, 95.01it/s, loss=0.0254]
[Trellis Server] Texture baking (opt): optimizing:  31%|###1      | 786/2500 [00:08<00:18, 95.01it/s, loss=0.0171]
Texture baking (opt): optimizing:  32%|###1      | 788/2500 [00:08<00:17, 96.15it/s, loss=0.015]it/s, loss=0.015]
[Trellis Server] Texture baking (opt): optimizing:  32%|###1      | 788/2500 [00:08<00:17, 96.15it/s, loss=0.0152]
[Trellis Server] Texture baking (opt): optimizing:  32%|###1      | 789/2500 [00:08<00:17, 96.15it/s, loss=0.0138]
[Trellis Server] Texture baking (opt): optimizing:  32%|###1      | 790/2500 [00:08<00:17, 96.15it/s, loss=0.00963]
[Trellis Server] Texture baking (opt): optimizing:  32%|###1      | 791/2500 [00:08<00:17, 96.15it/s, loss=0.00972]
[Trellis Server] Texture baking (opt): optimizing:  32%|###1      | 792/2500 [00:08<00:17, 96.15it/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  32%|###1      | 793/2500 [00:08<00:17, 96.15it/s, loss=0.0145]
[Trellis Server] Texture baking (opt): optimizing:  32%|###1      | 794/2500 [00:08<00:17, 96.15it/s, loss=0.0132]
[Trellis Server] Texture baking (opt): optimizing:  32%|###1      | 795/2500 [00:08<00:17, 96.15it/s, loss=0.0136]
[Trellis Server] Texture baking (opt): optimizing:  32%|###1      | 796/2500 [00:08<00:17, 96.15it/s, loss=0.0149]
Texture baking (opt): optimizing:  32%|###1      | 798/2500 [00:08<00:17, 96.16it/s, loss=0.015]it/s, loss=0.015]
[Trellis Server] Texture baking (opt): optimizing:  32%|###1      | 798/2500 [00:08<00:17, 96.16it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  32%|###1      | 799/2500 [00:08<00:17, 96.16it/s, loss=0.0189]
[Trellis Server] Texture baking (opt): optimizing:  32%|###2      | 800/2500 [00:08<00:17, 96.16it/s, loss=0.0143]
[Trellis Server] Texture baking (opt): optimizing:  32%|###2      | 801/2500 [00:08<00:17, 96.16it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  32%|###2      | 802/2500 [00:08<00:17, 96.16it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  32%|###2      | 803/2500 [00:08<00:17, 96.16it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  32%|###2      | 804/2500 [00:08<00:17, 96.16it/s, loss=0.0146]
[Trellis Server] Texture baking (opt): optimizing:  32%|###2      | 805/2500 [00:08<00:17, 96.16it/s, loss=0.0101]
[Trellis Server] Texture baking (opt): optimizing:  32%|###2      | 806/2500 [00:08<00:17, 96.16it/s, loss=0.0124]
Texture baking (opt): optimizing:  32%|###2      | 808/2500 [00:08<00:17, 96.99it/s, loss=0.0165]t/s, loss=0.0165]
[Trellis Server] Texture baking (opt): optimizing:  32%|###2      | 808/2500 [00:08<00:17, 96.99it/s, loss=0.0128]
[Trellis Server] Texture baking (opt): optimizing:  32%|###2      | 809/2500 [00:08<00:17, 96.99it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  32%|###2      | 810/2500 [00:08<00:17, 96.99it/s, loss=0.017]
[Trellis Server] Texture baking (opt): optimizing:  32%|###2      | 811/2500 [00:08<00:17, 96.99it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  32%|###2      | 812/2500 [00:08<00:17, 96.99it/s, loss=0.0153]
[Trellis Server] Texture baking (opt): optimizing:  33%|###2      | 813/2500 [00:08<00:17, 96.99it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  33%|###2      | 814/2500 [00:08<00:17, 96.99it/s, loss=0.00846]
[Trellis Server] Texture baking (opt): optimizing:  33%|###2      | 815/2500 [00:08<00:17, 96.99it/s, loss=0.0174]
[Trellis Server] Texture baking (opt): optimizing:  33%|###2      | 816/2500 [00:08<00:17, 96.99it/s, loss=0.0214]
[Trellis Server] Texture baking (opt): optimizing:  33%|###2      | 817/2500 [00:08<00:17, 96.99it/s, loss=0.00688]
Texture baking (opt): optimizing:  33%|###2      | 819/2500 [00:08<00:17, 97.65it/s, loss=0.0082]t/s, loss=0.0082]
[Trellis Server] Texture baking (opt): optimizing:  33%|###2      | 819/2500 [00:08<00:17, 97.65it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  33%|###2      | 820/2500 [00:08<00:17, 97.65it/s, loss=0.0105]
[Trellis Server] Texture baking (opt): optimizing:  33%|###2      | 821/2500 [00:08<00:17, 97.65it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  33%|###2      | 822/2500 [00:08<00:17, 97.65it/s, loss=0.00867]
[Trellis Server] Texture baking (opt): optimizing:  33%|###2      | 823/2500 [00:08<00:17, 97.65it/s, loss=0.0167]
[Trellis Server] Texture baking (opt): optimizing:  33%|###2      | 824/2500 [00:08<00:17, 97.65it/s, loss=0.0192]
[Trellis Server] Texture baking (opt): optimizing:  33%|###3      | 825/2500 [00:08<00:17, 97.65it/s, loss=0.015]
[Trellis Server] Texture baking (opt): optimizing:  33%|###3      | 826/2500 [00:08<00:17, 97.65it/s, loss=0.0123]
[Trellis Server] Texture baking (opt): optimizing:  33%|###3      | 827/2500 [00:08<00:17, 97.65it/s, loss=0.0107]
Texture baking (opt): optimizing:  33%|###3      | 829/2500 [00:08<00:17, 98.05it/s, loss=0.0151]t/s, loss=0.0151]
[Trellis Server] Texture baking (opt): optimizing:  33%|###3      | 829/2500 [00:08<00:17, 98.05it/s, loss=0.0155]
[Trellis Server] Texture baking (opt): optimizing:  33%|###3      | 830/2500 [00:08<00:17, 98.05it/s, loss=0.00885]
[Trellis Server] Texture baking (opt): optimizing:  33%|###3      | 831/2500 [00:08<00:17, 98.05it/s, loss=0.0122]
[Trellis Server] Texture baking (opt): optimizing:  33%|###3      | 832/2500 [00:08<00:17, 98.05it/s, loss=0.0216]
[Trellis Server] Texture baking (opt): optimizing:  33%|###3      | 833/2500 [00:08<00:17, 98.05it/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:  33%|###3      | 834/2500 [00:08<00:16, 98.05it/s, loss=0.0153]
[Trellis Server] Texture baking (opt): optimizing:  33%|###3      | 835/2500 [00:08<00:16, 98.05it/s, loss=0.0142]
[Trellis Server] Texture baking (opt): optimizing:  33%|###3      | 836/2500 [00:08<00:16, 98.05it/s, loss=0.00441]
[Trellis Server] Texture baking (opt): optimizing:  33%|###3      | 837/2500 [00:08<00:16, 98.05it/s, loss=0.0176]
[Trellis Server] Texture baking (opt): optimizing:  34%|###3      | 838/2500 [00:08<00:16, 98.05it/s, loss=0.00997]
Texture baking (opt): optimizing:  34%|###3      | 840/2500 [00:08<00:16, 98.94it/s, loss=0.0116]t/s, loss=0.0116]
[Trellis Server] Texture baking (opt): optimizing:  34%|###3      | 840/2500 [00:08<00:16, 98.94it/s, loss=0.0146]
[Trellis Server] Texture baking (opt): optimizing:  34%|###3      | 841/2500 [00:08<00:16, 98.94it/s, loss=0.00971]
[Trellis Server] Texture baking (opt): optimizing:  34%|###3      | 842/2500 [00:08<00:16, 98.94it/s, loss=0.012]
[Trellis Server] Texture baking (opt): optimizing:  34%|###3      | 843/2500 [00:08<00:16, 98.94it/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  34%|###3      | 844/2500 [00:09<00:16, 98.94it/s, loss=0.0164]
[Trellis Server] Texture baking (opt): optimizing:  34%|###3      | 845/2500 [00:09<00:16, 98.94it/s, loss=0.012]
[Trellis Server] Texture baking (opt): optimizing:  34%|###3      | 846/2500 [00:09<00:16, 98.94it/s, loss=0.0179]
[Trellis Server] Texture baking (opt): optimizing:  34%|###3      | 847/2500 [00:09<00:16, 98.94it/s, loss=0.0151]
[Trellis Server] Texture baking (opt): optimizing:  34%|###3      | 848/2500 [00:09<00:16, 98.94it/s, loss=0.0141]
Texture baking (opt): optimizing:  34%|###4      | 850/2500 [00:09<00:16, 98.11it/s, loss=0.0143]t/s, loss=0.0143]
[Trellis Server] Texture baking (opt): optimizing:  34%|###4      | 850/2500 [00:09<00:16, 98.11it/s, loss=0.0116]
[Trellis Server] Texture baking (opt): optimizing:  34%|###4      | 851/2500 [00:09<00:16, 98.11it/s, loss=0.0153]
[Trellis Server] Texture baking (opt): optimizing:  34%|###4      | 852/2500 [00:09<00:16, 98.11it/s, loss=0.0128]
[Trellis Server] Texture baking (opt): optimizing:  34%|###4      | 853/2500 [00:09<00:16, 98.11it/s, loss=0.0196]
[Trellis Server] Texture baking (opt): optimizing:  34%|###4      | 854/2500 [00:09<00:16, 98.11it/s, loss=0.0121]
[Trellis Server] Texture baking (opt): optimizing:  34%|###4      | 855/2500 [00:09<00:16, 98.11it/s, loss=0.0149]
[Trellis Server] Texture baking (opt): optimizing:  34%|###4      | 856/2500 [00:09<00:16, 98.11it/s, loss=0.00912]
[Trellis Server] Texture baking (opt): optimizing:  34%|###4      | 857/2500 [00:09<00:16, 98.11it/s, loss=0.0223]
[Trellis Server] Texture baking (opt): optimizing:  34%|###4      | 858/2500 [00:09<00:16, 98.11it/s, loss=0.0154]
Texture baking (opt): optimizing:  34%|###4      | 860/2500 [00:09<00:16, 98.00it/s, loss=0.0103]t/s, loss=0.0103]
[Trellis Server] Texture baking (opt): optimizing:  34%|###4      | 860/2500 [00:09<00:16, 98.00it/s, loss=0.0146]
[Trellis Server] Texture baking (opt): optimizing:  34%|###4      | 861/2500 [00:09<00:16, 98.00it/s, loss=0.0187]
[Trellis Server] Texture baking (opt): optimizing:  34%|###4      | 862/2500 [00:09<00:16, 98.00it/s, loss=0.0122]
[Trellis Server] Texture baking (opt): optimizing:  35%|###4      | 863/2500 [00:09<00:16, 98.00it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  35%|###4      | 864/2500 [00:09<00:16, 98.00it/s, loss=0.0129]
[Trellis Server] Texture baking (opt): optimizing:  35%|###4      | 865/2500 [00:09<00:16, 98.00it/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  35%|###4      | 866/2500 [00:09<00:16, 98.00it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  35%|###4      | 867/2500 [00:09<00:16, 98.00it/s, loss=0.0171]
[Trellis Server] Texture baking (opt): optimizing:  35%|###4      | 868/2500 [00:09<00:16, 98.00it/s, loss=0.0131]
Texture baking (opt): optimizing:  35%|###4      | 870/2500 [00:09<00:16, 98.01it/s, loss=0.015]it/s, loss=0.015]
[Trellis Server] Texture baking (opt): optimizing:  35%|###4      | 870/2500 [00:09<00:16, 98.01it/s, loss=0.0143]
[Trellis Server] Texture baking (opt): optimizing:  35%|###4      | 871/2500 [00:09<00:16, 98.01it/s, loss=0.0102]
[Trellis Server] Texture baking (opt): optimizing:  35%|###4      | 872/2500 [00:09<00:16, 98.01it/s, loss=0.0129]
[Trellis Server] Texture baking (opt): optimizing:  35%|###4      | 873/2500 [00:09<00:16, 98.01it/s, loss=0.0209]
[Trellis Server] Texture baking (opt): optimizing:  35%|###4      | 874/2500 [00:09<00:16, 98.01it/s, loss=0.0135]
[Trellis Server] Texture baking (opt): optimizing:  35%|###5      | 875/2500 [00:09<00:16, 98.01it/s, loss=0.0329]
[Trellis Server] Texture baking (opt): optimizing:  35%|###5      | 876/2500 [00:09<00:16, 98.01it/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:  35%|###5      | 877/2500 [00:09<00:16, 98.01it/s, loss=0.0129]
[Trellis Server] Texture baking (opt): optimizing:  35%|###5      | 878/2500 [00:09<00:16, 98.01it/s, loss=0.0158]
Texture baking (opt): optimizing:  35%|###5      | 880/2500 [00:09<00:16, 98.30it/s, loss=0.0136]t/s, loss=0.0136]
[Trellis Server] Texture baking (opt): optimizing:  35%|###5      | 880/2500 [00:09<00:16, 98.30it/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  35%|###5      | 881/2500 [00:09<00:16, 98.30it/s, loss=0.0146]
[Trellis Server] Texture baking (opt): optimizing:  35%|###5      | 882/2500 [00:09<00:16, 98.30it/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:  35%|###5      | 883/2500 [00:09<00:16, 98.30it/s, loss=0.0108]
[Trellis Server] Texture baking (opt): optimizing:  35%|###5      | 884/2500 [00:09<00:16, 98.30it/s, loss=0.012]
[Trellis Server] Texture baking (opt): optimizing:  35%|###5      | 885/2500 [00:09<00:16, 98.30it/s, loss=0.0152]
[Trellis Server] Texture baking (opt): optimizing:  35%|###5      | 886/2500 [00:09<00:16, 98.30it/s, loss=0.017]
[Trellis Server] Texture baking (opt): optimizing:  35%|###5      | 887/2500 [00:09<00:16, 98.30it/s, loss=0.01]
[Trellis Server] Texture baking (opt): optimizing:  36%|###5      | 888/2500 [00:09<00:16, 98.30it/s, loss=0.00972]
Texture baking (opt): optimizing:  36%|###5      | 890/2500 [00:09<00:16, 98.22it/s, loss=0.0153]t/s, loss=0.0153]
[Trellis Server] Texture baking (opt): optimizing:  36%|###5      | 890/2500 [00:09<00:16, 98.22it/s, loss=0.0224]
[Trellis Server] Texture baking (opt): optimizing:  36%|###5      | 891/2500 [00:09<00:16, 98.22it/s, loss=0.0145]
[Trellis Server] Texture baking (opt): optimizing:  36%|###5      | 892/2500 [00:09<00:16, 98.22it/s, loss=0.0175]
[Trellis Server] Texture baking (opt): optimizing:  36%|###5      | 893/2500 [00:09<00:16, 98.22it/s, loss=0.0145]
[Trellis Server] Texture baking (opt): optimizing:  36%|###5      | 894/2500 [00:09<00:16, 98.22it/s, loss=0.0164]
[Trellis Server] Texture baking (opt): optimizing:  36%|###5      | 895/2500 [00:09<00:16, 98.22it/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  36%|###5      | 896/2500 [00:09<00:16, 98.22it/s, loss=0.015]
[Trellis Server] Texture baking (opt): optimizing:  36%|###5      | 897/2500 [00:09<00:16, 98.22it/s, loss=0.0131]
[Trellis Server] Texture baking (opt): optimizing:  36%|###5      | 898/2500 [00:09<00:16, 98.22it/s, loss=0.0132]
Texture baking (opt): optimizing:  36%|###6      | 900/2500 [00:09<00:16, 98.46it/s, loss=0.0111]t/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  36%|###6      | 900/2500 [00:09<00:16, 98.46it/s, loss=0.0108]
[Trellis Server] Texture baking (opt): optimizing:  36%|###6      | 901/2500 [00:09<00:16, 98.46it/s, loss=0.0155]
[Trellis Server] Texture baking (opt): optimizing:  36%|###6      | 902/2500 [00:09<00:16, 98.46it/s, loss=0.016]
[Trellis Server] Texture baking (opt): optimizing:  36%|###6      | 903/2500 [00:09<00:16, 98.46it/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  36%|###6      | 904/2500 [00:09<00:16, 98.46it/s, loss=0.0157]
[Trellis Server] Texture baking (opt): optimizing:  36%|###6      | 905/2500 [00:09<00:16, 98.46it/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  36%|###6      | 906/2500 [00:09<00:16, 98.46it/s, loss=0.00753]
[Trellis Server] Texture baking (opt): optimizing:  36%|###6      | 907/2500 [00:09<00:16, 98.46it/s, loss=0.0189]
[Trellis Server] Texture baking (opt): optimizing:  36%|###6      | 908/2500 [00:09<00:16, 98.46it/s, loss=0.00725]
[Trellis Server] Texture baking (opt): optimizing:  36%|###6      | 909/2500 [00:09<00:16, 98.46it/s, loss=0.0112]
Texture baking (opt): optimizing:  36%|###6      | 911/2500 [00:09<00:16, 98.95it/s, loss=0.0127]t/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  36%|###6      | 911/2500 [00:09<00:16, 98.95it/s, loss=0.0068]
[Trellis Server] Texture baking (opt): optimizing:  36%|###6      | 912/2500 [00:09<00:16, 98.95it/s, loss=0.0102]
[Trellis Server] Texture baking (opt): optimizing:  37%|###6      | 913/2500 [00:09<00:16, 98.95it/s, loss=0.00639]
[Trellis Server] Texture baking (opt): optimizing:  37%|###6      | 914/2500 [00:09<00:16, 98.95it/s, loss=0.0235]
[Trellis Server] Texture baking (opt): optimizing:  37%|###6      | 915/2500 [00:09<00:16, 98.95it/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  37%|###6      | 916/2500 [00:09<00:16, 98.95it/s, loss=0.0235]
[Trellis Server] Texture baking (opt): optimizing:  37%|###6      | 917/2500 [00:09<00:15, 98.95it/s, loss=0.0157]
[Trellis Server] Texture baking (opt): optimizing:  37%|###6      | 918/2500 [00:09<00:15, 98.95it/s, loss=0.02]
[Trellis Server] Texture baking (opt): optimizing:  37%|###6      | 919/2500 [00:09<00:15, 98.95it/s, loss=0.0205]
Texture baking (opt): optimizing:  37%|###6      | 921/2500 [00:09<00:16, 98.41it/s, loss=0.0193]t/s, loss=0.0193]
[Trellis Server] Texture baking (opt): optimizing:  37%|###6      | 921/2500 [00:09<00:16, 98.41it/s, loss=0.00856]
[Trellis Server] Texture baking (opt): optimizing:  37%|###6      | 922/2500 [00:09<00:16, 98.41it/s, loss=0.0171]
[Trellis Server] Texture baking (opt): optimizing:  37%|###6      | 923/2500 [00:09<00:16, 98.41it/s, loss=0.0131]
[Trellis Server] Texture baking (opt): optimizing:  37%|###6      | 924/2500 [00:09<00:16, 98.41it/s, loss=0.0143]
[Trellis Server] Texture baking (opt): optimizing:  37%|###7      | 925/2500 [00:09<00:16, 98.41it/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:  37%|###7      | 926/2500 [00:09<00:15, 98.41it/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:  37%|###7      | 927/2500 [00:09<00:15, 98.41it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  37%|###7      | 928/2500 [00:09<00:15, 98.41it/s, loss=0.0215]
[Trellis Server] Texture baking (opt): optimizing:  37%|###7      | 929/2500 [00:09<00:15, 98.41it/s, loss=0.0152]
Texture baking (opt): optimizing:  37%|###7      | 931/2500 [00:09<00:15, 98.57it/s, loss=0.0105]t/s, loss=0.0105]
[Trellis Server] Texture baking (opt): optimizing:  37%|###7      | 931/2500 [00:09<00:15, 98.57it/s, loss=0.0144]
[Trellis Server] Texture baking (opt): optimizing:  37%|###7      | 932/2500 [00:09<00:15, 98.57it/s, loss=0.0177]
[Trellis Server] Texture baking (opt): optimizing:  37%|###7      | 933/2500 [00:09<00:15, 98.57it/s, loss=0.0175]
[Trellis Server] Texture baking (opt): optimizing:  37%|###7      | 934/2500 [00:09<00:15, 98.57it/s, loss=0.0101]
[Trellis Server] Texture baking (opt): optimizing:  37%|###7      | 935/2500 [00:09<00:15, 98.57it/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  37%|###7      | 936/2500 [00:09<00:15, 98.57it/s, loss=0.0151]
[Trellis Server] Texture baking (opt): optimizing:  37%|###7      | 937/2500 [00:09<00:15, 98.57it/s, loss=0.0138]
[Trellis Server] Texture baking (opt): optimizing:  38%|###7      | 938/2500 [00:09<00:15, 98.57it/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing:  38%|###7      | 939/2500 [00:09<00:15, 98.57it/s, loss=0.0168]
[Trellis Server] Texture baking (opt): optimizing:  38%|###7      | 940/2500 [00:09<00:15, 98.57it/s, loss=0.0232]
Texture baking (opt): optimizing:  38%|###7      | 942/2500 [00:09<00:15, 99.02it/s, loss=0.0153]t/s, loss=0.0153]
[Trellis Server] Texture baking (opt): optimizing:  38%|###7      | 942/2500 [00:10<00:15, 99.02it/s, loss=0.0164]
[Trellis Server] Texture baking (opt): optimizing:  38%|###7      | 943/2500 [00:10<00:15, 99.02it/s, loss=0.0161]
[Trellis Server] Texture baking (opt): optimizing:  38%|###7      | 944/2500 [00:10<00:15, 99.02it/s, loss=0.0155]
[Trellis Server] Texture baking (opt): optimizing:  38%|###7      | 945/2500 [00:10<00:15, 99.02it/s, loss=0.0166]
[Trellis Server] Texture baking (opt): optimizing:  38%|###7      | 946/2500 [00:10<00:15, 99.02it/s, loss=0.0148]
[Trellis Server] Texture baking (opt): optimizing:  38%|###7      | 947/2500 [00:10<00:15, 99.02it/s, loss=0.016]
[Trellis Server] Texture baking (opt): optimizing:  38%|###7      | 948/2500 [00:10<00:15, 99.02it/s, loss=0.01]
[Trellis Server] Texture baking (opt): optimizing:  38%|###7      | 949/2500 [00:10<00:15, 99.02it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  38%|###8      | 950/2500 [00:10<00:15, 99.02it/s, loss=0.0203]
Texture baking (opt): optimizing:  38%|###8      | 952/2500 [00:10<00:15, 99.19it/s, loss=0.0121]t/s, loss=0.0121]
[Trellis Server] Texture baking (opt): optimizing:  38%|###8      | 952/2500 [00:10<00:15, 99.19it/s, loss=0.0158]
[Trellis Server] Texture baking (opt): optimizing:  38%|###8      | 953/2500 [00:10<00:15, 99.19it/s, loss=0.0132]
[Trellis Server] Texture baking (opt): optimizing:  38%|###8      | 954/2500 [00:10<00:15, 99.19it/s, loss=0.0173]
[Trellis Server] Texture baking (opt): optimizing:  38%|###8      | 955/2500 [00:10<00:15, 99.19it/s, loss=0.0159]
[Trellis Server] Texture baking (opt): optimizing:  38%|###8      | 956/2500 [00:10<00:15, 99.19it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  38%|###8      | 957/2500 [00:10<00:15, 99.19it/s, loss=0.0143]
[Trellis Server] Texture baking (opt): optimizing:  38%|###8      | 958/2500 [00:10<00:15, 99.19it/s, loss=0.0168]
[Trellis Server] Texture baking (opt): optimizing:  38%|###8      | 959/2500 [00:10<00:15, 99.19it/s, loss=0.0137]
[Trellis Server] Texture baking (opt): optimizing:  38%|###8      | 960/2500 [00:10<00:15, 99.19it/s, loss=0.0199]
Texture baking (opt): optimizing:  38%|###8      | 962/2500 [00:10<00:15, 98.85it/s, loss=0.0174]t/s, loss=0.0174]
[Trellis Server] Texture baking (opt): optimizing:  38%|###8      | 962/2500 [00:10<00:15, 98.85it/s, loss=0.0087]
[Trellis Server] Texture baking (opt): optimizing:  39%|###8      | 963/2500 [00:10<00:15, 98.85it/s, loss=0.0218]
[Trellis Server] Texture baking (opt): optimizing:  39%|###8      | 964/2500 [00:10<00:15, 98.85it/s, loss=0.019]
[Trellis Server] Texture baking (opt): optimizing:  39%|###8      | 965/2500 [00:10<00:15, 98.85it/s, loss=0.0138]
[Trellis Server] Texture baking (opt): optimizing:  39%|###8      | 966/2500 [00:10<00:15, 98.85it/s, loss=0.00973]
[Trellis Server] Texture baking (opt): optimizing:  39%|###8      | 967/2500 [00:10<00:15, 98.85it/s, loss=0.0162]
[Trellis Server] Texture baking (opt): optimizing:  39%|###8      | 968/2500 [00:10<00:15, 98.85it/s, loss=0.015]
[Trellis Server] Texture baking (opt): optimizing:  39%|###8      | 969/2500 [00:10<00:15, 98.85it/s, loss=0.0153]
[Trellis Server] Texture baking (opt): optimizing:  39%|###8      | 970/2500 [00:10<00:15, 98.85it/s, loss=0.0139]
Texture baking (opt): optimizing:  39%|###8      | 972/2500 [00:10<00:15, 98.90it/s, loss=0.0144]t/s, loss=0.0144]
[Trellis Server] Texture baking (opt): optimizing:  39%|###8      | 972/2500 [00:10<00:15, 98.90it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  39%|###8      | 973/2500 [00:10<00:15, 98.90it/s, loss=0.0124]
[Trellis Server] Texture baking (opt): optimizing:  39%|###8      | 974/2500 [00:10<00:15, 98.90it/s, loss=0.0149]
[Trellis Server] Texture baking (opt): optimizing:  39%|###9      | 975/2500 [00:10<00:15, 98.90it/s, loss=0.0122]
[Trellis Server] Texture baking (opt): optimizing:  39%|###9      | 976/2500 [00:10<00:15, 98.90it/s, loss=0.02]
[Trellis Server] Texture baking (opt): optimizing:  39%|###9      | 977/2500 [00:10<00:15, 98.90it/s, loss=0.0142]
[Trellis Server] Texture baking (opt): optimizing:  39%|###9      | 978/2500 [00:10<00:15, 98.90it/s, loss=0.0138]
[Trellis Server] Texture baking (opt): optimizing:  39%|###9      | 979/2500 [00:10<00:15, 98.90it/s, loss=0.0149]
[Trellis Server] Texture baking (opt): optimizing:  39%|###9      | 980/2500 [00:10<00:15, 98.90it/s, loss=0.00857]
Texture baking (opt): optimizing:  39%|###9      | 982/2500 [00:10<00:15, 98.93it/s, loss=0.0171]t/s, loss=0.0171]
[Trellis Server] Texture baking (opt): optimizing:  39%|###9      | 982/2500 [00:10<00:15, 98.93it/s, loss=0.0105]
[Trellis Server] Texture baking (opt): optimizing:  39%|###9      | 983/2500 [00:10<00:15, 98.93it/s, loss=0.0135]
[Trellis Server] Texture baking (opt): optimizing:  39%|###9      | 984/2500 [00:10<00:15, 98.93it/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  39%|###9      | 985/2500 [00:10<00:15, 98.93it/s, loss=0.012]
[Trellis Server] Texture baking (opt): optimizing:  39%|###9      | 986/2500 [00:10<00:15, 98.93it/s, loss=0.0155]
[Trellis Server] Texture baking (opt): optimizing:  39%|###9      | 987/2500 [00:10<00:15, 98.93it/s, loss=0.0133]
[Trellis Server] Texture baking (opt): optimizing:  40%|###9      | 988/2500 [00:10<00:15, 98.93it/s, loss=0.017]
[Trellis Server] Texture baking (opt): optimizing:  40%|###9      | 989/2500 [00:10<00:15, 98.93it/s, loss=0.0158]
[Trellis Server] Texture baking (opt): optimizing:  40%|###9      | 990/2500 [00:10<00:15, 98.93it/s, loss=0.0161]
Texture baking (opt): optimizing:  40%|###9      | 992/2500 [00:10<00:15, 98.08it/s, loss=0.0163]t/s, loss=0.0163]
[Trellis Server] Texture baking (opt): optimizing:  40%|###9      | 992/2500 [00:10<00:15, 98.08it/s, loss=0.012]
[Trellis Server] Texture baking (opt): optimizing:  40%|###9      | 993/2500 [00:10<00:15, 98.08it/s, loss=0.0199]
[Trellis Server] Texture baking (opt): optimizing:  40%|###9      | 994/2500 [00:10<00:15, 98.08it/s, loss=0.0118]
[Trellis Server] Texture baking (opt): optimizing:  40%|###9      | 995/2500 [00:10<00:15, 98.08it/s, loss=0.014]
[Trellis Server] Texture baking (opt): optimizing:  40%|###9      | 996/2500 [00:10<00:15, 98.08it/s, loss=0.0155]
[Trellis Server] Texture baking (opt): optimizing:  40%|###9      | 997/2500 [00:10<00:15, 98.08it/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:  40%|###9      | 998/2500 [00:10<00:15, 98.08it/s, loss=0.00913]
[Trellis Server] Texture baking (opt): optimizing:  40%|###9      | 999/2500 [00:10<00:15, 98.08it/s, loss=0.014]
[Trellis Server] Texture baking (opt): optimizing:  40%|####      | 1000/2500 [00:10<00:15, 98.08it/s, loss=0.00734]
Texture baking (opt): optimizing:  40%|####      | 1002/2500 [00:10<00:15, 95.28it/s, loss=0.0134]t/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  40%|####      | 1002/2500 [00:10<00:15, 95.28it/s, loss=0.0143]
[Trellis Server] Texture baking (opt): optimizing:  40%|####      | 1003/2500 [00:10<00:15, 95.28it/s, loss=0.014]
[Trellis Server] Texture baking (opt): optimizing:  40%|####      | 1004/2500 [00:10<00:15, 95.28it/s, loss=0.0132]
[Trellis Server] Texture baking (opt): optimizing:  40%|####      | 1005/2500 [00:10<00:15, 95.28it/s, loss=0.0133]
[Trellis Server] Texture baking (opt): optimizing:  40%|####      | 1006/2500 [00:10<00:15, 95.28it/s, loss=0.0121]
[Trellis Server] Texture baking (opt): optimizing:  40%|####      | 1007/2500 [00:10<00:15, 95.28it/s, loss=0.0147]
[Trellis Server] Texture baking (opt): optimizing:  40%|####      | 1008/2500 [00:10<00:15, 95.28it/s, loss=0.0161]
[Trellis Server] Texture baking (opt): optimizing:  40%|####      | 1009/2500 [00:10<00:15, 95.28it/s, loss=0.00832]
[Trellis Server] Texture baking (opt): optimizing:  40%|####      | 1010/2500 [00:10<00:15, 95.28it/s, loss=0.0161]
Texture baking (opt): optimizing:  40%|####      | 1012/2500 [00:10<00:15, 93.60it/s, loss=0.0128]t/s, loss=0.0128]
[Trellis Server] Texture baking (opt): optimizing:  40%|####      | 1012/2500 [00:10<00:15, 93.60it/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:  41%|####      | 1013/2500 [00:10<00:15, 93.60it/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  41%|####      | 1014/2500 [00:10<00:15, 93.60it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  41%|####      | 1015/2500 [00:10<00:15, 93.60it/s, loss=0.0146]
[Trellis Server] Texture baking (opt): optimizing:  41%|####      | 1016/2500 [00:10<00:15, 93.60it/s, loss=0.00964]
[Trellis Server] Texture baking (opt): optimizing:  41%|####      | 1017/2500 [00:10<00:15, 93.60it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  41%|####      | 1018/2500 [00:10<00:15, 93.60it/s, loss=0.015]
[Trellis Server] Texture baking (opt): optimizing:  41%|####      | 1019/2500 [00:10<00:15, 93.60it/s, loss=0.0208]
[Trellis Server] Texture baking (opt): optimizing:  41%|####      | 1020/2500 [00:10<00:15, 93.60it/s, loss=0.0102]
Texture baking (opt): optimizing:  41%|####      | 1022/2500 [00:10<00:15, 94.93it/s, loss=0.015]it/s, loss=0.015]
[Trellis Server] Texture baking (opt): optimizing:  41%|####      | 1022/2500 [00:10<00:15, 94.93it/s, loss=0.0141]
[Trellis Server] Texture baking (opt): optimizing:  41%|####      | 1023/2500 [00:10<00:15, 94.93it/s, loss=0.0083]
[Trellis Server] Texture baking (opt): optimizing:  41%|####      | 1024/2500 [00:10<00:15, 94.93it/s, loss=0.00881]
[Trellis Server] Texture baking (opt): optimizing:  41%|####1     | 1025/2500 [00:10<00:15, 94.93it/s, loss=0.0143]
[Trellis Server] Texture baking (opt): optimizing:  41%|####1     | 1026/2500 [00:10<00:15, 94.93it/s, loss=0.0218]
[Trellis Server] Texture baking (opt): optimizing:  41%|####1     | 1027/2500 [00:10<00:15, 94.93it/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:  41%|####1     | 1028/2500 [00:10<00:15, 94.93it/s, loss=0.0171]
[Trellis Server] Texture baking (opt): optimizing:  41%|####1     | 1029/2500 [00:10<00:15, 94.93it/s, loss=0.0154]
[Trellis Server] Texture baking (opt): optimizing:  41%|####1     | 1030/2500 [00:10<00:15, 94.93it/s, loss=0.00813]
Texture baking (opt): optimizing:  41%|####1     | 1032/2500 [00:10<00:15, 95.10it/s, loss=0.0177]t/s, loss=0.0177]
[Trellis Server] Texture baking (opt): optimizing:  41%|####1     | 1032/2500 [00:10<00:15, 95.10it/s, loss=0.0185]
[Trellis Server] Texture baking (opt): optimizing:  41%|####1     | 1033/2500 [00:10<00:15, 95.10it/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  41%|####1     | 1034/2500 [00:10<00:15, 95.10it/s, loss=0.00741]
[Trellis Server] Texture baking (opt): optimizing:  41%|####1     | 1035/2500 [00:10<00:15, 95.10it/s, loss=0.016]
[Trellis Server] Texture baking (opt): optimizing:  41%|####1     | 1036/2500 [00:10<00:15, 95.10it/s, loss=0.00799]
[Trellis Server] Texture baking (opt): optimizing:  41%|####1     | 1037/2500 [00:10<00:15, 95.10it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  42%|####1     | 1038/2500 [00:11<00:15, 95.10it/s, loss=0.015]
[Trellis Server] Texture baking (opt): optimizing:  42%|####1     | 1039/2500 [00:11<00:15, 95.10it/s, loss=0.0146]
[Trellis Server] Texture baking (opt): optimizing:  42%|####1     | 1040/2500 [00:11<00:15, 95.10it/s, loss=0.0165]
Texture baking (opt): optimizing:  42%|####1     | 1042/2500 [00:11<00:15, 95.68it/s, loss=0.0129]t/s, loss=0.0129]
[Trellis Server] Texture baking (opt): optimizing:  42%|####1     | 1042/2500 [00:11<00:15, 95.68it/s, loss=0.00755]
[Trellis Server] Texture baking (opt): optimizing:  42%|####1     | 1043/2500 [00:11<00:15, 95.68it/s, loss=0.0145]
[Trellis Server] Texture baking (opt): optimizing:  42%|####1     | 1044/2500 [00:11<00:15, 95.68it/s, loss=0.0121]
[Trellis Server] Texture baking (opt): optimizing:  42%|####1     | 1045/2500 [00:11<00:15, 95.68it/s, loss=0.0165]
[Trellis Server] Texture baking (opt): optimizing:  42%|####1     | 1046/2500 [00:11<00:15, 95.68it/s, loss=0.0104]
[Trellis Server] Texture baking (opt): optimizing:  42%|####1     | 1047/2500 [00:11<00:15, 95.68it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  42%|####1     | 1048/2500 [00:11<00:15, 95.68it/s, loss=0.0204]
[Trellis Server] Texture baking (opt): optimizing:  42%|####1     | 1049/2500 [00:11<00:15, 95.68it/s, loss=0.0169]
[Trellis Server] Texture baking (opt): optimizing:  42%|####2     | 1050/2500 [00:11<00:15, 95.68it/s, loss=0.0185]
[Trellis Server] Texture baking (opt): optimizing:  42%|####2     | 1051/2500 [00:11<00:15, 95.68it/s, loss=0.0106]
Texture baking (opt): optimizing:  42%|####2     | 1053/2500 [00:11<00:14, 97.02it/s, loss=0.0148]t/s, loss=0.0148]
[Trellis Server] Texture baking (opt): optimizing:  42%|####2     | 1053/2500 [00:11<00:14, 97.02it/s, loss=0.0136]
[Trellis Server] Texture baking (opt): optimizing:  42%|####2     | 1054/2500 [00:11<00:14, 97.02it/s, loss=0.0204]
[Trellis Server] Texture baking (opt): optimizing:  42%|####2     | 1055/2500 [00:11<00:14, 97.02it/s, loss=0.0169]
[Trellis Server] Texture baking (opt): optimizing:  42%|####2     | 1056/2500 [00:11<00:14, 97.02it/s, loss=0.0141]
[Trellis Server] Texture baking (opt): optimizing:  42%|####2     | 1057/2500 [00:11<00:14, 97.02it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  42%|####2     | 1058/2500 [00:11<00:14, 97.02it/s, loss=0.0129]
[Trellis Server] Texture baking (opt): optimizing:  42%|####2     | 1059/2500 [00:11<00:14, 97.02it/s, loss=0.021]
[Trellis Server] Texture baking (opt): optimizing:  42%|####2     | 1060/2500 [00:11<00:14, 97.02it/s, loss=0.0105]
[Trellis Server] Texture baking (opt): optimizing:  42%|####2     | 1061/2500 [00:11<00:14, 97.02it/s, loss=0.0103]
Texture baking (opt): optimizing:  43%|####2     | 1063/2500 [00:11<00:14, 97.33it/s, loss=0.016]it/s, loss=0.016]
[Trellis Server] Texture baking (opt): optimizing:  43%|####2     | 1063/2500 [00:11<00:14, 97.33it/s, loss=0.00967]
[Trellis Server] Texture baking (opt): optimizing:  43%|####2     | 1064/2500 [00:11<00:14, 97.33it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  43%|####2     | 1065/2500 [00:11<00:14, 97.33it/s, loss=0.00686]
[Trellis Server] Texture baking (opt): optimizing:  43%|####2     | 1066/2500 [00:11<00:14, 97.33it/s, loss=0.0222]
[Trellis Server] Texture baking (opt): optimizing:  43%|####2     | 1067/2500 [00:11<00:14, 97.33it/s, loss=0.0155]
[Trellis Server] Texture baking (opt): optimizing:  43%|####2     | 1068/2500 [00:11<00:14, 97.33it/s, loss=0.0146]
[Trellis Server] Texture baking (opt): optimizing:  43%|####2     | 1069/2500 [00:11<00:14, 97.33it/s, loss=0.0202]
[Trellis Server] Texture baking (opt): optimizing:  43%|####2     | 1070/2500 [00:11<00:14, 97.33it/s, loss=0.0181]
[Trellis Server] Texture baking (opt): optimizing:  43%|####2     | 1071/2500 [00:11<00:14, 97.33it/s, loss=0.0139]
Texture baking (opt): optimizing:  43%|####2     | 1073/2500 [00:11<00:14, 97.81it/s, loss=0.0109]t/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  43%|####2     | 1073/2500 [00:11<00:14, 97.81it/s, loss=0.0154]
[Trellis Server] Texture baking (opt): optimizing:  43%|####2     | 1074/2500 [00:11<00:14, 97.81it/s, loss=0.0118]
[Trellis Server] Texture baking (opt): optimizing:  43%|####3     | 1075/2500 [00:11<00:14, 97.81it/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:  43%|####3     | 1076/2500 [00:11<00:14, 97.81it/s, loss=0.0125]
[Trellis Server] Texture baking (opt): optimizing:  43%|####3     | 1077/2500 [00:11<00:14, 97.81it/s, loss=0.0138]
[Trellis Server] Texture baking (opt): optimizing:  43%|####3     | 1078/2500 [00:11<00:14, 97.81it/s, loss=0.0101]
[Trellis Server] Texture baking (opt): optimizing:  43%|####3     | 1079/2500 [00:11<00:14, 97.81it/s, loss=0.0238]
[Trellis Server] Texture baking (opt): optimizing:  43%|####3     | 1080/2500 [00:11<00:14, 97.81it/s, loss=0.00911]
[Trellis Server] Texture baking (opt): optimizing:  43%|####3     | 1081/2500 [00:11<00:14, 97.81it/s, loss=0.021]
Texture baking (opt): optimizing:  43%|####3     | 1083/2500 [00:11<00:14, 98.16it/s, loss=0.0135]t/s, loss=0.0135]
[Trellis Server] Texture baking (opt): optimizing:  43%|####3     | 1083/2500 [00:11<00:14, 98.16it/s, loss=0.0138]
[Trellis Server] Texture baking (opt): optimizing:  43%|####3     | 1084/2500 [00:11<00:14, 98.16it/s, loss=0.0144]
[Trellis Server] Texture baking (opt): optimizing:  43%|####3     | 1085/2500 [00:11<00:14, 98.16it/s, loss=0.0165]
[Trellis Server] Texture baking (opt): optimizing:  43%|####3     | 1086/2500 [00:11<00:14, 98.16it/s, loss=0.0131]
[Trellis Server] Texture baking (opt): optimizing:  43%|####3     | 1087/2500 [00:11<00:14, 98.16it/s, loss=0.0099]
[Trellis Server] Texture baking (opt): optimizing:  44%|####3     | 1088/2500 [00:11<00:14, 98.16it/s, loss=0.0143]
[Trellis Server] Texture baking (opt): optimizing:  44%|####3     | 1089/2500 [00:11<00:14, 98.16it/s, loss=0.0169]
[Trellis Server] Texture baking (opt): optimizing:  44%|####3     | 1090/2500 [00:11<00:14, 98.16it/s, loss=0.0167]
[Trellis Server] Texture baking (opt): optimizing:  44%|####3     | 1091/2500 [00:11<00:14, 98.16it/s, loss=0.0135]
Texture baking (opt): optimizing:  44%|####3     | 1093/2500 [00:11<00:14, 96.44it/s, loss=0.0128]t/s, loss=0.0128]
[Trellis Server] Texture baking (opt): optimizing:  44%|####3     | 1093/2500 [00:11<00:14, 96.44it/s, loss=0.0128]
[Trellis Server] Texture baking (opt): optimizing:  44%|####3     | 1094/2500 [00:11<00:14, 96.44it/s, loss=0.00969]
[Trellis Server] Texture baking (opt): optimizing:  44%|####3     | 1095/2500 [00:11<00:14, 96.44it/s, loss=0.0135]
[Trellis Server] Texture baking (opt): optimizing:  44%|####3     | 1096/2500 [00:11<00:14, 96.44it/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing:  44%|####3     | 1097/2500 [00:11<00:14, 96.44it/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing:  44%|####3     | 1098/2500 [00:11<00:14, 96.44it/s, loss=0.01]
[Trellis Server] Texture baking (opt): optimizing:  44%|####3     | 1099/2500 [00:11<00:14, 96.44it/s, loss=0.0143]
[Trellis Server] Texture baking (opt): optimizing:  44%|####4     | 1100/2500 [00:11<00:14, 96.44it/s, loss=0.0146]
[Trellis Server] Texture baking (opt): optimizing:  44%|####4     | 1101/2500 [00:11<00:14, 96.44it/s, loss=0.0211]
Texture baking (opt): optimizing:  44%|####4     | 1103/2500 [00:11<00:14, 97.19it/s, loss=0.016]it/s, loss=0.016]
[Trellis Server] Texture baking (opt): optimizing:  44%|####4     | 1103/2500 [00:11<00:14, 97.19it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  44%|####4     | 1104/2500 [00:11<00:14, 97.19it/s, loss=0.0194]
[Trellis Server] Texture baking (opt): optimizing:  44%|####4     | 1105/2500 [00:11<00:14, 97.19it/s, loss=0.00732]
[Trellis Server] Texture baking (opt): optimizing:  44%|####4     | 1106/2500 [00:11<00:14, 97.19it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  44%|####4     | 1107/2500 [00:11<00:14, 97.19it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  44%|####4     | 1108/2500 [00:11<00:14, 97.19it/s, loss=0.0162]
[Trellis Server] Texture baking (opt): optimizing:  44%|####4     | 1109/2500 [00:11<00:14, 97.19it/s, loss=0.0132]
[Trellis Server] Texture baking (opt): optimizing:  44%|####4     | 1110/2500 [00:11<00:14, 97.19it/s, loss=0.0159]
[Trellis Server] Texture baking (opt): optimizing:  44%|####4     | 1111/2500 [00:11<00:14, 97.19it/s, loss=0.0137]
[Trellis Server] Texture baking (opt): optimizing:  44%|####4     | 1112/2500 [00:11<00:14, 97.19it/s, loss=0.0165]
Texture baking (opt): optimizing:  45%|####4     | 1114/2500 [00:11<00:14, 98.35it/s, loss=0.0133]t/s, loss=0.0133]
[Trellis Server] Texture baking (opt): optimizing:  45%|####4     | 1114/2500 [00:11<00:14, 98.35it/s, loss=0.0151]
[Trellis Server] Texture baking (opt): optimizing:  45%|####4     | 1115/2500 [00:11<00:14, 98.35it/s, loss=0.0136]
[Trellis Server] Texture baking (opt): optimizing:  45%|####4     | 1116/2500 [00:11<00:14, 98.35it/s, loss=0.0107]
[Trellis Server] Texture baking (opt): optimizing:  45%|####4     | 1117/2500 [00:11<00:14, 98.35it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  45%|####4     | 1118/2500 [00:11<00:14, 98.35it/s, loss=0.0105]
[Trellis Server] Texture baking (opt): optimizing:  45%|####4     | 1119/2500 [00:11<00:14, 98.35it/s, loss=0.0153]
[Trellis Server] Texture baking (opt): optimizing:  45%|####4     | 1120/2500 [00:11<00:14, 98.35it/s, loss=0.00409]
[Trellis Server] Texture baking (opt): optimizing:  45%|####4     | 1121/2500 [00:11<00:14, 98.35it/s, loss=0.0136]
[Trellis Server] Texture baking (opt): optimizing:  45%|####4     | 1122/2500 [00:11<00:14, 98.35it/s, loss=0.0123]
Texture baking (opt): optimizing:  45%|####4     | 1124/2500 [00:11<00:13, 98.72it/s, loss=0.0112]t/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  45%|####4     | 1124/2500 [00:11<00:13, 98.72it/s, loss=0.0162]
[Trellis Server] Texture baking (opt): optimizing:  45%|####5     | 1125/2500 [00:11<00:13, 98.72it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  45%|####5     | 1126/2500 [00:11<00:13, 98.72it/s, loss=0.0154]
[Trellis Server] Texture baking (opt): optimizing:  45%|####5     | 1127/2500 [00:11<00:13, 98.72it/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing:  45%|####5     | 1128/2500 [00:11<00:13, 98.72it/s, loss=0.00762]
[Trellis Server] Texture baking (opt): optimizing:  45%|####5     | 1129/2500 [00:11<00:13, 98.72it/s, loss=0.0159]
[Trellis Server] Texture baking (opt): optimizing:  45%|####5     | 1130/2500 [00:11<00:13, 98.72it/s, loss=0.00898]
[Trellis Server] Texture baking (opt): optimizing:  45%|####5     | 1131/2500 [00:11<00:13, 98.72it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  45%|####5     | 1132/2500 [00:11<00:13, 98.72it/s, loss=0.0207]
[Trellis Server] Texture baking (opt): optimizing:  45%|####5     | 1133/2500 [00:11<00:13, 98.72it/s, loss=0.00835]
Texture baking (opt): optimizing:  45%|####5     | 1135/2500 [00:11<00:13, 99.12it/s, loss=0.015]it/s, loss=0.015]
[Trellis Server] Texture baking (opt): optimizing:  45%|####5     | 1135/2500 [00:11<00:13, 99.12it/s, loss=0.0176]
[Trellis Server] Texture baking (opt): optimizing:  45%|####5     | 1136/2500 [00:11<00:13, 99.12it/s, loss=0.0133]
[Trellis Server] Texture baking (opt): optimizing:  45%|####5     | 1137/2500 [00:12<00:13, 99.12it/s, loss=0.0158]
[Trellis Server] Texture baking (opt): optimizing:  46%|####5     | 1138/2500 [00:12<00:13, 99.12it/s, loss=0.0137]
[Trellis Server] Texture baking (opt): optimizing:  46%|####5     | 1139/2500 [00:12<00:13, 99.12it/s, loss=0.0122]
[Trellis Server] Texture baking (opt): optimizing:  46%|####5     | 1140/2500 [00:12<00:13, 99.12it/s, loss=0.0153]
[Trellis Server] Texture baking (opt): optimizing:  46%|####5     | 1141/2500 [00:12<00:13, 99.12it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  46%|####5     | 1142/2500 [00:12<00:13, 99.12it/s, loss=0.0138]
[Trellis Server] Texture baking (opt): optimizing:  46%|####5     | 1143/2500 [00:12<00:13, 99.12it/s, loss=0.0138]
Texture baking (opt): optimizing:  46%|####5     | 1145/2500 [00:12<00:13, 99.38it/s, loss=0.0111]t/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  46%|####5     | 1145/2500 [00:12<00:13, 99.38it/s, loss=0.014]
[Trellis Server] Texture baking (opt): optimizing:  46%|####5     | 1146/2500 [00:12<00:13, 99.38it/s, loss=0.0164]
[Trellis Server] Texture baking (opt): optimizing:  46%|####5     | 1147/2500 [00:12<00:13, 99.38it/s, loss=0.0161]
[Trellis Server] Texture baking (opt): optimizing:  46%|####5     | 1148/2500 [00:12<00:13, 99.38it/s, loss=0.0203]
[Trellis Server] Texture baking (opt): optimizing:  46%|####5     | 1149/2500 [00:12<00:13, 99.38it/s, loss=0.0137]
[Trellis Server] Texture baking (opt): optimizing:  46%|####6     | 1150/2500 [00:12<00:13, 99.38it/s, loss=0.0138]
[Trellis Server] Texture baking (opt): optimizing:  46%|####6     | 1151/2500 [00:12<00:13, 99.38it/s, loss=0.0186]
[Trellis Server] Texture baking (opt): optimizing:  46%|####6     | 1152/2500 [00:12<00:13, 99.38it/s, loss=0.00914]
[Trellis Server] Texture baking (opt): optimizing:  46%|####6     | 1153/2500 [00:12<00:13, 99.38it/s, loss=0.0133]
Texture baking (opt): optimizing:  46%|####6     | 1155/2500 [00:12<00:13, 98.99it/s, loss=0.00894]/s, loss=0.00894]
[Trellis Server] Texture baking (opt): optimizing:  46%|####6     | 1155/2500 [00:12<00:13, 98.99it/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  46%|####6     | 1156/2500 [00:12<00:13, 98.99it/s, loss=0.0129]
[Trellis Server] Texture baking (opt): optimizing:  46%|####6     | 1157/2500 [00:12<00:13, 98.99it/s, loss=0.0103]
[Trellis Server] Texture baking (opt): optimizing:  46%|####6     | 1158/2500 [00:12<00:13, 98.99it/s, loss=0.00968]
[Trellis Server] Texture baking (opt): optimizing:  46%|####6     | 1159/2500 [00:12<00:13, 98.99it/s, loss=0.0128]
[Trellis Server] Texture baking (opt): optimizing:  46%|####6     | 1160/2500 [00:12<00:13, 98.99it/s, loss=0.0222]
[Trellis Server] Texture baking (opt): optimizing:  46%|####6     | 1161/2500 [00:12<00:13, 98.99it/s, loss=0.0108]
[Trellis Server] Texture baking (opt): optimizing:  46%|####6     | 1162/2500 [00:12<00:13, 98.99it/s, loss=0.0229]
[Trellis Server] Texture baking (opt): optimizing:  47%|####6     | 1163/2500 [00:12<00:13, 98.99it/s, loss=0.0129]
Texture baking (opt): optimizing:  47%|####6     | 1165/2500 [00:12<00:13, 98.99it/s, loss=0.0215]t/s, loss=0.0215]
[Trellis Server] Texture baking (opt): optimizing:  47%|####6     | 1165/2500 [00:12<00:13, 98.99it/s, loss=0.0135]
[Trellis Server] Texture baking (opt): optimizing:  47%|####6     | 1166/2500 [00:12<00:13, 98.99it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  47%|####6     | 1167/2500 [00:12<00:13, 98.99it/s, loss=0.0108]
[Trellis Server] Texture baking (opt): optimizing:  47%|####6     | 1168/2500 [00:12<00:13, 98.99it/s, loss=0.0101]
[Trellis Server] Texture baking (opt): optimizing:  47%|####6     | 1169/2500 [00:12<00:13, 98.99it/s, loss=0.0137]
[Trellis Server] Texture baking (opt): optimizing:  47%|####6     | 1170/2500 [00:12<00:13, 98.99it/s, loss=0.0161]
[Trellis Server] Texture baking (opt): optimizing:  47%|####6     | 1171/2500 [00:12<00:13, 98.99it/s, loss=0.0141]
[Trellis Server] Texture baking (opt): optimizing:  47%|####6     | 1172/2500 [00:12<00:13, 98.99it/s, loss=0.0149]
[Trellis Server] Texture baking (opt): optimizing:  47%|####6     | 1173/2500 [00:12<00:13, 98.99it/s, loss=0.0108]
Texture baking (opt): optimizing:  47%|####6     | 1175/2500 [00:12<00:13, 98.99it/s, loss=0.0145]t/s, loss=0.0145]
[Trellis Server] Texture baking (opt): optimizing:  47%|####6     | 1175/2500 [00:12<00:13, 98.99it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  47%|####7     | 1176/2500 [00:12<00:13, 98.99it/s, loss=0.012]
[Trellis Server] Texture baking (opt): optimizing:  47%|####7     | 1177/2500 [00:12<00:13, 98.99it/s, loss=0.00882]
[Trellis Server] Texture baking (opt): optimizing:  47%|####7     | 1178/2500 [00:12<00:13, 98.99it/s, loss=0.00997]
[Trellis Server] Texture baking (opt): optimizing:  47%|####7     | 1179/2500 [00:12<00:13, 98.99it/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  47%|####7     | 1180/2500 [00:12<00:13, 98.99it/s, loss=0.0128]
[Trellis Server] Texture baking (opt): optimizing:  47%|####7     | 1181/2500 [00:12<00:13, 98.99it/s, loss=0.0103]
[Trellis Server] Texture baking (opt): optimizing:  47%|####7     | 1182/2500 [00:12<00:13, 98.99it/s, loss=0.0144]
[Trellis Server] Texture baking (opt): optimizing:  47%|####7     | 1183/2500 [00:12<00:13, 98.99it/s, loss=0.0153]
[Trellis Server] Texture baking (opt): optimizing:  47%|####7     | 1184/2500 [00:12<00:13, 98.99it/s, loss=0.017]
Texture baking (opt): optimizing:  47%|####7     | 1186/2500 [00:12<00:13, 99.31it/s, loss=0.0151]t/s, loss=0.0151]
[Trellis Server] Texture baking (opt): optimizing:  47%|####7     | 1186/2500 [00:12<00:13, 99.31it/s, loss=0.0136]
[Trellis Server] Texture baking (opt): optimizing:  47%|####7     | 1187/2500 [00:12<00:13, 99.31it/s, loss=0.0154]
[Trellis Server] Texture baking (opt): optimizing:  48%|####7     | 1188/2500 [00:12<00:13, 99.31it/s, loss=0.0108]
[Trellis Server] Texture baking (opt): optimizing:  48%|####7     | 1189/2500 [00:12<00:13, 99.31it/s, loss=0.0107]
[Trellis Server] Texture baking (opt): optimizing:  48%|####7     | 1190/2500 [00:12<00:13, 99.31it/s, loss=0.0103]
[Trellis Server] Texture baking (opt): optimizing:  48%|####7     | 1191/2500 [00:12<00:13, 99.31it/s, loss=0.0154]
[Trellis Server] Texture baking (opt): optimizing:  48%|####7     | 1192/2500 [00:12<00:13, 99.31it/s, loss=0.0159]
[Trellis Server] Texture baking (opt): optimizing:  48%|####7     | 1193/2500 [00:12<00:13, 99.31it/s, loss=0.0154]
[Trellis Server] Texture baking (opt): optimizing:  48%|####7     | 1194/2500 [00:12<00:13, 99.31it/s, loss=0.0181]
Texture baking (opt): optimizing:  48%|####7     | 1196/2500 [00:12<00:13, 98.94it/s, loss=0.0157]t/s, loss=0.0157]
[Trellis Server] Texture baking (opt): optimizing:  48%|####7     | 1196/2500 [00:12<00:13, 98.94it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  48%|####7     | 1197/2500 [00:12<00:13, 98.94it/s, loss=0.0166]
[Trellis Server] Texture baking (opt): optimizing:  48%|####7     | 1198/2500 [00:12<00:13, 98.94it/s, loss=0.00752]
[Trellis Server] Texture baking (opt): optimizing:  48%|####7     | 1199/2500 [00:12<00:13, 98.94it/s, loss=0.0156]
[Trellis Server] Texture baking (opt): optimizing:  48%|####8     | 1200/2500 [00:12<00:13, 98.94it/s, loss=0.0121]
[Trellis Server] Texture baking (opt): optimizing:  48%|####8     | 1201/2500 [00:12<00:13, 98.94it/s, loss=0.0122]
[Trellis Server] Texture baking (opt): optimizing:  48%|####8     | 1202/2500 [00:12<00:13, 98.94it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  48%|####8     | 1203/2500 [00:12<00:13, 98.94it/s, loss=0.0179]
[Trellis Server] Texture baking (opt): optimizing:  48%|####8     | 1204/2500 [00:12<00:13, 98.94it/s, loss=0.0139]
Texture baking (opt): optimizing:  48%|####8     | 1206/2500 [00:12<00:13, 98.66it/s, loss=0.0124]t/s, loss=0.0124]
[Trellis Server] Texture baking (opt): optimizing:  48%|####8     | 1206/2500 [00:12<00:13, 98.66it/s, loss=0.0129]
[Trellis Server] Texture baking (opt): optimizing:  48%|####8     | 1207/2500 [00:12<00:13, 98.66it/s, loss=0.0138]
[Trellis Server] Texture baking (opt): optimizing:  48%|####8     | 1208/2500 [00:12<00:13, 98.66it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  48%|####8     | 1209/2500 [00:12<00:13, 98.66it/s, loss=0.00951]
[Trellis Server] Texture baking (opt): optimizing:  48%|####8     | 1210/2500 [00:12<00:13, 98.66it/s, loss=0.00742]
[Trellis Server] Texture baking (opt): optimizing:  48%|####8     | 1211/2500 [00:12<00:13, 98.66it/s, loss=0.0164]
[Trellis Server] Texture baking (opt): optimizing:  48%|####8     | 1212/2500 [00:12<00:13, 98.66it/s, loss=0.0129]
[Trellis Server] Texture baking (opt): optimizing:  49%|####8     | 1213/2500 [00:12<00:13, 98.66it/s, loss=0.00727]
[Trellis Server] Texture baking (opt): optimizing:  49%|####8     | 1214/2500 [00:12<00:13, 98.66it/s, loss=0.0136]
[Trellis Server] Texture baking (opt): optimizing:  49%|####8     | 1215/2500 [00:12<00:13, 98.66it/s, loss=0.0167]
Texture baking (opt): optimizing:  49%|####8     | 1217/2500 [00:12<00:12, 99.56it/s, loss=0.0122]t/s, loss=0.0122]
[Trellis Server] Texture baking (opt): optimizing:  49%|####8     | 1217/2500 [00:12<00:12, 99.56it/s, loss=0.0149]
[Trellis Server] Texture baking (opt): optimizing:  49%|####8     | 1218/2500 [00:12<00:12, 99.56it/s, loss=0.0122]
[Trellis Server] Texture baking (opt): optimizing:  49%|####8     | 1219/2500 [00:12<00:12, 99.56it/s, loss=0.0145]
[Trellis Server] Texture baking (opt): optimizing:  49%|####8     | 1220/2500 [00:12<00:12, 99.56it/s, loss=0.0122]
[Trellis Server] Texture baking (opt): optimizing:  49%|####8     | 1221/2500 [00:12<00:12, 99.56it/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  49%|####8     | 1222/2500 [00:12<00:12, 99.56it/s, loss=0.0189]
[Trellis Server] Texture baking (opt): optimizing:  49%|####8     | 1223/2500 [00:12<00:12, 99.56it/s, loss=0.0159]
[Trellis Server] Texture baking (opt): optimizing:  49%|####8     | 1224/2500 [00:12<00:12, 99.56it/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:  49%|####9     | 1225/2500 [00:12<00:12, 99.56it/s, loss=0.0113]
Texture baking (opt): optimizing:  49%|####9     | 1227/2500 [00:12<00:12, 98.26it/s, loss=0.0146]t/s, loss=0.0146]
[Trellis Server] Texture baking (opt): optimizing:  49%|####9     | 1227/2500 [00:12<00:12, 98.26it/s, loss=0.0157]
[Trellis Server] Texture baking (opt): optimizing:  49%|####9     | 1228/2500 [00:12<00:12, 98.26it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  49%|####9     | 1229/2500 [00:12<00:12, 98.26it/s, loss=0.0101]
[Trellis Server] Texture baking (opt): optimizing:  49%|####9     | 1230/2500 [00:12<00:12, 98.26it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  49%|####9     | 1231/2500 [00:12<00:12, 98.26it/s, loss=0.0188]
[Trellis Server] Texture baking (opt): optimizing:  49%|####9     | 1232/2500 [00:12<00:12, 98.26it/s, loss=0.0166]
[Trellis Server] Texture baking (opt): optimizing:  49%|####9     | 1233/2500 [00:12<00:12, 98.26it/s, loss=0.00847]
[Trellis Server] Texture baking (opt): optimizing:  49%|####9     | 1234/2500 [00:12<00:12, 98.26it/s, loss=0.0136]
[Trellis Server] Texture baking (opt): optimizing:  49%|####9     | 1235/2500 [00:13<00:12, 98.26it/s, loss=0.0209]
Texture baking (opt): optimizing:  49%|####9     | 1237/2500 [00:13<00:13, 96.53it/s, loss=0.00916]/s, loss=0.00916]
[Trellis Server] Texture baking (opt): optimizing:  49%|####9     | 1237/2500 [00:13<00:13, 96.53it/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:  50%|####9     | 1238/2500 [00:13<00:13, 96.53it/s, loss=0.0118]
[Trellis Server] Texture baking (opt): optimizing:  50%|####9     | 1239/2500 [00:13<00:13, 96.53it/s, loss=0.0184]
[Trellis Server] Texture baking (opt): optimizing:  50%|####9     | 1240/2500 [00:13<00:13, 96.53it/s, loss=0.0143]
[Trellis Server] Texture baking (opt): optimizing:  50%|####9     | 1241/2500 [00:13<00:13, 96.53it/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  50%|####9     | 1242/2500 [00:13<00:13, 96.53it/s, loss=0.00948]
[Trellis Server] Texture baking (opt): optimizing:  50%|####9     | 1243/2500 [00:13<00:13, 96.53it/s, loss=0.0162]
[Trellis Server] Texture baking (opt): optimizing:  50%|####9     | 1244/2500 [00:13<00:13, 96.53it/s, loss=0.0164]
[Trellis Server] Texture baking (opt): optimizing:  50%|####9     | 1245/2500 [00:13<00:13, 96.53it/s, loss=0.0133]
Texture baking (opt): optimizing:  50%|####9     | 1247/2500 [00:13<00:13, 96.15it/s, loss=0.016]it/s, loss=0.016]
[Trellis Server] Texture baking (opt): optimizing:  50%|####9     | 1247/2500 [00:13<00:13, 96.15it/s, loss=0.018]
[Trellis Server] Texture baking (opt): optimizing:  50%|####9     | 1248/2500 [00:13<00:13, 96.15it/s, loss=0.0105]
[Trellis Server] Texture baking (opt): optimizing:  50%|####9     | 1249/2500 [00:13<00:13, 96.15it/s, loss=0.0147]
[Trellis Server] Texture baking (opt): optimizing:  50%|#####     | 1250/2500 [00:13<00:13, 96.15it/s, loss=0.0108]
[Trellis Server] Texture baking (opt): optimizing:  50%|#####     | 1251/2500 [00:13<00:12, 96.15it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  50%|#####     | 1252/2500 [00:13<00:12, 96.15it/s, loss=0.0144]
[Trellis Server] Texture baking (opt): optimizing:  50%|#####     | 1253/2500 [00:13<00:12, 96.15it/s, loss=0.00823]
[Trellis Server] Texture baking (opt): optimizing:  50%|#####     | 1254/2500 [00:13<00:12, 96.15it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  50%|#####     | 1255/2500 [00:13<00:12, 96.15it/s, loss=0.0187]
Texture baking (opt): optimizing:  50%|#####     | 1257/2500 [00:13<00:13, 95.47it/s, loss=0.011]it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  50%|#####     | 1257/2500 [00:13<00:13, 95.47it/s, loss=0.0183]
[Trellis Server] Texture baking (opt): optimizing:  50%|#####     | 1258/2500 [00:13<00:13, 95.47it/s, loss=0.0107]
[Trellis Server] Texture baking (opt): optimizing:  50%|#####     | 1259/2500 [00:13<00:12, 95.47it/s, loss=0.0138]
[Trellis Server] Texture baking (opt): optimizing:  50%|#####     | 1260/2500 [00:13<00:12, 95.47it/s, loss=0.0136]
[Trellis Server] Texture baking (opt): optimizing:  50%|#####     | 1261/2500 [00:13<00:12, 95.47it/s, loss=0.018]
[Trellis Server] Texture baking (opt): optimizing:  50%|#####     | 1262/2500 [00:13<00:12, 95.47it/s, loss=0.0147]
[Trellis Server] Texture baking (opt): optimizing:  51%|#####     | 1263/2500 [00:13<00:12, 95.47it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  51%|#####     | 1264/2500 [00:13<00:12, 95.47it/s, loss=0.0164]
[Trellis Server] Texture baking (opt): optimizing:  51%|#####     | 1265/2500 [00:13<00:12, 95.47it/s, loss=0.0111]
Texture baking (opt): optimizing:  51%|#####     | 1267/2500 [00:13<00:12, 96.16it/s, loss=0.0163]t/s, loss=0.0163]
[Trellis Server] Texture baking (opt): optimizing:  51%|#####     | 1267/2500 [00:13<00:12, 96.16it/s, loss=0.0118]
[Trellis Server] Texture baking (opt): optimizing:  51%|#####     | 1268/2500 [00:13<00:12, 96.16it/s, loss=0.0145]
[Trellis Server] Texture baking (opt): optimizing:  51%|#####     | 1269/2500 [00:13<00:12, 96.16it/s, loss=0.0147]
[Trellis Server] Texture baking (opt): optimizing:  51%|#####     | 1270/2500 [00:13<00:12, 96.16it/s, loss=0.00854]
[Trellis Server] Texture baking (opt): optimizing:  51%|#####     | 1271/2500 [00:13<00:12, 96.16it/s, loss=0.0096]
[Trellis Server] Texture baking (opt): optimizing:  51%|#####     | 1272/2500 [00:13<00:12, 96.16it/s, loss=0.014]
[Trellis Server] Texture baking (opt): optimizing:  51%|#####     | 1273/2500 [00:13<00:12, 96.16it/s, loss=0.02]
[Trellis Server] Texture baking (opt): optimizing:  51%|#####     | 1274/2500 [00:13<00:12, 96.16it/s, loss=0.0149]
[Trellis Server] Texture baking (opt): optimizing:  51%|#####1    | 1275/2500 [00:13<00:12, 96.16it/s, loss=0.0109]
Texture baking (opt): optimizing:  51%|#####1    | 1277/2500 [00:13<00:13, 93.75it/s, loss=0.0129]t/s, loss=0.0129]
[Trellis Server] Texture baking (opt): optimizing:  51%|#####1    | 1277/2500 [00:13<00:13, 93.75it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  51%|#####1    | 1278/2500 [00:13<00:13, 93.75it/s, loss=0.00609]
[Trellis Server] Texture baking (opt): optimizing:  51%|#####1    | 1279/2500 [00:13<00:13, 93.75it/s, loss=0.0141]
[Trellis Server] Texture baking (opt): optimizing:  51%|#####1    | 1280/2500 [00:13<00:13, 93.75it/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing:  51%|#####1    | 1281/2500 [00:13<00:13, 93.75it/s, loss=0.0118]
[Trellis Server] Texture baking (opt): optimizing:  51%|#####1    | 1282/2500 [00:13<00:12, 93.75it/s, loss=0.00953]
[Trellis Server] Texture baking (opt): optimizing:  51%|#####1    | 1283/2500 [00:13<00:12, 93.75it/s, loss=0.0236]
[Trellis Server] Texture baking (opt): optimizing:  51%|#####1    | 1284/2500 [00:13<00:12, 93.75it/s, loss=0.0172]
[Trellis Server] Texture baking (opt): optimizing:  51%|#####1    | 1285/2500 [00:13<00:12, 93.75it/s, loss=0.0104]
Texture baking (opt): optimizing:  51%|#####1    | 1287/2500 [00:13<00:12, 94.99it/s, loss=0.00802]/s, loss=0.00802]
[Trellis Server] Texture baking (opt): optimizing:  51%|#####1    | 1287/2500 [00:13<00:12, 94.99it/s, loss=0.0162]
[Trellis Server] Texture baking (opt): optimizing:  52%|#####1    | 1288/2500 [00:13<00:12, 94.99it/s, loss=0.00956]
[Trellis Server] Texture baking (opt): optimizing:  52%|#####1    | 1289/2500 [00:13<00:12, 94.99it/s, loss=0.00693]
[Trellis Server] Texture baking (opt): optimizing:  52%|#####1    | 1290/2500 [00:13<00:12, 94.99it/s, loss=0.0101]
[Trellis Server] Texture baking (opt): optimizing:  52%|#####1    | 1291/2500 [00:13<00:12, 94.99it/s, loss=0.0156]
[Trellis Server] Texture baking (opt): optimizing:  52%|#####1    | 1292/2500 [00:13<00:12, 94.99it/s, loss=0.0123]
[Trellis Server] Texture baking (opt): optimizing:  52%|#####1    | 1293/2500 [00:13<00:12, 94.99it/s, loss=0.0101]
[Trellis Server] Texture baking (opt): optimizing:  52%|#####1    | 1294/2500 [00:13<00:12, 94.99it/s, loss=0.00983]
[Trellis Server] Texture baking (opt): optimizing:  52%|#####1    | 1295/2500 [00:13<00:12, 94.99it/s, loss=0.0128]
Texture baking (opt): optimizing:  52%|#####1    | 1297/2500 [00:13<00:12, 95.00it/s, loss=0.0112]t/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  52%|#####1    | 1297/2500 [00:13<00:12, 95.00it/s, loss=0.0165]
[Trellis Server] Texture baking (opt): optimizing:  52%|#####1    | 1298/2500 [00:13<00:12, 95.00it/s, loss=0.00736]
[Trellis Server] Texture baking (opt): optimizing:  52%|#####1    | 1299/2500 [00:13<00:12, 95.00it/s, loss=0.0135]
[Trellis Server] Texture baking (opt): optimizing:  52%|#####2    | 1300/2500 [00:13<00:12, 95.00it/s, loss=0.0152]
[Trellis Server] Texture baking (opt): optimizing:  52%|#####2    | 1301/2500 [00:13<00:12, 95.00it/s, loss=0.0147]
[Trellis Server] Texture baking (opt): optimizing:  52%|#####2    | 1302/2500 [00:13<00:12, 95.00it/s, loss=0.00867]
[Trellis Server] Texture baking (opt): optimizing:  52%|#####2    | 1303/2500 [00:13<00:12, 95.00it/s, loss=0.0128]
[Trellis Server] Texture baking (opt): optimizing:  52%|#####2    | 1304/2500 [00:13<00:12, 95.00it/s, loss=0.00691]
[Trellis Server] Texture baking (opt): optimizing:  52%|#####2    | 1305/2500 [00:13<00:12, 95.00it/s, loss=0.0137]
Texture baking (opt): optimizing:  52%|#####2    | 1307/2500 [00:13<00:12, 96.44it/s, loss=0.0145]t/s, loss=0.0145]
[Trellis Server] Texture baking (opt): optimizing:  52%|#####2    | 1307/2500 [00:13<00:12, 96.44it/s, loss=0.015]
[Trellis Server] Texture baking (opt): optimizing:  52%|#####2    | 1308/2500 [00:13<00:12, 96.44it/s, loss=0.0273]
[Trellis Server] Texture baking (opt): optimizing:  52%|#####2    | 1309/2500 [00:13<00:12, 96.44it/s, loss=0.0161]
[Trellis Server] Texture baking (opt): optimizing:  52%|#####2    | 1310/2500 [00:13<00:12, 96.44it/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  52%|#####2    | 1311/2500 [00:13<00:12, 96.44it/s, loss=0.0143]
[Trellis Server] Texture baking (opt): optimizing:  52%|#####2    | 1312/2500 [00:13<00:12, 96.44it/s, loss=0.0138]
[Trellis Server] Texture baking (opt): optimizing:  53%|#####2    | 1313/2500 [00:13<00:12, 96.44it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  53%|#####2    | 1314/2500 [00:13<00:12, 96.44it/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:  53%|#####2    | 1315/2500 [00:13<00:12, 96.44it/s, loss=0.0166]
Texture baking (opt): optimizing:  53%|#####2    | 1317/2500 [00:13<00:12, 97.43it/s, loss=0.0147]t/s, loss=0.0147]
[Trellis Server] Texture baking (opt): optimizing:  53%|#####2    | 1317/2500 [00:13<00:12, 97.43it/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing:  53%|#####2    | 1318/2500 [00:13<00:12, 97.43it/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  53%|#####2    | 1319/2500 [00:13<00:12, 97.43it/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  53%|#####2    | 1320/2500 [00:13<00:12, 97.43it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  53%|#####2    | 1321/2500 [00:13<00:12, 97.43it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  53%|#####2    | 1322/2500 [00:13<00:12, 97.43it/s, loss=0.00911]
[Trellis Server] Texture baking (opt): optimizing:  53%|#####2    | 1323/2500 [00:13<00:12, 97.43it/s, loss=0.00993]
[Trellis Server] Texture baking (opt): optimizing:  53%|#####2    | 1324/2500 [00:13<00:12, 97.43it/s, loss=0.0118]
[Trellis Server] Texture baking (opt): optimizing:  53%|#####3    | 1325/2500 [00:13<00:12, 97.43it/s, loss=0.02]
[Trellis Server] Texture baking (opt): optimizing:  53%|#####3    | 1326/2500 [00:13<00:12, 97.43it/s, loss=0.00899]
Texture baking (opt): optimizing:  53%|#####3    | 1328/2500 [00:13<00:11, 98.24it/s, loss=0.0139]t/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:  53%|#####3    | 1328/2500 [00:13<00:11, 98.24it/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  53%|#####3    | 1329/2500 [00:13<00:11, 98.24it/s, loss=0.0138]
[Trellis Server] Texture baking (opt): optimizing:  53%|#####3    | 1330/2500 [00:13<00:11, 98.24it/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  53%|#####3    | 1331/2500 [00:13<00:11, 98.24it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  53%|#####3    | 1332/2500 [00:14<00:11, 98.24it/s, loss=0.017]
[Trellis Server] Texture baking (opt): optimizing:  53%|#####3    | 1333/2500 [00:14<00:11, 98.24it/s, loss=0.0152]
[Trellis Server] Texture baking (opt): optimizing:  53%|#####3    | 1334/2500 [00:14<00:11, 98.24it/s, loss=0.0141]
[Trellis Server] Texture baking (opt): optimizing:  53%|#####3    | 1335/2500 [00:14<00:11, 98.24it/s, loss=0.0137]
[Trellis Server] Texture baking (opt): optimizing:  53%|#####3    | 1336/2500 [00:14<00:11, 98.24it/s, loss=0.0149]
[Trellis Server] Texture baking (opt): optimizing:  53%|#####3    | 1337/2500 [00:14<00:11, 98.24it/s, loss=0.0161]
Texture baking (opt): optimizing:  54%|#####3    | 1339/2500 [00:14<00:11, 99.07it/s, loss=0.00951]/s, loss=0.00951]
[Trellis Server] Texture baking (opt): optimizing:  54%|#####3    | 1339/2500 [00:14<00:11, 99.07it/s, loss=0.0207]
[Trellis Server] Texture baking (opt): optimizing:  54%|#####3    | 1340/2500 [00:14<00:11, 99.07it/s, loss=0.0116]
[Trellis Server] Texture baking (opt): optimizing:  54%|#####3    | 1341/2500 [00:14<00:11, 99.07it/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  54%|#####3    | 1342/2500 [00:14<00:11, 99.07it/s, loss=0.0125]
[Trellis Server] Texture baking (opt): optimizing:  54%|#####3    | 1343/2500 [00:14<00:11, 99.07it/s, loss=0.0158]
[Trellis Server] Texture baking (opt): optimizing:  54%|#####3    | 1344/2500 [00:14<00:11, 99.07it/s, loss=0.0173]
[Trellis Server] Texture baking (opt): optimizing:  54%|#####3    | 1345/2500 [00:14<00:11, 99.07it/s, loss=0.00938]
[Trellis Server] Texture baking (opt): optimizing:  54%|#####3    | 1346/2500 [00:14<00:11, 99.07it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  54%|#####3    | 1347/2500 [00:14<00:11, 99.07it/s, loss=0.0161]
Texture baking (opt): optimizing:  54%|#####3    | 1349/2500 [00:14<00:11, 99.18it/s, loss=0.0108]t/s, loss=0.0108]
[Trellis Server] Texture baking (opt): optimizing:  54%|#####3    | 1349/2500 [00:14<00:11, 99.18it/s, loss=0.0162]
[Trellis Server] Texture baking (opt): optimizing:  54%|#####4    | 1350/2500 [00:14<00:11, 99.18it/s, loss=0.0158]
[Trellis Server] Texture baking (opt): optimizing:  54%|#####4    | 1351/2500 [00:14<00:11, 99.18it/s, loss=0.0162]
[Trellis Server] Texture baking (opt): optimizing:  54%|#####4    | 1352/2500 [00:14<00:11, 99.18it/s, loss=0.0282]
[Trellis Server] Texture baking (opt): optimizing:  54%|#####4    | 1353/2500 [00:14<00:11, 99.18it/s, loss=0.0149]
[Trellis Server] Texture baking (opt): optimizing:  54%|#####4    | 1354/2500 [00:14<00:11, 99.18it/s, loss=0.0158]
[Trellis Server] Texture baking (opt): optimizing:  54%|#####4    | 1355/2500 [00:14<00:11, 99.18it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  54%|#####4    | 1356/2500 [00:14<00:11, 99.18it/s, loss=0.00787]
[Trellis Server] Texture baking (opt): optimizing:  54%|#####4    | 1357/2500 [00:14<00:11, 99.18it/s, loss=0.0107]
Texture baking (opt): optimizing:  54%|#####4    | 1359/2500 [00:14<00:11, 99.13it/s, loss=0.0157]t/s, loss=0.0157]
[Trellis Server] Texture baking (opt): optimizing:  54%|#####4    | 1359/2500 [00:14<00:11, 99.13it/s, loss=0.0118]
[Trellis Server] Texture baking (opt): optimizing:  54%|#####4    | 1360/2500 [00:14<00:11, 99.13it/s, loss=0.0157]
[Trellis Server] Texture baking (opt): optimizing:  54%|#####4    | 1361/2500 [00:14<00:11, 99.13it/s, loss=0.0231]
[Trellis Server] Texture baking (opt): optimizing:  54%|#####4    | 1362/2500 [00:14<00:11, 99.13it/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  55%|#####4    | 1363/2500 [00:14<00:11, 99.13it/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:  55%|#####4    | 1364/2500 [00:14<00:11, 99.13it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  55%|#####4    | 1365/2500 [00:14<00:11, 99.13it/s, loss=0.0151]
[Trellis Server] Texture baking (opt): optimizing:  55%|#####4    | 1366/2500 [00:14<00:11, 99.13it/s, loss=0.0141]
[Trellis Server] Texture baking (opt): optimizing:  55%|#####4    | 1367/2500 [00:14<00:11, 99.13it/s, loss=0.00736]
Texture baking (opt): optimizing:  55%|#####4    | 1369/2500 [00:14<00:11, 98.52it/s, loss=0.0133]t/s, loss=0.0133]
[Trellis Server] Texture baking (opt): optimizing:  55%|#####4    | 1369/2500 [00:14<00:11, 98.52it/s, loss=0.0167]
[Trellis Server] Texture baking (opt): optimizing:  55%|#####4    | 1370/2500 [00:14<00:11, 98.52it/s, loss=0.0131]
[Trellis Server] Texture baking (opt): optimizing:  55%|#####4    | 1371/2500 [00:14<00:11, 98.52it/s, loss=0.0103]
[Trellis Server] Texture baking (opt): optimizing:  55%|#####4    | 1372/2500 [00:14<00:11, 98.52it/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:  55%|#####4    | 1373/2500 [00:14<00:11, 98.52it/s, loss=0.0154]
[Trellis Server] Texture baking (opt): optimizing:  55%|#####4    | 1374/2500 [00:14<00:11, 98.52it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  55%|#####5    | 1375/2500 [00:14<00:11, 98.52it/s, loss=0.00887]
[Trellis Server] Texture baking (opt): optimizing:  55%|#####5    | 1376/2500 [00:14<00:11, 98.52it/s, loss=0.0183]
[Trellis Server] Texture baking (opt): optimizing:  55%|#####5    | 1377/2500 [00:14<00:11, 98.52it/s, loss=0.014]
Texture baking (opt): optimizing:  55%|#####5    | 1379/2500 [00:14<00:11, 96.91it/s, loss=0.0133]t/s, loss=0.0133]
[Trellis Server] Texture baking (opt): optimizing:  55%|#####5    | 1379/2500 [00:14<00:11, 96.91it/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  55%|#####5    | 1380/2500 [00:14<00:11, 96.91it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  55%|#####5    | 1381/2500 [00:14<00:11, 96.91it/s, loss=0.00751]
[Trellis Server] Texture baking (opt): optimizing:  55%|#####5    | 1382/2500 [00:14<00:11, 96.91it/s, loss=0.0153]
[Trellis Server] Texture baking (opt): optimizing:  55%|#####5    | 1383/2500 [00:14<00:11, 96.91it/s, loss=0.0129]
[Trellis Server] Texture baking (opt): optimizing:  55%|#####5    | 1384/2500 [00:14<00:11, 96.91it/s, loss=0.0129]
[Trellis Server] Texture baking (opt): optimizing:  55%|#####5    | 1385/2500 [00:14<00:11, 96.91it/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:  55%|#####5    | 1386/2500 [00:14<00:11, 96.91it/s, loss=0.0131]
[Trellis Server] Texture baking (opt): optimizing:  55%|#####5    | 1387/2500 [00:14<00:11, 96.91it/s, loss=0.0188]
Texture baking (opt): optimizing:  56%|#####5    | 1389/2500 [00:14<00:11, 95.58it/s, loss=0.00773]/s, loss=0.00773]
[Trellis Server] Texture baking (opt): optimizing:  56%|#####5    | 1389/2500 [00:14<00:11, 95.58it/s, loss=0.0204]
[Trellis Server] Texture baking (opt): optimizing:  56%|#####5    | 1390/2500 [00:14<00:11, 95.58it/s, loss=0.00945]
[Trellis Server] Texture baking (opt): optimizing:  56%|#####5    | 1391/2500 [00:14<00:11, 95.58it/s, loss=0.00846]
[Trellis Server] Texture baking (opt): optimizing:  56%|#####5    | 1392/2500 [00:14<00:11, 95.58it/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  56%|#####5    | 1393/2500 [00:14<00:11, 95.58it/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing:  56%|#####5    | 1394/2500 [00:14<00:11, 95.58it/s, loss=0.0101]
[Trellis Server] Texture baking (opt): optimizing:  56%|#####5    | 1395/2500 [00:14<00:11, 95.58it/s, loss=0.0158]
[Trellis Server] Texture baking (opt): optimizing:  56%|#####5    | 1396/2500 [00:14<00:11, 95.58it/s, loss=0.0131]
[Trellis Server] Texture baking (opt): optimizing:  56%|#####5    | 1397/2500 [00:14<00:11, 95.58it/s, loss=0.0109]
Texture baking (opt): optimizing:  56%|#####5    | 1399/2500 [00:14<00:11, 94.90it/s, loss=0.0197]t/s, loss=0.0197]
[Trellis Server] Texture baking (opt): optimizing:  56%|#####5    | 1399/2500 [00:14<00:11, 94.90it/s, loss=0.0125]
[Trellis Server] Texture baking (opt): optimizing:  56%|#####6    | 1400/2500 [00:14<00:11, 94.90it/s, loss=0.0133]
[Trellis Server] Texture baking (opt): optimizing:  56%|#####6    | 1401/2500 [00:14<00:11, 94.90it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  56%|#####6    | 1402/2500 [00:14<00:11, 94.90it/s, loss=0.0154]
[Trellis Server] Texture baking (opt): optimizing:  56%|#####6    | 1403/2500 [00:14<00:11, 94.90it/s, loss=0.0131]
[Trellis Server] Texture baking (opt): optimizing:  56%|#####6    | 1404/2500 [00:14<00:11, 94.90it/s, loss=0.0214]
[Trellis Server] Texture baking (opt): optimizing:  56%|#####6    | 1405/2500 [00:14<00:11, 94.90it/s, loss=0.00787]
[Trellis Server] Texture baking (opt): optimizing:  56%|#####6    | 1406/2500 [00:14<00:11, 94.90it/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  56%|#####6    | 1407/2500 [00:14<00:11, 94.90it/s, loss=0.0103]
Texture baking (opt): optimizing:  56%|#####6    | 1409/2500 [00:14<00:11, 95.01it/s, loss=0.0111]t/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  56%|#####6    | 1409/2500 [00:14<00:11, 95.01it/s, loss=0.0102]
[Trellis Server] Texture baking (opt): optimizing:  56%|#####6    | 1410/2500 [00:14<00:11, 95.01it/s, loss=0.0204]
[Trellis Server] Texture baking (opt): optimizing:  56%|#####6    | 1411/2500 [00:14<00:11, 95.01it/s, loss=0.0138]
[Trellis Server] Texture baking (opt): optimizing:  56%|#####6    | 1412/2500 [00:14<00:11, 95.01it/s, loss=0.0118]
[Trellis Server] Texture baking (opt): optimizing:  57%|#####6    | 1413/2500 [00:14<00:11, 95.01it/s, loss=0.0151]
[Trellis Server] Texture baking (opt): optimizing:  57%|#####6    | 1414/2500 [00:14<00:11, 95.01it/s, loss=0.00724]
[Trellis Server] Texture baking (opt): optimizing:  57%|#####6    | 1415/2500 [00:14<00:11, 95.01it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  57%|#####6    | 1416/2500 [00:14<00:11, 95.01it/s, loss=0.00745]
[Trellis Server] Texture baking (opt): optimizing:  57%|#####6    | 1417/2500 [00:14<00:11, 95.01it/s, loss=0.0185]
[Trellis Server] Texture baking (opt): optimizing:  57%|#####6    | 1418/2500 [00:14<00:11, 95.01it/s, loss=0.0158]
Texture baking (opt): optimizing:  57%|#####6    | 1420/2500 [00:14<00:11, 96.43it/s, loss=0.0123]t/s, loss=0.0123]
[Trellis Server] Texture baking (opt): optimizing:  57%|#####6    | 1420/2500 [00:14<00:11, 96.43it/s, loss=0.0158]
[Trellis Server] Texture baking (opt): optimizing:  57%|#####6    | 1421/2500 [00:14<00:11, 96.43it/s, loss=0.0132]
[Trellis Server] Texture baking (opt): optimizing:  57%|#####6    | 1422/2500 [00:14<00:11, 96.43it/s, loss=0.0107]
[Trellis Server] Texture baking (opt): optimizing:  57%|#####6    | 1423/2500 [00:14<00:11, 96.43it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  57%|#####6    | 1424/2500 [00:14<00:11, 96.43it/s, loss=0.017]
[Trellis Server] Texture baking (opt): optimizing:  57%|#####6    | 1425/2500 [00:14<00:11, 96.43it/s, loss=0.00957]
[Trellis Server] Texture baking (opt): optimizing:  57%|#####7    | 1426/2500 [00:14<00:11, 96.43it/s, loss=0.00974]
[Trellis Server] Texture baking (opt): optimizing:  57%|#####7    | 1427/2500 [00:14<00:11, 96.43it/s, loss=0.0131]
[Trellis Server] Texture baking (opt): optimizing:  57%|#####7    | 1428/2500 [00:14<00:11, 96.43it/s, loss=0.0107]
Texture baking (opt): optimizing:  57%|#####7    | 1430/2500 [00:15<00:10, 97.45it/s, loss=0.0134]t/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  57%|#####7    | 1430/2500 [00:15<00:10, 97.45it/s, loss=0.00937]
[Trellis Server] Texture baking (opt): optimizing:  57%|#####7    | 1431/2500 [00:15<00:10, 97.45it/s, loss=0.00719]
[Trellis Server] Texture baking (opt): optimizing:  57%|#####7    | 1432/2500 [00:15<00:10, 97.45it/s, loss=0.0088]
[Trellis Server] Texture baking (opt): optimizing:  57%|#####7    | 1433/2500 [00:15<00:10, 97.45it/s, loss=0.0187]
[Trellis Server] Texture baking (opt): optimizing:  57%|#####7    | 1434/2500 [00:15<00:10, 97.45it/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  57%|#####7    | 1435/2500 [00:15<00:10, 97.45it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  57%|#####7    | 1436/2500 [00:15<00:10, 97.45it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  57%|#####7    | 1437/2500 [00:15<00:10, 97.45it/s, loss=0.016]
[Trellis Server] Texture baking (opt): optimizing:  58%|#####7    | 1438/2500 [00:15<00:10, 97.45it/s, loss=0.0142]
Texture baking (opt): optimizing:  58%|#####7    | 1440/2500 [00:15<00:10, 96.79it/s, loss=0.0096]t/s, loss=0.0096]
[Trellis Server] Texture baking (opt): optimizing:  58%|#####7    | 1440/2500 [00:15<00:10, 96.79it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  58%|#####7    | 1441/2500 [00:15<00:10, 96.79it/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  58%|#####7    | 1442/2500 [00:15<00:10, 96.79it/s, loss=0.00724]
[Trellis Server] Texture baking (opt): optimizing:  58%|#####7    | 1443/2500 [00:15<00:10, 96.79it/s, loss=0.008]
[Trellis Server] Texture baking (opt): optimizing:  58%|#####7    | 1444/2500 [00:15<00:10, 96.79it/s, loss=0.0129]
[Trellis Server] Texture baking (opt): optimizing:  58%|#####7    | 1445/2500 [00:15<00:10, 96.79it/s, loss=0.0195]
[Trellis Server] Texture baking (opt): optimizing:  58%|#####7    | 1446/2500 [00:15<00:10, 96.79it/s, loss=0.00784]
[Trellis Server] Texture baking (opt): optimizing:  58%|#####7    | 1447/2500 [00:15<00:10, 96.79it/s, loss=0.0122]
[Trellis Server] Texture baking (opt): optimizing:  58%|#####7    | 1448/2500 [00:15<00:10, 96.79it/s, loss=0.0113]
Texture baking (opt): optimizing:  58%|#####8    | 1450/2500 [00:15<00:10, 97.10it/s, loss=0.0153]t/s, loss=0.0153]
[Trellis Server] Texture baking (opt): optimizing:  58%|#####8    | 1450/2500 [00:15<00:10, 97.10it/s, loss=0.012]
[Trellis Server] Texture baking (opt): optimizing:  58%|#####8    | 1451/2500 [00:15<00:10, 97.10it/s, loss=0.00761]
[Trellis Server] Texture baking (opt): optimizing:  58%|#####8    | 1452/2500 [00:15<00:10, 97.10it/s, loss=0.0128]
[Trellis Server] Texture baking (opt): optimizing:  58%|#####8    | 1453/2500 [00:15<00:10, 97.10it/s, loss=0.0136]
[Trellis Server] Texture baking (opt): optimizing:  58%|#####8    | 1454/2500 [00:15<00:10, 97.10it/s, loss=0.0105]
[Trellis Server] Texture baking (opt): optimizing:  58%|#####8    | 1455/2500 [00:15<00:10, 97.10it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  58%|#####8    | 1456/2500 [00:15<00:10, 97.10it/s, loss=0.0123]
[Trellis Server] Texture baking (opt): optimizing:  58%|#####8    | 1457/2500 [00:15<00:10, 97.10it/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  58%|#####8    | 1458/2500 [00:15<00:10, 97.10it/s, loss=0.00932]
Texture baking (opt): optimizing:  58%|#####8    | 1460/2500 [00:15<00:10, 97.38it/s, loss=0.0126]t/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:  58%|#####8    | 1460/2500 [00:15<00:10, 97.38it/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  58%|#####8    | 1461/2500 [00:15<00:10, 97.38it/s, loss=0.017]
[Trellis Server] Texture baking (opt): optimizing:  58%|#####8    | 1462/2500 [00:15<00:10, 97.38it/s, loss=0.0155]
[Trellis Server] Texture baking (opt): optimizing:  59%|#####8    | 1463/2500 [00:15<00:10, 97.38it/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:  59%|#####8    | 1464/2500 [00:15<00:10, 97.38it/s, loss=0.0132]
[Trellis Server] Texture baking (opt): optimizing:  59%|#####8    | 1465/2500 [00:15<00:10, 97.38it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  59%|#####8    | 1466/2500 [00:15<00:10, 97.38it/s, loss=0.0122]
[Trellis Server] Texture baking (opt): optimizing:  59%|#####8    | 1467/2500 [00:15<00:10, 97.38it/s, loss=0.0217]
[Trellis Server] Texture baking (opt): optimizing:  59%|#####8    | 1468/2500 [00:15<00:10, 97.38it/s, loss=0.012]
Texture baking (opt): optimizing:  59%|#####8    | 1470/2500 [00:15<00:10, 97.58it/s, loss=0.00739]/s, loss=0.00739]
[Trellis Server] Texture baking (opt): optimizing:  59%|#####8    | 1470/2500 [00:15<00:10, 97.58it/s, loss=0.0149]
[Trellis Server] Texture baking (opt): optimizing:  59%|#####8    | 1471/2500 [00:15<00:10, 97.58it/s, loss=0.016]
[Trellis Server] Texture baking (opt): optimizing:  59%|#####8    | 1472/2500 [00:15<00:10, 97.58it/s, loss=0.00765]
[Trellis Server] Texture baking (opt): optimizing:  59%|#####8    | 1473/2500 [00:15<00:10, 97.58it/s, loss=0.0132]
[Trellis Server] Texture baking (opt): optimizing:  59%|#####8    | 1474/2500 [00:15<00:10, 97.58it/s, loss=0.0176]
[Trellis Server] Texture baking (opt): optimizing:  59%|#####8    | 1475/2500 [00:15<00:10, 97.58it/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  59%|#####9    | 1476/2500 [00:15<00:10, 97.58it/s, loss=0.0141]
[Trellis Server] Texture baking (opt): optimizing:  59%|#####9    | 1477/2500 [00:15<00:10, 97.58it/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  59%|#####9    | 1478/2500 [00:15<00:10, 97.58it/s, loss=0.0132]
Texture baking (opt): optimizing:  59%|#####9    | 1480/2500 [00:15<00:10, 96.31it/s, loss=0.0161]t/s, loss=0.0161]
[Trellis Server] Texture baking (opt): optimizing:  59%|#####9    | 1480/2500 [00:15<00:10, 96.31it/s, loss=0.0124]
[Trellis Server] Texture baking (opt): optimizing:  59%|#####9    | 1481/2500 [00:15<00:10, 96.31it/s, loss=0.00707]
[Trellis Server] Texture baking (opt): optimizing:  59%|#####9    | 1482/2500 [00:15<00:10, 96.31it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  59%|#####9    | 1483/2500 [00:15<00:10, 96.31it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  59%|#####9    | 1484/2500 [00:15<00:10, 96.31it/s, loss=0.0213]
[Trellis Server] Texture baking (opt): optimizing:  59%|#####9    | 1485/2500 [00:15<00:10, 96.31it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  59%|#####9    | 1486/2500 [00:15<00:10, 96.31it/s, loss=0.0153]
[Trellis Server] Texture baking (opt): optimizing:  59%|#####9    | 1487/2500 [00:15<00:10, 96.31it/s, loss=0.00905]
[Trellis Server] Texture baking (opt): optimizing:  60%|#####9    | 1488/2500 [00:15<00:10, 96.31it/s, loss=0.0139]
Texture baking (opt): optimizing:  60%|#####9    | 1490/2500 [00:15<00:10, 92.38it/s, loss=0.0115]t/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  60%|#####9    | 1490/2500 [00:15<00:10, 92.38it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  60%|#####9    | 1491/2500 [00:15<00:10, 92.38it/s, loss=0.0136]
[Trellis Server] Texture baking (opt): optimizing:  60%|#####9    | 1492/2500 [00:15<00:10, 92.38it/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  60%|#####9    | 1493/2500 [00:15<00:10, 92.38it/s, loss=0.0121]
[Trellis Server] Texture baking (opt): optimizing:  60%|#####9    | 1494/2500 [00:15<00:10, 92.38it/s, loss=0.0159]
[Trellis Server] Texture baking (opt): optimizing:  60%|#####9    | 1495/2500 [00:15<00:10, 92.38it/s, loss=0.0107]
[Trellis Server] Texture baking (opt): optimizing:  60%|#####9    | 1496/2500 [00:15<00:10, 92.38it/s, loss=0.0202]
[Trellis Server] Texture baking (opt): optimizing:  60%|#####9    | 1497/2500 [00:15<00:10, 92.38it/s, loss=0.007]
[Trellis Server] Texture baking (opt): optimizing:  60%|#####9    | 1498/2500 [00:15<00:10, 92.38it/s, loss=0.00669]
Texture baking (opt): optimizing:  60%|######    | 1500/2500 [00:15<00:10, 92.94it/s, loss=0.0138]t/s, loss=0.0138]
[Trellis Server] Texture baking (opt): optimizing:  60%|######    | 1500/2500 [00:15<00:10, 92.94it/s, loss=0.00772]
[Trellis Server] Texture baking (opt): optimizing:  60%|######    | 1501/2500 [00:15<00:10, 92.94it/s, loss=0.0123]
[Trellis Server] Texture baking (opt): optimizing:  60%|######    | 1502/2500 [00:15<00:10, 92.94it/s, loss=0.0155]
[Trellis Server] Texture baking (opt): optimizing:  60%|######    | 1503/2500 [00:15<00:10, 92.94it/s, loss=0.00802]
[Trellis Server] Texture baking (opt): optimizing:  60%|######    | 1504/2500 [00:15<00:10, 92.94it/s, loss=0.0205]
[Trellis Server] Texture baking (opt): optimizing:  60%|######    | 1505/2500 [00:15<00:10, 92.94it/s, loss=0.0101]
[Trellis Server] Texture baking (opt): optimizing:  60%|######    | 1506/2500 [00:15<00:10, 92.94it/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  60%|######    | 1507/2500 [00:15<00:10, 92.94it/s, loss=0.0132]
[Trellis Server] Texture baking (opt): optimizing:  60%|######    | 1508/2500 [00:15<00:10, 92.94it/s, loss=0.0143]
Texture baking (opt): optimizing:  60%|######    | 1510/2500 [00:15<00:10, 94.31it/s, loss=0.00995]/s, loss=0.00995]
[Trellis Server] Texture baking (opt): optimizing:  60%|######    | 1510/2500 [00:15<00:10, 94.31it/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  60%|######    | 1511/2500 [00:15<00:10, 94.31it/s, loss=0.00867]
[Trellis Server] Texture baking (opt): optimizing:  60%|######    | 1512/2500 [00:15<00:10, 94.31it/s, loss=0.0186]
[Trellis Server] Texture baking (opt): optimizing:  61%|######    | 1513/2500 [00:15<00:10, 94.31it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  61%|######    | 1514/2500 [00:15<00:10, 94.31it/s, loss=0.00832]
[Trellis Server] Texture baking (opt): optimizing:  61%|######    | 1515/2500 [00:15<00:10, 94.31it/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  61%|######    | 1516/2500 [00:15<00:10, 94.31it/s, loss=0.0143]
[Trellis Server] Texture baking (opt): optimizing:  61%|######    | 1517/2500 [00:15<00:10, 94.31it/s, loss=0.0213]
[Trellis Server] Texture baking (opt): optimizing:  61%|######    | 1518/2500 [00:15<00:10, 94.31it/s, loss=0.0131]
Texture baking (opt): optimizing:  61%|######    | 1520/2500 [00:15<00:10, 94.86it/s, loss=0.00954]/s, loss=0.00954]
[Trellis Server] Texture baking (opt): optimizing:  61%|######    | 1520/2500 [00:15<00:10, 94.86it/s, loss=0.00955]
[Trellis Server] Texture baking (opt): optimizing:  61%|######    | 1521/2500 [00:15<00:10, 94.86it/s, loss=0.0207]
[Trellis Server] Texture baking (opt): optimizing:  61%|######    | 1522/2500 [00:15<00:10, 94.86it/s, loss=0.016]
[Trellis Server] Texture baking (opt): optimizing:  61%|######    | 1523/2500 [00:16<00:10, 94.86it/s, loss=0.0186]
[Trellis Server] Texture baking (opt): optimizing:  61%|######    | 1524/2500 [00:16<00:10, 94.86it/s, loss=0.0209]
[Trellis Server] Texture baking (opt): optimizing:  61%|######1   | 1525/2500 [00:16<00:10, 94.86it/s, loss=0.00653]
[Trellis Server] Texture baking (opt): optimizing:  61%|######1   | 1526/2500 [00:16<00:10, 94.86it/s, loss=0.00917]
[Trellis Server] Texture baking (opt): optimizing:  61%|######1   | 1527/2500 [00:16<00:10, 94.86it/s, loss=0.0135]
[Trellis Server] Texture baking (opt): optimizing:  61%|######1   | 1528/2500 [00:16<00:10, 94.86it/s, loss=0.0101]
Texture baking (opt): optimizing:  61%|######1   | 1530/2500 [00:16<00:10, 96.32it/s, loss=0.0139]t/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:  61%|######1   | 1530/2500 [00:16<00:10, 96.32it/s, loss=0.00919]
[Trellis Server] Texture baking (opt): optimizing:  61%|######1   | 1531/2500 [00:16<00:10, 96.32it/s, loss=0.0162]
[Trellis Server] Texture baking (opt): optimizing:  61%|######1   | 1532/2500 [00:16<00:10, 96.32it/s, loss=0.0153]
[Trellis Server] Texture baking (opt): optimizing:  61%|######1   | 1533/2500 [00:16<00:10, 96.32it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  61%|######1   | 1534/2500 [00:16<00:10, 96.32it/s, loss=0.0128]
[Trellis Server] Texture baking (opt): optimizing:  61%|######1   | 1535/2500 [00:16<00:10, 96.32it/s, loss=0.0104]
[Trellis Server] Texture baking (opt): optimizing:  61%|######1   | 1536/2500 [00:16<00:10, 96.32it/s, loss=0.0129]
[Trellis Server] Texture baking (opt): optimizing:  61%|######1   | 1537/2500 [00:16<00:09, 96.32it/s, loss=0.00905]
[Trellis Server] Texture baking (opt): optimizing:  62%|######1   | 1538/2500 [00:16<00:09, 96.32it/s, loss=0.0146]
Texture baking (opt): optimizing:  62%|######1   | 1540/2500 [00:16<00:09, 97.33it/s, loss=0.0116]t/s, loss=0.0116]
[Trellis Server] Texture baking (opt): optimizing:  62%|######1   | 1540/2500 [00:16<00:09, 97.33it/s, loss=0.00746]
[Trellis Server] Texture baking (opt): optimizing:  62%|######1   | 1541/2500 [00:16<00:09, 97.33it/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  62%|######1   | 1542/2500 [00:16<00:09, 97.33it/s, loss=0.00756]
[Trellis Server] Texture baking (opt): optimizing:  62%|######1   | 1543/2500 [00:16<00:09, 97.33it/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:  62%|######1   | 1544/2500 [00:16<00:09, 97.33it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  62%|######1   | 1545/2500 [00:16<00:09, 97.33it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  62%|######1   | 1546/2500 [00:16<00:09, 97.33it/s, loss=0.0215]
[Trellis Server] Texture baking (opt): optimizing:  62%|######1   | 1547/2500 [00:16<00:09, 97.33it/s, loss=0.0165]
[Trellis Server] Texture baking (opt): optimizing:  62%|######1   | 1548/2500 [00:16<00:09, 97.33it/s, loss=0.0144]
Texture baking (opt): optimizing:  62%|######2   | 1550/2500 [00:16<00:09, 95.58it/s, loss=0.0171]t/s, loss=0.0171]
[Trellis Server] Texture baking (opt): optimizing:  62%|######2   | 1550/2500 [00:16<00:09, 95.58it/s, loss=0.0101]
[Trellis Server] Texture baking (opt): optimizing:  62%|######2   | 1551/2500 [00:16<00:09, 95.58it/s, loss=0.0118]
[Trellis Server] Texture baking (opt): optimizing:  62%|######2   | 1552/2500 [00:16<00:09, 95.58it/s, loss=0.0125]
[Trellis Server] Texture baking (opt): optimizing:  62%|######2   | 1553/2500 [00:16<00:09, 95.58it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  62%|######2   | 1554/2500 [00:16<00:09, 95.58it/s, loss=0.0154]
[Trellis Server] Texture baking (opt): optimizing:  62%|######2   | 1555/2500 [00:16<00:09, 95.58it/s, loss=0.0175]
[Trellis Server] Texture baking (opt): optimizing:  62%|######2   | 1556/2500 [00:16<00:09, 95.58it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  62%|######2   | 1557/2500 [00:16<00:09, 95.58it/s, loss=0.0123]
[Trellis Server] Texture baking (opt): optimizing:  62%|######2   | 1558/2500 [00:16<00:09, 95.58it/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  62%|######2   | 1559/2500 [00:16<00:09, 95.58it/s, loss=0.01]
Texture baking (opt): optimizing:  62%|######2   | 1561/2500 [00:16<00:09, 96.68it/s, loss=0.012]it/s, loss=0.012]
[Trellis Server] Texture baking (opt): optimizing:  62%|######2   | 1561/2500 [00:16<00:09, 96.68it/s, loss=0.0162]
[Trellis Server] Texture baking (opt): optimizing:  62%|######2   | 1562/2500 [00:16<00:09, 96.68it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  63%|######2   | 1563/2500 [00:16<00:09, 96.68it/s, loss=0.0124]
[Trellis Server] Texture baking (opt): optimizing:  63%|######2   | 1564/2500 [00:16<00:09, 96.68it/s, loss=0.0122]
[Trellis Server] Texture baking (opt): optimizing:  63%|######2   | 1565/2500 [00:16<00:09, 96.68it/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  63%|######2   | 1566/2500 [00:16<00:09, 96.68it/s, loss=0.0145]
[Trellis Server] Texture baking (opt): optimizing:  63%|######2   | 1567/2500 [00:16<00:09, 96.68it/s, loss=0.019]
[Trellis Server] Texture baking (opt): optimizing:  63%|######2   | 1568/2500 [00:16<00:09, 96.68it/s, loss=0.0186]
[Trellis Server] Texture baking (opt): optimizing:  63%|######2   | 1569/2500 [00:16<00:09, 96.68it/s, loss=0.0111]
Texture baking (opt): optimizing:  63%|######2   | 1571/2500 [00:16<00:09, 97.35it/s, loss=0.0114]t/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  63%|######2   | 1571/2500 [00:16<00:09, 97.35it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  63%|######2   | 1572/2500 [00:16<00:09, 97.35it/s, loss=0.0131]
[Trellis Server] Texture baking (opt): optimizing:  63%|######2   | 1573/2500 [00:16<00:09, 97.35it/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing:  63%|######2   | 1574/2500 [00:16<00:09, 97.35it/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:  63%|######3   | 1575/2500 [00:16<00:09, 97.35it/s, loss=0.00873]
[Trellis Server] Texture baking (opt): optimizing:  63%|######3   | 1576/2500 [00:16<00:09, 97.35it/s, loss=0.00943]
[Trellis Server] Texture baking (opt): optimizing:  63%|######3   | 1577/2500 [00:16<00:09, 97.35it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  63%|######3   | 1578/2500 [00:16<00:09, 97.35it/s, loss=0.00639]
[Trellis Server] Texture baking (opt): optimizing:  63%|######3   | 1579/2500 [00:16<00:09, 97.35it/s, loss=0.0126]
Texture baking (opt): optimizing:  63%|######3   | 1581/2500 [00:16<00:09, 97.75it/s, loss=0.00614]/s, loss=0.00614]
[Trellis Server] Texture baking (opt): optimizing:  63%|######3   | 1581/2500 [00:16<00:09, 97.75it/s, loss=0.0072]
[Trellis Server] Texture baking (opt): optimizing:  63%|######3   | 1582/2500 [00:16<00:09, 97.75it/s, loss=0.014]
[Trellis Server] Texture baking (opt): optimizing:  63%|######3   | 1583/2500 [00:16<00:09, 97.75it/s, loss=0.0123]
[Trellis Server] Texture baking (opt): optimizing:  63%|######3   | 1584/2500 [00:16<00:09, 97.75it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  63%|######3   | 1585/2500 [00:16<00:09, 97.75it/s, loss=0.00596]
[Trellis Server] Texture baking (opt): optimizing:  63%|######3   | 1586/2500 [00:16<00:09, 97.75it/s, loss=0.0203]
[Trellis Server] Texture baking (opt): optimizing:  63%|######3   | 1587/2500 [00:16<00:09, 97.75it/s, loss=0.0166]
[Trellis Server] Texture baking (opt): optimizing:  64%|######3   | 1588/2500 [00:16<00:09, 97.75it/s, loss=0.0089]
[Trellis Server] Texture baking (opt): optimizing:  64%|######3   | 1589/2500 [00:16<00:09, 97.75it/s, loss=0.00935]
Texture baking (opt): optimizing:  64%|######3   | 1591/2500 [00:16<00:09, 93.75it/s, loss=0.0174]t/s, loss=0.0174]
[Trellis Server] Texture baking (opt): optimizing:  64%|######3   | 1591/2500 [00:16<00:09, 93.75it/s, loss=0.0145]
[Trellis Server] Texture baking (opt): optimizing:  64%|######3   | 1592/2500 [00:16<00:09, 93.75it/s, loss=0.00761]
[Trellis Server] Texture baking (opt): optimizing:  64%|######3   | 1593/2500 [00:16<00:09, 93.75it/s, loss=0.00717]
[Trellis Server] Texture baking (opt): optimizing:  64%|######3   | 1594/2500 [00:16<00:09, 93.75it/s, loss=0.00631]
[Trellis Server] Texture baking (opt): optimizing:  64%|######3   | 1595/2500 [00:16<00:09, 93.75it/s, loss=0.0179]
[Trellis Server] Texture baking (opt): optimizing:  64%|######3   | 1596/2500 [00:16<00:09, 93.75it/s, loss=0.0223]
[Trellis Server] Texture baking (opt): optimizing:  64%|######3   | 1597/2500 [00:16<00:09, 93.75it/s, loss=0.00761]
[Trellis Server] Texture baking (opt): optimizing:  64%|######3   | 1598/2500 [00:16<00:09, 93.75it/s, loss=0.0158]
[Trellis Server] Texture baking (opt): optimizing:  64%|######3   | 1599/2500 [00:16<00:09, 93.75it/s, loss=0.02]
[Trellis Server] Texture baking (opt): optimizing:  64%|######4   | 1600/2500 [00:16<00:09, 93.75it/s, loss=0.0188]
Texture baking (opt): optimizing:  64%|######4   | 1602/2500 [00:16<00:09, 95.39it/s, loss=0.0072]t/s, loss=0.0072]
[Trellis Server] Texture baking (opt): optimizing:  64%|######4   | 1602/2500 [00:16<00:09, 95.39it/s, loss=0.0167]
[Trellis Server] Texture baking (opt): optimizing:  64%|######4   | 1603/2500 [00:16<00:09, 95.39it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  64%|######4   | 1604/2500 [00:16<00:09, 95.39it/s, loss=0.0136]
[Trellis Server] Texture baking (opt): optimizing:  64%|######4   | 1605/2500 [00:16<00:09, 95.39it/s, loss=0.0221]
[Trellis Server] Texture baking (opt): optimizing:  64%|######4   | 1606/2500 [00:16<00:09, 95.39it/s, loss=0.00935]
[Trellis Server] Texture baking (opt): optimizing:  64%|######4   | 1607/2500 [00:16<00:09, 95.39it/s, loss=0.00715]
[Trellis Server] Texture baking (opt): optimizing:  64%|######4   | 1608/2500 [00:16<00:09, 95.39it/s, loss=0.00997]
[Trellis Server] Texture baking (opt): optimizing:  64%|######4   | 1609/2500 [00:16<00:09, 95.39it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  64%|######4   | 1610/2500 [00:16<00:09, 95.39it/s, loss=0.00822]
[Trellis Server] Texture baking (opt): optimizing:  64%|######4   | 1611/2500 [00:16<00:09, 95.39it/s, loss=0.00676]
Texture baking (opt): optimizing:  65%|######4   | 1613/2500 [00:16<00:09, 97.05it/s, loss=0.0196]t/s, loss=0.0196]
[Trellis Server] Texture baking (opt): optimizing:  65%|######4   | 1613/2500 [00:16<00:09, 97.05it/s, loss=0.0105]
[Trellis Server] Texture baking (opt): optimizing:  65%|######4   | 1614/2500 [00:16<00:09, 97.05it/s, loss=0.0132]
[Trellis Server] Texture baking (opt): optimizing:  65%|######4   | 1615/2500 [00:16<00:09, 97.05it/s, loss=0.00592]
[Trellis Server] Texture baking (opt): optimizing:  65%|######4   | 1616/2500 [00:16<00:09, 97.05it/s, loss=0.0177]
[Trellis Server] Texture baking (opt): optimizing:  65%|######4   | 1617/2500 [00:16<00:09, 97.05it/s, loss=0.00907]
[Trellis Server] Texture baking (opt): optimizing:  65%|######4   | 1618/2500 [00:16<00:09, 97.05it/s, loss=0.00855]
[Trellis Server] Texture baking (opt): optimizing:  65%|######4   | 1619/2500 [00:16<00:09, 97.05it/s, loss=0.00697]
[Trellis Server] Texture baking (opt): optimizing:  65%|######4   | 1620/2500 [00:17<00:09, 97.05it/s, loss=0.00468]
[Trellis Server] Texture baking (opt): optimizing:  65%|######4   | 1621/2500 [00:17<00:09, 97.05it/s, loss=0.0176]
Texture baking (opt): optimizing:  65%|######4   | 1623/2500 [00:17<00:09, 97.34it/s, loss=0.0206]t/s, loss=0.0206]
[Trellis Server] Texture baking (opt): optimizing:  65%|######4   | 1623/2500 [00:17<00:09, 97.34it/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  65%|######4   | 1624/2500 [00:17<00:08, 97.34it/s, loss=0.0209]
[Trellis Server] Texture baking (opt): optimizing:  65%|######5   | 1625/2500 [00:17<00:08, 97.34it/s, loss=0.00993]
[Trellis Server] Texture baking (opt): optimizing:  65%|######5   | 1626/2500 [00:17<00:08, 97.34it/s, loss=0.0186]
[Trellis Server] Texture baking (opt): optimizing:  65%|######5   | 1627/2500 [00:17<00:08, 97.34it/s, loss=0.0161]
[Trellis Server] Texture baking (opt): optimizing:  65%|######5   | 1628/2500 [00:17<00:08, 97.34it/s, loss=0.00827]
[Trellis Server] Texture baking (opt): optimizing:  65%|######5   | 1629/2500 [00:17<00:08, 97.34it/s, loss=0.0177]
[Trellis Server] Texture baking (opt): optimizing:  65%|######5   | 1630/2500 [00:17<00:08, 97.34it/s, loss=0.0151]
[Trellis Server] Texture baking (opt): optimizing:  65%|######5   | 1631/2500 [00:17<00:08, 97.34it/s, loss=0.0177]
[Trellis Server] Texture baking (opt): optimizing:  65%|######5   | 1632/2500 [00:17<00:08, 97.34it/s, loss=0.0112]
Texture baking (opt): optimizing:  65%|######5   | 1634/2500 [00:17<00:08, 98.42it/s, loss=0.0137]t/s, loss=0.0137]
[Trellis Server] Texture baking (opt): optimizing:  65%|######5   | 1634/2500 [00:17<00:08, 98.42it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  65%|######5   | 1635/2500 [00:17<00:08, 98.42it/s, loss=0.0128]
[Trellis Server] Texture baking (opt): optimizing:  65%|######5   | 1636/2500 [00:17<00:08, 98.42it/s, loss=0.0128]
[Trellis Server] Texture baking (opt): optimizing:  65%|######5   | 1637/2500 [00:17<00:08, 98.42it/s, loss=0.0102]
[Trellis Server] Texture baking (opt): optimizing:  66%|######5   | 1638/2500 [00:17<00:08, 98.42it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  66%|######5   | 1639/2500 [00:17<00:08, 98.42it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  66%|######5   | 1640/2500 [00:17<00:08, 98.42it/s, loss=0.0148]
[Trellis Server] Texture baking (opt): optimizing:  66%|######5   | 1641/2500 [00:17<00:08, 98.42it/s, loss=0.0179]
[Trellis Server] Texture baking (opt): optimizing:  66%|######5   | 1642/2500 [00:17<00:08, 98.42it/s, loss=0.00843]
Texture baking (opt): optimizing:  66%|######5   | 1644/2500 [00:17<00:09, 94.30it/s, loss=0.0122]t/s, loss=0.0122]
[Trellis Server] Texture baking (opt): optimizing:  66%|######5   | 1644/2500 [00:17<00:09, 94.30it/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  66%|######5   | 1645/2500 [00:17<00:09, 94.30it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  66%|######5   | 1646/2500 [00:17<00:09, 94.30it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  66%|######5   | 1647/2500 [00:17<00:09, 94.30it/s, loss=0.0138]
[Trellis Server] Texture baking (opt): optimizing:  66%|######5   | 1648/2500 [00:17<00:09, 94.30it/s, loss=0.0138]
[Trellis Server] Texture baking (opt): optimizing:  66%|######5   | 1649/2500 [00:17<00:09, 94.30it/s, loss=0.0124]
[Trellis Server] Texture baking (opt): optimizing:  66%|######6   | 1650/2500 [00:17<00:09, 94.30it/s, loss=0.0198]
[Trellis Server] Texture baking (opt): optimizing:  66%|######6   | 1651/2500 [00:17<00:09, 94.30it/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  66%|######6   | 1652/2500 [00:17<00:08, 94.30it/s, loss=0.0137]
Texture baking (opt): optimizing:  66%|######6   | 1654/2500 [00:17<00:08, 94.31it/s, loss=0.00424]/s, loss=0.00424]
[Trellis Server] Texture baking (opt): optimizing:  66%|######6   | 1654/2500 [00:17<00:08, 94.31it/s, loss=0.0125]
[Trellis Server] Texture baking (opt): optimizing:  66%|######6   | 1655/2500 [00:17<00:08, 94.31it/s, loss=0.0167]
[Trellis Server] Texture baking (opt): optimizing:  66%|######6   | 1656/2500 [00:17<00:08, 94.31it/s, loss=0.0162]
[Trellis Server] Texture baking (opt): optimizing:  66%|######6   | 1657/2500 [00:17<00:08, 94.31it/s, loss=0.0103]
[Trellis Server] Texture baking (opt): optimizing:  66%|######6   | 1658/2500 [00:17<00:08, 94.31it/s, loss=0.0161]
[Trellis Server] Texture baking (opt): optimizing:  66%|######6   | 1659/2500 [00:17<00:08, 94.31it/s, loss=0.0129]
[Trellis Server] Texture baking (opt): optimizing:  66%|######6   | 1660/2500 [00:17<00:08, 94.31it/s, loss=0.00756]
[Trellis Server] Texture baking (opt): optimizing:  66%|######6   | 1661/2500 [00:17<00:08, 94.31it/s, loss=0.0123]
[Trellis Server] Texture baking (opt): optimizing:  66%|######6   | 1662/2500 [00:17<00:08, 94.31it/s, loss=0.0106]
Texture baking (opt): optimizing:  67%|######6   | 1664/2500 [00:17<00:08, 94.06it/s, loss=0.0189]t/s, loss=0.0189]
[Trellis Server] Texture baking (opt): optimizing:  67%|######6   | 1664/2500 [00:17<00:08, 94.06it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  67%|######6   | 1665/2500 [00:17<00:08, 94.06it/s, loss=0.0187]
[Trellis Server] Texture baking (opt): optimizing:  67%|######6   | 1666/2500 [00:17<00:08, 94.06it/s, loss=0.0125]
[Trellis Server] Texture baking (opt): optimizing:  67%|######6   | 1667/2500 [00:17<00:08, 94.06it/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  67%|######6   | 1668/2500 [00:17<00:08, 94.06it/s, loss=0.00663]
[Trellis Server] Texture baking (opt): optimizing:  67%|######6   | 1669/2500 [00:17<00:08, 94.06it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  67%|######6   | 1670/2500 [00:17<00:08, 94.06it/s, loss=0.0123]
[Trellis Server] Texture baking (opt): optimizing:  67%|######6   | 1671/2500 [00:17<00:08, 94.06it/s, loss=0.0137]
[Trellis Server] Texture baking (opt): optimizing:  67%|######6   | 1672/2500 [00:17<00:08, 94.06it/s, loss=0.0106]
Texture baking (opt): optimizing:  67%|######6   | 1674/2500 [00:17<00:08, 93.88it/s, loss=0.0156]t/s, loss=0.0156]
[Trellis Server] Texture baking (opt): optimizing:  67%|######6   | 1674/2500 [00:17<00:08, 93.88it/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:  67%|######7   | 1675/2500 [00:17<00:08, 93.88it/s, loss=0.009]
[Trellis Server] Texture baking (opt): optimizing:  67%|######7   | 1676/2500 [00:17<00:08, 93.88it/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  67%|######7   | 1677/2500 [00:17<00:08, 93.88it/s, loss=0.0163]
[Trellis Server] Texture baking (opt): optimizing:  67%|######7   | 1678/2500 [00:17<00:08, 93.88it/s, loss=0.0133]
[Trellis Server] Texture baking (opt): optimizing:  67%|######7   | 1679/2500 [00:17<00:08, 93.88it/s, loss=0.0183]
[Trellis Server] Texture baking (opt): optimizing:  67%|######7   | 1680/2500 [00:17<00:08, 93.88it/s, loss=0.0153]
[Trellis Server] Texture baking (opt): optimizing:  67%|######7   | 1681/2500 [00:17<00:08, 93.88it/s, loss=0.0074]
[Trellis Server] Texture baking (opt): optimizing:  67%|######7   | 1682/2500 [00:17<00:08, 93.88it/s, loss=0.0122]
Texture baking (opt): optimizing:  67%|######7   | 1684/2500 [00:17<00:08, 92.22it/s, loss=0.00961]/s, loss=0.00961]
[Trellis Server] Texture baking (opt): optimizing:  67%|######7   | 1684/2500 [00:17<00:08, 92.22it/s, loss=0.00739]
[Trellis Server] Texture baking (opt): optimizing:  67%|######7   | 1685/2500 [00:17<00:08, 92.22it/s, loss=0.00982]
[Trellis Server] Texture baking (opt): optimizing:  67%|######7   | 1686/2500 [00:17<00:08, 92.22it/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  67%|######7   | 1687/2500 [00:17<00:08, 92.22it/s, loss=0.0156]
[Trellis Server] Texture baking (opt): optimizing:  68%|######7   | 1688/2500 [00:17<00:08, 92.22it/s, loss=0.0116]
[Trellis Server] Texture baking (opt): optimizing:  68%|######7   | 1689/2500 [00:17<00:08, 92.22it/s, loss=0.00819]
[Trellis Server] Texture baking (opt): optimizing:  68%|######7   | 1690/2500 [00:17<00:08, 92.22it/s, loss=0.00861]
[Trellis Server] Texture baking (opt): optimizing:  68%|######7   | 1691/2500 [00:17<00:08, 92.22it/s, loss=0.0129]
[Trellis Server] Texture baking (opt): optimizing:  68%|######7   | 1692/2500 [00:17<00:08, 92.22it/s, loss=0.0119]
Texture baking (opt): optimizing:  68%|######7   | 1694/2500 [00:17<00:08, 92.33it/s, loss=0.0145]t/s, loss=0.0145]
[Trellis Server] Texture baking (opt): optimizing:  68%|######7   | 1694/2500 [00:17<00:08, 92.33it/s, loss=0.0173]
[Trellis Server] Texture baking (opt): optimizing:  68%|######7   | 1695/2500 [00:17<00:08, 92.33it/s, loss=0.0121]
[Trellis Server] Texture baking (opt): optimizing:  68%|######7   | 1696/2500 [00:17<00:08, 92.33it/s, loss=0.014]
[Trellis Server] Texture baking (opt): optimizing:  68%|######7   | 1697/2500 [00:17<00:08, 92.33it/s, loss=0.0136]
[Trellis Server] Texture baking (opt): optimizing:  68%|######7   | 1698/2500 [00:17<00:08, 92.33it/s, loss=0.0182]
[Trellis Server] Texture baking (opt): optimizing:  68%|######7   | 1699/2500 [00:17<00:08, 92.33it/s, loss=0.0107]
[Trellis Server] Texture baking (opt): optimizing:  68%|######8   | 1700/2500 [00:17<00:08, 92.33it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  68%|######8   | 1701/2500 [00:17<00:08, 92.33it/s, loss=0.0155]
[Trellis Server] Texture baking (opt): optimizing:  68%|######8   | 1702/2500 [00:17<00:08, 92.33it/s, loss=0.00748]
Texture baking (opt): optimizing:  68%|######8   | 1704/2500 [00:17<00:08, 92.15it/s, loss=0.00754]/s, loss=0.00754]
[Trellis Server] Texture baking (opt): optimizing:  68%|######8   | 1704/2500 [00:17<00:08, 92.15it/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  68%|######8   | 1705/2500 [00:17<00:08, 92.15it/s, loss=0.0138]
[Trellis Server] Texture baking (opt): optimizing:  68%|######8   | 1706/2500 [00:17<00:08, 92.15it/s, loss=0.0201]
[Trellis Server] Texture baking (opt): optimizing:  68%|######8   | 1707/2500 [00:17<00:08, 92.15it/s, loss=0.0143]
[Trellis Server] Texture baking (opt): optimizing:  68%|######8   | 1708/2500 [00:17<00:08, 92.15it/s, loss=0.0147]
[Trellis Server] Texture baking (opt): optimizing:  68%|######8   | 1709/2500 [00:17<00:08, 92.15it/s, loss=0.00847]
[Trellis Server] Texture baking (opt): optimizing:  68%|######8   | 1710/2500 [00:17<00:08, 92.15it/s, loss=0.0131]
[Trellis Server] Texture baking (opt): optimizing:  68%|######8   | 1711/2500 [00:17<00:08, 92.15it/s, loss=0.00828]
[Trellis Server] Texture baking (opt): optimizing:  68%|######8   | 1712/2500 [00:18<00:08, 92.15it/s, loss=0.014]
Texture baking (opt): optimizing:  69%|######8   | 1714/2500 [00:18<00:08, 90.05it/s, loss=0.011]it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  69%|######8   | 1714/2500 [00:18<00:08, 90.05it/s, loss=0.0223]
[Trellis Server] Texture baking (opt): optimizing:  69%|######8   | 1715/2500 [00:18<00:08, 90.05it/s, loss=0.0145]
[Trellis Server] Texture baking (opt): optimizing:  69%|######8   | 1716/2500 [00:18<00:08, 90.05it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  69%|######8   | 1717/2500 [00:18<00:08, 90.05it/s, loss=0.0142]
[Trellis Server] Texture baking (opt): optimizing:  69%|######8   | 1718/2500 [00:18<00:08, 90.05it/s, loss=0.014]
[Trellis Server] Texture baking (opt): optimizing:  69%|######8   | 1719/2500 [00:18<00:08, 90.05it/s, loss=0.0193]
[Trellis Server] Texture baking (opt): optimizing:  69%|######8   | 1720/2500 [00:18<00:08, 90.05it/s, loss=0.017]
[Trellis Server] Texture baking (opt): optimizing:  69%|######8   | 1721/2500 [00:18<00:08, 90.05it/s, loss=0.0124]
[Trellis Server] Texture baking (opt): optimizing:  69%|######8   | 1722/2500 [00:18<00:08, 90.05it/s, loss=0.0121]
Texture baking (opt): optimizing:  69%|######8   | 1724/2500 [00:18<00:08, 90.55it/s, loss=0.0119]t/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  69%|######8   | 1724/2500 [00:18<00:08, 90.55it/s, loss=0.00734]
[Trellis Server] Texture baking (opt): optimizing:  69%|######9   | 1725/2500 [00:18<00:08, 90.55it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  69%|######9   | 1726/2500 [00:18<00:08, 90.55it/s, loss=0.0105]
[Trellis Server] Texture baking (opt): optimizing:  69%|######9   | 1727/2500 [00:18<00:08, 90.55it/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  69%|######9   | 1728/2500 [00:18<00:08, 90.55it/s, loss=0.00877]
[Trellis Server] Texture baking (opt): optimizing:  69%|######9   | 1729/2500 [00:18<00:08, 90.55it/s, loss=0.012]
[Trellis Server] Texture baking (opt): optimizing:  69%|######9   | 1730/2500 [00:18<00:08, 90.55it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  69%|######9   | 1731/2500 [00:18<00:08, 90.55it/s, loss=0.00855]
[Trellis Server] Texture baking (opt): optimizing:  69%|######9   | 1732/2500 [00:18<00:08, 90.55it/s, loss=0.00938]
Texture baking (opt): optimizing:  69%|######9   | 1734/2500 [00:18<00:08, 90.44it/s, loss=0.0125]t/s, loss=0.0125]
[Trellis Server] Texture baking (opt): optimizing:  69%|######9   | 1734/2500 [00:18<00:08, 90.44it/s, loss=0.0131]
[Trellis Server] Texture baking (opt): optimizing:  69%|######9   | 1735/2500 [00:18<00:08, 90.44it/s, loss=0.0153]
[Trellis Server] Texture baking (opt): optimizing:  69%|######9   | 1736/2500 [00:18<00:08, 90.44it/s, loss=0.0128]
[Trellis Server] Texture baking (opt): optimizing:  69%|######9   | 1737/2500 [00:18<00:08, 90.44it/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  70%|######9   | 1738/2500 [00:18<00:08, 90.44it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  70%|######9   | 1739/2500 [00:18<00:08, 90.44it/s, loss=0.0137]
[Trellis Server] Texture baking (opt): optimizing:  70%|######9   | 1740/2500 [00:18<00:08, 90.44it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  70%|######9   | 1741/2500 [00:18<00:08, 90.44it/s, loss=0.0163]
[Trellis Server] Texture baking (opt): optimizing:  70%|######9   | 1742/2500 [00:18<00:08, 90.44it/s, loss=0.00808]
Texture baking (opt): optimizing:  70%|######9   | 1744/2500 [00:18<00:08, 90.40it/s, loss=0.0174]t/s, loss=0.0174]
[Trellis Server] Texture baking (opt): optimizing:  70%|######9   | 1744/2500 [00:18<00:08, 90.40it/s, loss=0.0176]
[Trellis Server] Texture baking (opt): optimizing:  70%|######9   | 1745/2500 [00:18<00:08, 90.40it/s, loss=0.0145]
[Trellis Server] Texture baking (opt): optimizing:  70%|######9   | 1746/2500 [00:18<00:08, 90.40it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  70%|######9   | 1747/2500 [00:18<00:08, 90.40it/s, loss=0.014]
[Trellis Server] Texture baking (opt): optimizing:  70%|######9   | 1748/2500 [00:18<00:08, 90.40it/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  70%|######9   | 1749/2500 [00:18<00:08, 90.40it/s, loss=0.00787]
[Trellis Server] Texture baking (opt): optimizing:  70%|#######   | 1750/2500 [00:18<00:08, 90.40it/s, loss=0.0135]
[Trellis Server] Texture baking (opt): optimizing:  70%|#######   | 1751/2500 [00:18<00:08, 90.40it/s, loss=0.00778]
[Trellis Server] Texture baking (opt): optimizing:  70%|#######   | 1752/2500 [00:18<00:08, 90.40it/s, loss=0.0145]
Texture baking (opt): optimizing:  70%|#######   | 1754/2500 [00:18<00:08, 91.89it/s, loss=0.0109]t/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  70%|#######   | 1754/2500 [00:18<00:08, 91.89it/s, loss=0.0116]
[Trellis Server] Texture baking (opt): optimizing:  70%|#######   | 1755/2500 [00:18<00:08, 91.89it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  70%|#######   | 1756/2500 [00:18<00:08, 91.89it/s, loss=0.00957]
[Trellis Server] Texture baking (opt): optimizing:  70%|#######   | 1757/2500 [00:18<00:08, 91.89it/s, loss=0.00928]
[Trellis Server] Texture baking (opt): optimizing:  70%|#######   | 1758/2500 [00:18<00:08, 91.89it/s, loss=0.0118]
[Trellis Server] Texture baking (opt): optimizing:  70%|#######   | 1759/2500 [00:18<00:08, 91.89it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  70%|#######   | 1760/2500 [00:18<00:08, 91.89it/s, loss=0.00996]
[Trellis Server] Texture baking (opt): optimizing:  70%|#######   | 1761/2500 [00:18<00:08, 91.89it/s, loss=0.0128]
[Trellis Server] Texture baking (opt): optimizing:  70%|#######   | 1762/2500 [00:18<00:08, 91.89it/s, loss=0.0142]
Texture baking (opt): optimizing:  71%|#######   | 1764/2500 [00:18<00:07, 93.89it/s, loss=0.0114]t/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  71%|#######   | 1764/2500 [00:18<00:07, 93.89it/s, loss=0.00799]
[Trellis Server] Texture baking (opt): optimizing:  71%|#######   | 1765/2500 [00:18<00:07, 93.89it/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  71%|#######   | 1766/2500 [00:18<00:07, 93.89it/s, loss=0.00793]
[Trellis Server] Texture baking (opt): optimizing:  71%|#######   | 1767/2500 [00:18<00:07, 93.89it/s, loss=0.0107]
[Trellis Server] Texture baking (opt): optimizing:  71%|#######   | 1768/2500 [00:18<00:07, 93.89it/s, loss=0.0137]
[Trellis Server] Texture baking (opt): optimizing:  71%|#######   | 1769/2500 [00:18<00:07, 93.89it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  71%|#######   | 1770/2500 [00:18<00:07, 93.89it/s, loss=0.0156]
[Trellis Server] Texture baking (opt): optimizing:  71%|#######   | 1771/2500 [00:18<00:07, 93.89it/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  71%|#######   | 1772/2500 [00:18<00:07, 93.89it/s, loss=0.0131]
Texture baking (opt): optimizing:  71%|#######   | 1774/2500 [00:18<00:07, 94.55it/s, loss=0.0143]t/s, loss=0.0143]
[Trellis Server] Texture baking (opt): optimizing:  71%|#######   | 1774/2500 [00:18<00:07, 94.55it/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  71%|#######1  | 1775/2500 [00:18<00:07, 94.55it/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:  71%|#######1  | 1776/2500 [00:18<00:07, 94.55it/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  71%|#######1  | 1777/2500 [00:18<00:07, 94.55it/s, loss=0.0105]
[Trellis Server] Texture baking (opt): optimizing:  71%|#######1  | 1778/2500 [00:18<00:07, 94.55it/s, loss=0.0165]
[Trellis Server] Texture baking (opt): optimizing:  71%|#######1  | 1779/2500 [00:18<00:07, 94.55it/s, loss=0.0125]
[Trellis Server] Texture baking (opt): optimizing:  71%|#######1  | 1780/2500 [00:18<00:07, 94.55it/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:  71%|#######1  | 1781/2500 [00:18<00:07, 94.55it/s, loss=0.00958]
[Trellis Server] Texture baking (opt): optimizing:  71%|#######1  | 1782/2500 [00:18<00:07, 94.55it/s, loss=0.0132]
Texture baking (opt): optimizing:  71%|#######1  | 1784/2500 [00:18<00:07, 93.69it/s, loss=0.0109]t/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  71%|#######1  | 1784/2500 [00:18<00:07, 93.69it/s, loss=0.00395]
[Trellis Server] Texture baking (opt): optimizing:  71%|#######1  | 1785/2500 [00:18<00:07, 93.69it/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:  71%|#######1  | 1786/2500 [00:18<00:07, 93.69it/s, loss=0.0146]
[Trellis Server] Texture baking (opt): optimizing:  71%|#######1  | 1787/2500 [00:18<00:07, 93.69it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  72%|#######1  | 1788/2500 [00:18<00:07, 93.69it/s, loss=0.0176]
[Trellis Server] Texture baking (opt): optimizing:  72%|#######1  | 1789/2500 [00:18<00:07, 93.69it/s, loss=0.0118]
[Trellis Server] Texture baking (opt): optimizing:  72%|#######1  | 1790/2500 [00:18<00:07, 93.69it/s, loss=0.00913]
[Trellis Server] Texture baking (opt): optimizing:  72%|#######1  | 1791/2500 [00:18<00:07, 93.69it/s, loss=0.00938]
[Trellis Server] Texture baking (opt): optimizing:  72%|#######1  | 1792/2500 [00:18<00:07, 93.69it/s, loss=0.0104]
Texture baking (opt): optimizing:  72%|#######1  | 1794/2500 [00:18<00:07, 95.50it/s, loss=0.0117]t/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  72%|#######1  | 1794/2500 [00:18<00:07, 95.50it/s, loss=0.00971]
[Trellis Server] Texture baking (opt): optimizing:  72%|#######1  | 1795/2500 [00:18<00:07, 95.50it/s, loss=0.0187]
[Trellis Server] Texture baking (opt): optimizing:  72%|#######1  | 1796/2500 [00:18<00:07, 95.50it/s, loss=0.0153]
[Trellis Server] Texture baking (opt): optimizing:  72%|#######1  | 1797/2500 [00:18<00:07, 95.50it/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  72%|#######1  | 1798/2500 [00:18<00:07, 95.50it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  72%|#######1  | 1799/2500 [00:18<00:07, 95.50it/s, loss=0.0164]
[Trellis Server] Texture baking (opt): optimizing:  72%|#######2  | 1800/2500 [00:18<00:07, 95.50it/s, loss=0.0128]
[Trellis Server] Texture baking (opt): optimizing:  72%|#######2  | 1801/2500 [00:18<00:07, 95.50it/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing:  72%|#######2  | 1802/2500 [00:18<00:07, 95.50it/s, loss=0.0107]
Texture baking (opt): optimizing:  72%|#######2  | 1804/2500 [00:18<00:07, 94.88it/s, loss=0.00987]/s, loss=0.00987]
[Trellis Server] Texture baking (opt): optimizing:  72%|#######2  | 1804/2500 [00:18<00:07, 94.88it/s, loss=0.012]
[Trellis Server] Texture baking (opt): optimizing:  72%|#######2  | 1805/2500 [00:18<00:07, 94.88it/s, loss=0.0146]
[Trellis Server] Texture baking (opt): optimizing:  72%|#######2  | 1806/2500 [00:19<00:07, 94.88it/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  72%|#######2  | 1807/2500 [00:19<00:07, 94.88it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  72%|#######2  | 1808/2500 [00:19<00:07, 94.88it/s, loss=0.0181]
[Trellis Server] Texture baking (opt): optimizing:  72%|#######2  | 1809/2500 [00:19<00:07, 94.88it/s, loss=0.0125]
[Trellis Server] Texture baking (opt): optimizing:  72%|#######2  | 1810/2500 [00:19<00:07, 94.88it/s, loss=0.0154]
[Trellis Server] Texture baking (opt): optimizing:  72%|#######2  | 1811/2500 [00:19<00:07, 94.88it/s, loss=0.0103]
[Trellis Server] Texture baking (opt): optimizing:  72%|#######2  | 1812/2500 [00:19<00:07, 94.88it/s, loss=0.0108]
Texture baking (opt): optimizing:  73%|#######2  | 1814/2500 [00:19<00:07, 94.31it/s, loss=0.0108]t/s, loss=0.0108]
[Trellis Server] Texture baking (opt): optimizing:  73%|#######2  | 1814/2500 [00:19<00:07, 94.31it/s, loss=0.00894]
[Trellis Server] Texture baking (opt): optimizing:  73%|#######2  | 1815/2500 [00:19<00:07, 94.31it/s, loss=0.00863]
[Trellis Server] Texture baking (opt): optimizing:  73%|#######2  | 1816/2500 [00:19<00:07, 94.31it/s, loss=0.0128]
[Trellis Server] Texture baking (opt): optimizing:  73%|#######2  | 1817/2500 [00:19<00:07, 94.31it/s, loss=0.00757]
[Trellis Server] Texture baking (opt): optimizing:  73%|#######2  | 1818/2500 [00:19<00:07, 94.31it/s, loss=0.00929]
[Trellis Server] Texture baking (opt): optimizing:  73%|#######2  | 1819/2500 [00:19<00:07, 94.31it/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  73%|#######2  | 1820/2500 [00:19<00:07, 94.31it/s, loss=0.0196]
[Trellis Server] Texture baking (opt): optimizing:  73%|#######2  | 1821/2500 [00:19<00:07, 94.31it/s, loss=0.0157]
[Trellis Server] Texture baking (opt): optimizing:  73%|#######2  | 1822/2500 [00:19<00:07, 94.31it/s, loss=0.0198]
Texture baking (opt): optimizing:  73%|#######2  | 1824/2500 [00:19<00:07, 94.76it/s, loss=0.0116]t/s, loss=0.0116]
[Trellis Server] Texture baking (opt): optimizing:  73%|#######2  | 1824/2500 [00:19<00:07, 94.76it/s, loss=0.00333]
[Trellis Server] Texture baking (opt): optimizing:  73%|#######3  | 1825/2500 [00:19<00:07, 94.76it/s, loss=0.0105]
[Trellis Server] Texture baking (opt): optimizing:  73%|#######3  | 1826/2500 [00:19<00:07, 94.76it/s, loss=0.00937]
[Trellis Server] Texture baking (opt): optimizing:  73%|#######3  | 1827/2500 [00:19<00:07, 94.76it/s, loss=0.0105]
[Trellis Server] Texture baking (opt): optimizing:  73%|#######3  | 1828/2500 [00:19<00:07, 94.76it/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  73%|#######3  | 1829/2500 [00:19<00:07, 94.76it/s, loss=0.0161]
[Trellis Server] Texture baking (opt): optimizing:  73%|#######3  | 1830/2500 [00:19<00:07, 94.76it/s, loss=0.00854]
[Trellis Server] Texture baking (opt): optimizing:  73%|#######3  | 1831/2500 [00:19<00:07, 94.76it/s, loss=0.0077]
[Trellis Server] Texture baking (opt): optimizing:  73%|#######3  | 1832/2500 [00:19<00:07, 94.76it/s, loss=0.00969]
Texture baking (opt): optimizing:  73%|#######3  | 1834/2500 [00:19<00:07, 94.90it/s, loss=0.00883]/s, loss=0.00883]
[Trellis Server] Texture baking (opt): optimizing:  73%|#######3  | 1834/2500 [00:19<00:07, 94.90it/s, loss=0.00762]
[Trellis Server] Texture baking (opt): optimizing:  73%|#######3  | 1835/2500 [00:19<00:07, 94.90it/s, loss=0.0105]
[Trellis Server] Texture baking (opt): optimizing:  73%|#######3  | 1836/2500 [00:19<00:06, 94.90it/s, loss=0.00909]
[Trellis Server] Texture baking (opt): optimizing:  73%|#######3  | 1837/2500 [00:19<00:06, 94.90it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  74%|#######3  | 1838/2500 [00:19<00:06, 94.90it/s, loss=0.0197]
[Trellis Server] Texture baking (opt): optimizing:  74%|#######3  | 1839/2500 [00:19<00:06, 94.90it/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  74%|#######3  | 1840/2500 [00:19<00:06, 94.90it/s, loss=0.00761]
[Trellis Server] Texture baking (opt): optimizing:  74%|#######3  | 1841/2500 [00:19<00:06, 94.90it/s, loss=0.00687]
[Trellis Server] Texture baking (opt): optimizing:  74%|#######3  | 1842/2500 [00:19<00:06, 94.90it/s, loss=0.00674]
Texture baking (opt): optimizing:  74%|#######3  | 1844/2500 [00:19<00:06, 95.27it/s, loss=0.0144]t/s, loss=0.0144]
[Trellis Server] Texture baking (opt): optimizing:  74%|#######3  | 1844/2500 [00:19<00:06, 95.27it/s, loss=0.00942]
[Trellis Server] Texture baking (opt): optimizing:  74%|#######3  | 1845/2500 [00:19<00:06, 95.27it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  74%|#######3  | 1846/2500 [00:19<00:06, 95.27it/s, loss=0.00792]
[Trellis Server] Texture baking (opt): optimizing:  74%|#######3  | 1847/2500 [00:19<00:06, 95.27it/s, loss=0.0161]
[Trellis Server] Texture baking (opt): optimizing:  74%|#######3  | 1848/2500 [00:19<00:06, 95.27it/s, loss=0.012]
[Trellis Server] Texture baking (opt): optimizing:  74%|#######3  | 1849/2500 [00:19<00:06, 95.27it/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  74%|#######4  | 1850/2500 [00:19<00:06, 95.27it/s, loss=0.0144]
[Trellis Server] Texture baking (opt): optimizing:  74%|#######4  | 1851/2500 [00:19<00:06, 95.27it/s, loss=0.0108]
[Trellis Server] Texture baking (opt): optimizing:  74%|#######4  | 1852/2500 [00:19<00:06, 95.27it/s, loss=0.0155]
Texture baking (opt): optimizing:  74%|#######4  | 1854/2500 [00:19<00:06, 95.27it/s, loss=0.0106]t/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing:  74%|#######4  | 1854/2500 [00:19<00:06, 95.27it/s, loss=0.00971]
[Trellis Server] Texture baking (opt): optimizing:  74%|#######4  | 1855/2500 [00:19<00:06, 95.27it/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  74%|#######4  | 1856/2500 [00:19<00:06, 95.27it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  74%|#######4  | 1857/2500 [00:19<00:06, 95.27it/s, loss=0.0191]
[Trellis Server] Texture baking (opt): optimizing:  74%|#######4  | 1858/2500 [00:19<00:06, 95.27it/s, loss=0.0148]
[Trellis Server] Texture baking (opt): optimizing:  74%|#######4  | 1859/2500 [00:19<00:06, 95.27it/s, loss=0.0149]
[Trellis Server] Texture baking (opt): optimizing:  74%|#######4  | 1860/2500 [00:19<00:06, 95.27it/s, loss=0.0102]
[Trellis Server] Texture baking (opt): optimizing:  74%|#######4  | 1861/2500 [00:19<00:06, 95.27it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  74%|#######4  | 1862/2500 [00:19<00:06, 95.27it/s, loss=0.0134]
Texture baking (opt): optimizing:  75%|#######4  | 1864/2500 [00:19<00:06, 96.08it/s, loss=0.0117]t/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  75%|#######4  | 1864/2500 [00:19<00:06, 96.08it/s, loss=0.00959]
[Trellis Server] Texture baking (opt): optimizing:  75%|#######4  | 1865/2500 [00:19<00:06, 96.08it/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing:  75%|#######4  | 1866/2500 [00:19<00:06, 96.08it/s, loss=0.0131]
[Trellis Server] Texture baking (opt): optimizing:  75%|#######4  | 1867/2500 [00:19<00:06, 96.08it/s, loss=0.0118]
[Trellis Server] Texture baking (opt): optimizing:  75%|#######4  | 1868/2500 [00:19<00:06, 96.08it/s, loss=0.00878]
[Trellis Server] Texture baking (opt): optimizing:  75%|#######4  | 1869/2500 [00:19<00:06, 96.08it/s, loss=0.0103]
[Trellis Server] Texture baking (opt): optimizing:  75%|#######4  | 1870/2500 [00:19<00:06, 96.08it/s, loss=0.00921]
[Trellis Server] Texture baking (opt): optimizing:  75%|#######4  | 1871/2500 [00:19<00:06, 96.08it/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing:  75%|#######4  | 1872/2500 [00:19<00:06, 96.08it/s, loss=0.0122]
Texture baking (opt): optimizing:  75%|#######4  | 1874/2500 [00:19<00:06, 97.22it/s, loss=0.0122]t/s, loss=0.0122]
[Trellis Server] Texture baking (opt): optimizing:  75%|#######4  | 1874/2500 [00:19<00:06, 97.22it/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:  75%|#######5  | 1875/2500 [00:19<00:06, 97.22it/s, loss=0.0102]
[Trellis Server] Texture baking (opt): optimizing:  75%|#######5  | 1876/2500 [00:19<00:06, 97.22it/s, loss=0.01]
[Trellis Server] Texture baking (opt): optimizing:  75%|#######5  | 1877/2500 [00:19<00:06, 97.22it/s, loss=0.00831]
[Trellis Server] Texture baking (opt): optimizing:  75%|#######5  | 1878/2500 [00:19<00:06, 97.22it/s, loss=0.00968]
[Trellis Server] Texture baking (opt): optimizing:  75%|#######5  | 1879/2500 [00:19<00:06, 97.22it/s, loss=0.0155]
[Trellis Server] Texture baking (opt): optimizing:  75%|#######5  | 1880/2500 [00:19<00:06, 97.22it/s, loss=0.00822]
[Trellis Server] Texture baking (opt): optimizing:  75%|#######5  | 1881/2500 [00:19<00:06, 97.22it/s, loss=0.00732]
[Trellis Server] Texture baking (opt): optimizing:  75%|#######5  | 1882/2500 [00:19<00:06, 97.22it/s, loss=0.0154]
[Trellis Server] Texture baking (opt): optimizing:  75%|#######5  | 1883/2500 [00:19<00:06, 97.22it/s, loss=0.0119]
Texture baking (opt): optimizing:  75%|#######5  | 1885/2500 [00:19<00:06, 96.44it/s, loss=0.0129]t/s, loss=0.0129]
[Trellis Server] Texture baking (opt): optimizing:  75%|#######5  | 1885/2500 [00:19<00:06, 96.44it/s, loss=0.00782]
[Trellis Server] Texture baking (opt): optimizing:  75%|#######5  | 1886/2500 [00:19<00:06, 96.44it/s, loss=0.00981]
[Trellis Server] Texture baking (opt): optimizing:  75%|#######5  | 1887/2500 [00:19<00:06, 96.44it/s, loss=0.0169]
[Trellis Server] Texture baking (opt): optimizing:  76%|#######5  | 1888/2500 [00:19<00:06, 96.44it/s, loss=0.00929]
[Trellis Server] Texture baking (opt): optimizing:  76%|#######5  | 1889/2500 [00:19<00:06, 96.44it/s, loss=0.0124]
[Trellis Server] Texture baking (opt): optimizing:  76%|#######5  | 1890/2500 [00:19<00:06, 96.44it/s, loss=0.0142]
[Trellis Server] Texture baking (opt): optimizing:  76%|#######5  | 1891/2500 [00:19<00:06, 96.44it/s, loss=0.0136]
[Trellis Server] Texture baking (opt): optimizing:  76%|#######5  | 1892/2500 [00:19<00:06, 96.44it/s, loss=0.00895]
[Trellis Server] Texture baking (opt): optimizing:  76%|#######5  | 1893/2500 [00:19<00:06, 96.44it/s, loss=0.00919]
Texture baking (opt): optimizing:  76%|#######5  | 1895/2500 [00:19<00:06, 97.18it/s, loss=0.0129]t/s, loss=0.0129]
[Trellis Server] Texture baking (opt): optimizing:  76%|#######5  | 1895/2500 [00:19<00:06, 97.18it/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  76%|#######5  | 1896/2500 [00:19<00:06, 97.18it/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  76%|#######5  | 1897/2500 [00:19<00:06, 97.18it/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:  76%|#######5  | 1898/2500 [00:19<00:06, 97.18it/s, loss=0.017]
[Trellis Server] Texture baking (opt): optimizing:  76%|#######5  | 1899/2500 [00:19<00:06, 97.18it/s, loss=0.0122]
[Trellis Server] Texture baking (opt): optimizing:  76%|#######6  | 1900/2500 [00:19<00:06, 97.18it/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing:  76%|#######6  | 1901/2500 [00:19<00:06, 97.18it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  76%|#######6  | 1902/2500 [00:19<00:06, 97.18it/s, loss=0.0116]
[Trellis Server] Texture baking (opt): optimizing:  76%|#######6  | 1903/2500 [00:20<00:06, 97.18it/s, loss=0.0103]
[Trellis Server] Texture baking (opt): optimizing:  76%|#######6  | 1904/2500 [00:20<00:06, 97.18it/s, loss=0.0105]
Texture baking (opt): optimizing:  76%|#######6  | 1906/2500 [00:20<00:06, 98.06it/s, loss=0.0109]t/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  76%|#######6  | 1906/2500 [00:20<00:06, 98.06it/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  76%|#######6  | 1907/2500 [00:20<00:06, 98.06it/s, loss=0.0125]
[Trellis Server] Texture baking (opt): optimizing:  76%|#######6  | 1908/2500 [00:20<00:06, 98.06it/s, loss=0.00872]
[Trellis Server] Texture baking (opt): optimizing:  76%|#######6  | 1909/2500 [00:20<00:06, 98.06it/s, loss=0.0146]
[Trellis Server] Texture baking (opt): optimizing:  76%|#######6  | 1910/2500 [00:20<00:06, 98.06it/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing:  76%|#######6  | 1911/2500 [00:20<00:06, 98.06it/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  76%|#######6  | 1912/2500 [00:20<00:05, 98.06it/s, loss=0.00692]
[Trellis Server] Texture baking (opt): optimizing:  77%|#######6  | 1913/2500 [00:20<00:05, 98.06it/s, loss=0.0149]
[Trellis Server] Texture baking (opt): optimizing:  77%|#######6  | 1914/2500 [00:20<00:05, 98.06it/s, loss=0.0128]
Texture baking (opt): optimizing:  77%|#######6  | 1916/2500 [00:20<00:06, 96.94it/s, loss=0.0163]t/s, loss=0.0163]
[Trellis Server] Texture baking (opt): optimizing:  77%|#######6  | 1916/2500 [00:20<00:06, 96.94it/s, loss=0.0158]
[Trellis Server] Texture baking (opt): optimizing:  77%|#######6  | 1917/2500 [00:20<00:06, 96.94it/s, loss=0.0104]
[Trellis Server] Texture baking (opt): optimizing:  77%|#######6  | 1918/2500 [00:20<00:06, 96.94it/s, loss=0.0108]
[Trellis Server] Texture baking (opt): optimizing:  77%|#######6  | 1919/2500 [00:20<00:05, 96.94it/s, loss=0.00948]
[Trellis Server] Texture baking (opt): optimizing:  77%|#######6  | 1920/2500 [00:20<00:05, 96.94it/s, loss=0.00822]
[Trellis Server] Texture baking (opt): optimizing:  77%|#######6  | 1921/2500 [00:20<00:05, 96.94it/s, loss=0.0103]
[Trellis Server] Texture baking (opt): optimizing:  77%|#######6  | 1922/2500 [00:20<00:05, 96.94it/s, loss=0.0157]
[Trellis Server] Texture baking (opt): optimizing:  77%|#######6  | 1923/2500 [00:20<00:05, 96.94it/s, loss=0.0132]
[Trellis Server] Texture baking (opt): optimizing:  77%|#######6  | 1924/2500 [00:20<00:05, 96.94it/s, loss=0.0118]
Texture baking (opt): optimizing:  77%|#######7  | 1926/2500 [00:20<00:05, 97.27it/s, loss=0.0115]t/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  77%|#######7  | 1926/2500 [00:20<00:05, 97.27it/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:  77%|#######7  | 1927/2500 [00:20<00:05, 97.27it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  77%|#######7  | 1928/2500 [00:20<00:05, 97.27it/s, loss=0.0123]
[Trellis Server] Texture baking (opt): optimizing:  77%|#######7  | 1929/2500 [00:20<00:05, 97.27it/s, loss=0.0118]
[Trellis Server] Texture baking (opt): optimizing:  77%|#######7  | 1930/2500 [00:20<00:05, 97.27it/s, loss=0.0122]
[Trellis Server] Texture baking (opt): optimizing:  77%|#######7  | 1931/2500 [00:20<00:05, 97.27it/s, loss=0.0107]
[Trellis Server] Texture baking (opt): optimizing:  77%|#######7  | 1932/2500 [00:20<00:05, 97.27it/s, loss=0.0103]
[Trellis Server] Texture baking (opt): optimizing:  77%|#######7  | 1933/2500 [00:20<00:05, 97.27it/s, loss=0.00708]
[Trellis Server] Texture baking (opt): optimizing:  77%|#######7  | 1934/2500 [00:20<00:05, 97.27it/s, loss=0.0108]
Texture baking (opt): optimizing:  77%|#######7  | 1936/2500 [00:20<00:05, 96.67it/s, loss=0.00847]/s, loss=0.00847]
[Trellis Server] Texture baking (opt): optimizing:  77%|#######7  | 1936/2500 [00:20<00:05, 96.67it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  77%|#######7  | 1937/2500 [00:20<00:05, 96.67it/s, loss=0.0135]
[Trellis Server] Texture baking (opt): optimizing:  78%|#######7  | 1938/2500 [00:20<00:05, 96.67it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  78%|#######7  | 1939/2500 [00:20<00:05, 96.67it/s, loss=0.0135]
[Trellis Server] Texture baking (opt): optimizing:  78%|#######7  | 1940/2500 [00:20<00:05, 96.67it/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  78%|#######7  | 1941/2500 [00:20<00:05, 96.67it/s, loss=0.0159]
[Trellis Server] Texture baking (opt): optimizing:  78%|#######7  | 1942/2500 [00:20<00:05, 96.67it/s, loss=0.0177]
[Trellis Server] Texture baking (opt): optimizing:  78%|#######7  | 1943/2500 [00:20<00:05, 96.67it/s, loss=0.0107]
[Trellis Server] Texture baking (opt): optimizing:  78%|#######7  | 1944/2500 [00:20<00:05, 96.67it/s, loss=0.00934]
Texture baking (opt): optimizing:  78%|#######7  | 1946/2500 [00:20<00:05, 96.50it/s, loss=0.0102]t/s, loss=0.0102]
[Trellis Server] Texture baking (opt): optimizing:  78%|#######7  | 1946/2500 [00:20<00:05, 96.50it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  78%|#######7  | 1947/2500 [00:20<00:05, 96.50it/s, loss=0.0121]
[Trellis Server] Texture baking (opt): optimizing:  78%|#######7  | 1948/2500 [00:20<00:05, 96.50it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  78%|#######7  | 1949/2500 [00:20<00:05, 96.50it/s, loss=0.0108]
[Trellis Server] Texture baking (opt): optimizing:  78%|#######8  | 1950/2500 [00:20<00:05, 96.50it/s, loss=0.00774]
[Trellis Server] Texture baking (opt): optimizing:  78%|#######8  | 1951/2500 [00:20<00:05, 96.50it/s, loss=0.0125]
[Trellis Server] Texture baking (opt): optimizing:  78%|#######8  | 1952/2500 [00:20<00:05, 96.50it/s, loss=0.0145]
[Trellis Server] Texture baking (opt): optimizing:  78%|#######8  | 1953/2500 [00:20<00:05, 96.50it/s, loss=0.0152]
[Trellis Server] Texture baking (opt): optimizing:  78%|#######8  | 1954/2500 [00:20<00:05, 96.50it/s, loss=0.011]
Texture baking (opt): optimizing:  78%|#######8  | 1956/2500 [00:20<00:05, 93.97it/s, loss=0.0117]t/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  78%|#######8  | 1956/2500 [00:20<00:05, 93.97it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  78%|#######8  | 1957/2500 [00:20<00:05, 93.97it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  78%|#######8  | 1958/2500 [00:20<00:05, 93.97it/s, loss=0.00707]
[Trellis Server] Texture baking (opt): optimizing:  78%|#######8  | 1959/2500 [00:20<00:05, 93.97it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  78%|#######8  | 1960/2500 [00:20<00:05, 93.97it/s, loss=0.0108]
[Trellis Server] Texture baking (opt): optimizing:  78%|#######8  | 1961/2500 [00:20<00:05, 93.97it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  78%|#######8  | 1962/2500 [00:20<00:05, 93.97it/s, loss=0.0161]
[Trellis Server] Texture baking (opt): optimizing:  79%|#######8  | 1963/2500 [00:20<00:05, 93.97it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  79%|#######8  | 1964/2500 [00:20<00:05, 93.97it/s, loss=0.0128]
Texture baking (opt): optimizing:  79%|#######8  | 1966/2500 [00:20<00:05, 92.45it/s, loss=0.013]it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  79%|#######8  | 1966/2500 [00:20<00:05, 92.45it/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing:  79%|#######8  | 1967/2500 [00:20<00:05, 92.45it/s, loss=0.0129]
[Trellis Server] Texture baking (opt): optimizing:  79%|#######8  | 1968/2500 [00:20<00:05, 92.45it/s, loss=0.0145]
[Trellis Server] Texture baking (opt): optimizing:  79%|#######8  | 1969/2500 [00:20<00:05, 92.45it/s, loss=0.00996]
[Trellis Server] Texture baking (opt): optimizing:  79%|#######8  | 1970/2500 [00:20<00:05, 92.45it/s, loss=0.00942]
[Trellis Server] Texture baking (opt): optimizing:  79%|#######8  | 1971/2500 [00:20<00:05, 92.45it/s, loss=0.0144]
[Trellis Server] Texture baking (opt): optimizing:  79%|#######8  | 1972/2500 [00:20<00:05, 92.45it/s, loss=0.0157]
[Trellis Server] Texture baking (opt): optimizing:  79%|#######8  | 1973/2500 [00:20<00:05, 92.45it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  79%|#######8  | 1974/2500 [00:20<00:05, 92.45it/s, loss=0.00812]
Texture baking (opt): optimizing:  79%|#######9  | 1976/2500 [00:20<00:05, 92.26it/s, loss=0.0107]t/s, loss=0.0107]
[Trellis Server] Texture baking (opt): optimizing:  79%|#######9  | 1976/2500 [00:20<00:05, 92.26it/s, loss=0.00919]
[Trellis Server] Texture baking (opt): optimizing:  79%|#######9  | 1977/2500 [00:20<00:05, 92.26it/s, loss=0.0153]
[Trellis Server] Texture baking (opt): optimizing:  79%|#######9  | 1978/2500 [00:20<00:05, 92.26it/s, loss=0.0118]
[Trellis Server] Texture baking (opt): optimizing:  79%|#######9  | 1979/2500 [00:20<00:05, 92.26it/s, loss=0.0173]
[Trellis Server] Texture baking (opt): optimizing:  79%|#######9  | 1980/2500 [00:20<00:05, 92.26it/s, loss=0.0135]
[Trellis Server] Texture baking (opt): optimizing:  79%|#######9  | 1981/2500 [00:20<00:05, 92.26it/s, loss=0.014]
[Trellis Server] Texture baking (opt): optimizing:  79%|#######9  | 1982/2500 [00:20<00:05, 92.26it/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  79%|#######9  | 1983/2500 [00:20<00:05, 92.26it/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  79%|#######9  | 1984/2500 [00:20<00:05, 92.26it/s, loss=0.0127]
Texture baking (opt): optimizing:  79%|#######9  | 1986/2500 [00:20<00:05, 90.92it/s, loss=0.00863]/s, loss=0.00863]
[Trellis Server] Texture baking (opt): optimizing:  79%|#######9  | 1986/2500 [00:20<00:05, 90.92it/s, loss=0.0158]
[Trellis Server] Texture baking (opt): optimizing:  79%|#######9  | 1987/2500 [00:20<00:05, 90.92it/s, loss=0.0116]
[Trellis Server] Texture baking (opt): optimizing:  80%|#######9  | 1988/2500 [00:20<00:05, 90.92it/s, loss=0.0124]
[Trellis Server] Texture baking (opt): optimizing:  80%|#######9  | 1989/2500 [00:20<00:05, 90.92it/s, loss=0.012]
[Trellis Server] Texture baking (opt): optimizing:  80%|#######9  | 1990/2500 [00:20<00:05, 90.92it/s, loss=0.00836]
[Trellis Server] Texture baking (opt): optimizing:  80%|#######9  | 1991/2500 [00:20<00:05, 90.92it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  80%|#######9  | 1992/2500 [00:20<00:05, 90.92it/s, loss=0.0199]
[Trellis Server] Texture baking (opt): optimizing:  80%|#######9  | 1993/2500 [00:20<00:05, 90.92it/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  80%|#######9  | 1994/2500 [00:20<00:05, 90.92it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  80%|#######9  | 1995/2500 [00:20<00:05, 90.92it/s, loss=0.0113]
Texture baking (opt): optimizing:  80%|#######9  | 1997/2500 [00:20<00:05, 93.90it/s, loss=0.00965]/s, loss=0.00965]
[Trellis Server] Texture baking (opt): optimizing:  80%|#######9  | 1997/2500 [00:21<00:05, 93.90it/s, loss=0.0124]
[Trellis Server] Texture baking (opt): optimizing:  80%|#######9  | 1998/2500 [00:21<00:05, 93.90it/s, loss=0.00984]
[Trellis Server] Texture baking (opt): optimizing:  80%|#######9  | 1999/2500 [00:21<00:05, 93.90it/s, loss=0.0131]
[Trellis Server] Texture baking (opt): optimizing:  80%|########  | 2000/2500 [00:21<00:05, 93.90it/s, loss=0.00534]
[Trellis Server] Texture baking (opt): optimizing:  80%|########  | 2001/2500 [00:21<00:05, 93.90it/s, loss=0.0104]
[Trellis Server] Texture baking (opt): optimizing:  80%|########  | 2002/2500 [00:21<00:05, 93.90it/s, loss=0.0177]
[Trellis Server] Texture baking (opt): optimizing:  80%|########  | 2003/2500 [00:21<00:05, 93.90it/s, loss=0.0143]
[Trellis Server] Texture baking (opt): optimizing:  80%|########  | 2004/2500 [00:21<00:05, 93.90it/s, loss=0.00476]
[Trellis Server] Texture baking (opt): optimizing:  80%|########  | 2005/2500 [00:21<00:05, 93.90it/s, loss=0.00927]
Texture baking (opt): optimizing:  80%|########  | 2007/2500 [00:21<00:05, 94.55it/s, loss=0.00855]/s, loss=0.00855]
[Trellis Server] Texture baking (opt): optimizing:  80%|########  | 2007/2500 [00:21<00:05, 94.55it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  80%|########  | 2008/2500 [00:21<00:05, 94.55it/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  80%|########  | 2009/2500 [00:21<00:05, 94.55it/s, loss=0.0163]
[Trellis Server] Texture baking (opt): optimizing:  80%|########  | 2010/2500 [00:21<00:05, 94.55it/s, loss=0.00991]
[Trellis Server] Texture baking (opt): optimizing:  80%|########  | 2011/2500 [00:21<00:05, 94.55it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  80%|########  | 2012/2500 [00:21<00:05, 94.55it/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:  81%|########  | 2013/2500 [00:21<00:05, 94.55it/s, loss=0.0143]
[Trellis Server] Texture baking (opt): optimizing:  81%|########  | 2014/2500 [00:21<00:05, 94.55it/s, loss=0.0105]
[Trellis Server] Texture baking (opt): optimizing:  81%|########  | 2015/2500 [00:21<00:05, 94.55it/s, loss=0.0129]
Texture baking (opt): optimizing:  81%|########  | 2017/2500 [00:21<00:05, 95.54it/s, loss=0.0133]t/s, loss=0.0133]
[Trellis Server] Texture baking (opt): optimizing:  81%|########  | 2017/2500 [00:21<00:05, 95.54it/s, loss=0.00825]
[Trellis Server] Texture baking (opt): optimizing:  81%|########  | 2018/2500 [00:21<00:05, 95.54it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  81%|########  | 2019/2500 [00:21<00:05, 95.54it/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:  81%|########  | 2020/2500 [00:21<00:05, 95.54it/s, loss=0.00679]
[Trellis Server] Texture baking (opt): optimizing:  81%|########  | 2021/2500 [00:21<00:05, 95.54it/s, loss=0.0105]
[Trellis Server] Texture baking (opt): optimizing:  81%|########  | 2022/2500 [00:21<00:05, 95.54it/s, loss=0.00941]
[Trellis Server] Texture baking (opt): optimizing:  81%|########  | 2023/2500 [00:21<00:04, 95.54it/s, loss=0.0179]
[Trellis Server] Texture baking (opt): optimizing:  81%|########  | 2024/2500 [00:21<00:04, 95.54it/s, loss=0.0101]
[Trellis Server] Texture baking (opt): optimizing:  81%|########1 | 2025/2500 [00:21<00:04, 95.54it/s, loss=0.0162]
Texture baking (opt): optimizing:  81%|########1 | 2027/2500 [00:21<00:04, 95.46it/s, loss=0.0157]t/s, loss=0.0157]
[Trellis Server] Texture baking (opt): optimizing:  81%|########1 | 2027/2500 [00:21<00:04, 95.46it/s, loss=0.0107]
[Trellis Server] Texture baking (opt): optimizing:  81%|########1 | 2028/2500 [00:21<00:04, 95.46it/s, loss=0.00805]
[Trellis Server] Texture baking (opt): optimizing:  81%|########1 | 2029/2500 [00:21<00:04, 95.46it/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:  81%|########1 | 2030/2500 [00:21<00:04, 95.46it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  81%|########1 | 2031/2500 [00:21<00:04, 95.46it/s, loss=0.0123]
[Trellis Server] Texture baking (opt): optimizing:  81%|########1 | 2032/2500 [00:21<00:04, 95.46it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  81%|########1 | 2033/2500 [00:21<00:04, 95.46it/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  81%|########1 | 2034/2500 [00:21<00:04, 95.46it/s, loss=0.0118]
[Trellis Server] Texture baking (opt): optimizing:  81%|########1 | 2035/2500 [00:21<00:04, 95.46it/s, loss=0.013]
Texture baking (opt): optimizing:  81%|########1 | 2037/2500 [00:21<00:04, 95.39it/s, loss=0.0162]t/s, loss=0.0162]
[Trellis Server] Texture baking (opt): optimizing:  81%|########1 | 2037/2500 [00:21<00:04, 95.39it/s, loss=0.0159]
[Trellis Server] Texture baking (opt): optimizing:  82%|########1 | 2038/2500 [00:21<00:04, 95.39it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  82%|########1 | 2039/2500 [00:21<00:04, 95.39it/s, loss=0.0186]
[Trellis Server] Texture baking (opt): optimizing:  82%|########1 | 2040/2500 [00:21<00:04, 95.39it/s, loss=0.014]
[Trellis Server] Texture baking (opt): optimizing:  82%|########1 | 2041/2500 [00:21<00:04, 95.39it/s, loss=0.014]
[Trellis Server] Texture baking (opt): optimizing:  82%|########1 | 2042/2500 [00:21<00:04, 95.39it/s, loss=0.0133]
[Trellis Server] Texture baking (opt): optimizing:  82%|########1 | 2043/2500 [00:21<00:04, 95.39it/s, loss=0.01]
[Trellis Server] Texture baking (opt): optimizing:  82%|########1 | 2044/2500 [00:21<00:04, 95.39it/s, loss=0.00863]
[Trellis Server] Texture baking (opt): optimizing:  82%|########1 | 2045/2500 [00:21<00:04, 95.39it/s, loss=0.0132]
Texture baking (opt): optimizing:  82%|########1 | 2047/2500 [00:21<00:04, 96.45it/s, loss=0.0128]t/s, loss=0.0128]
[Trellis Server] Texture baking (opt): optimizing:  82%|########1 | 2047/2500 [00:21<00:04, 96.45it/s, loss=0.0121]
[Trellis Server] Texture baking (opt): optimizing:  82%|########1 | 2048/2500 [00:21<00:04, 96.45it/s, loss=0.0154]
[Trellis Server] Texture baking (opt): optimizing:  82%|########1 | 2049/2500 [00:21<00:04, 96.45it/s, loss=0.00977]
[Trellis Server] Texture baking (opt): optimizing:  82%|########2 | 2050/2500 [00:21<00:04, 96.45it/s, loss=0.0102]
[Trellis Server] Texture baking (opt): optimizing:  82%|########2 | 2051/2500 [00:21<00:04, 96.45it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  82%|########2 | 2052/2500 [00:21<00:04, 96.45it/s, loss=0.0121]
[Trellis Server] Texture baking (opt): optimizing:  82%|########2 | 2053/2500 [00:21<00:04, 96.45it/s, loss=0.0103]
[Trellis Server] Texture baking (opt): optimizing:  82%|########2 | 2054/2500 [00:21<00:04, 96.45it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  82%|########2 | 2055/2500 [00:21<00:04, 96.45it/s, loss=0.00549]
Texture baking (opt): optimizing:  82%|########2 | 2057/2500 [00:21<00:04, 94.71it/s, loss=0.0107]t/s, loss=0.0107]
[Trellis Server] Texture baking (opt): optimizing:  82%|########2 | 2057/2500 [00:21<00:04, 94.71it/s, loss=0.0116]
[Trellis Server] Texture baking (opt): optimizing:  82%|########2 | 2058/2500 [00:21<00:04, 94.71it/s, loss=0.014]
[Trellis Server] Texture baking (opt): optimizing:  82%|########2 | 2059/2500 [00:21<00:04, 94.71it/s, loss=0.00879]
[Trellis Server] Texture baking (opt): optimizing:  82%|########2 | 2060/2500 [00:21<00:04, 94.71it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  82%|########2 | 2061/2500 [00:21<00:04, 94.71it/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:  82%|########2 | 2062/2500 [00:21<00:04, 94.71it/s, loss=0.0129]
[Trellis Server] Texture baking (opt): optimizing:  83%|########2 | 2063/2500 [00:21<00:04, 94.71it/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  83%|########2 | 2064/2500 [00:21<00:04, 94.71it/s, loss=0.00752]
[Trellis Server] Texture baking (opt): optimizing:  83%|########2 | 2065/2500 [00:21<00:04, 94.71it/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing:  83%|########2 | 2066/2500 [00:21<00:04, 94.71it/s, loss=0.0101]
Texture baking (opt): optimizing:  83%|########2 | 2068/2500 [00:21<00:04, 96.89it/s, loss=0.0163]t/s, loss=0.0163]
[Trellis Server] Texture baking (opt): optimizing:  83%|########2 | 2068/2500 [00:21<00:04, 96.89it/s, loss=0.00756]
[Trellis Server] Texture baking (opt): optimizing:  83%|########2 | 2069/2500 [00:21<00:04, 96.89it/s, loss=0.0105]
[Trellis Server] Texture baking (opt): optimizing:  83%|########2 | 2070/2500 [00:21<00:04, 96.89it/s, loss=0.0101]
[Trellis Server] Texture baking (opt): optimizing:  83%|########2 | 2071/2500 [00:21<00:04, 96.89it/s, loss=0.014]
[Trellis Server] Texture baking (opt): optimizing:  83%|########2 | 2072/2500 [00:21<00:04, 96.89it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  83%|########2 | 2073/2500 [00:21<00:04, 96.89it/s, loss=0.0124]
[Trellis Server] Texture baking (opt): optimizing:  83%|########2 | 2074/2500 [00:21<00:04, 96.89it/s, loss=0.00821]
[Trellis Server] Texture baking (opt): optimizing:  83%|########2 | 2075/2500 [00:21<00:04, 96.89it/s, loss=0.0191]
[Trellis Server] Texture baking (opt): optimizing:  83%|########3 | 2076/2500 [00:21<00:04, 96.89it/s, loss=0.0125]
Texture baking (opt): optimizing:  83%|########3 | 2078/2500 [00:21<00:04, 97.22it/s, loss=0.0104]t/s, loss=0.0104]
[Trellis Server] Texture baking (opt): optimizing:  83%|########3 | 2078/2500 [00:21<00:04, 97.22it/s, loss=0.0125]
[Trellis Server] Texture baking (opt): optimizing:  83%|########3 | 2079/2500 [00:21<00:04, 97.22it/s, loss=0.0122]
[Trellis Server] Texture baking (opt): optimizing:  83%|########3 | 2080/2500 [00:21<00:04, 97.22it/s, loss=0.00956]
[Trellis Server] Texture baking (opt): optimizing:  83%|########3 | 2081/2500 [00:21<00:04, 97.22it/s, loss=0.0128]
[Trellis Server] Texture baking (opt): optimizing:  83%|########3 | 2082/2500 [00:21<00:04, 97.22it/s, loss=0.00672]
[Trellis Server] Texture baking (opt): optimizing:  83%|########3 | 2083/2500 [00:21<00:04, 97.22it/s, loss=0.0133]
[Trellis Server] Texture baking (opt): optimizing:  83%|########3 | 2084/2500 [00:21<00:04, 97.22it/s, loss=0.01]
[Trellis Server] Texture baking (opt): optimizing:  83%|########3 | 2085/2500 [00:21<00:04, 97.22it/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  83%|########3 | 2086/2500 [00:21<00:04, 97.22it/s, loss=0.0155]
[Trellis Server] Texture baking (opt): optimizing:  83%|########3 | 2087/2500 [00:21<00:04, 97.22it/s, loss=0.0139]
Texture baking (opt): optimizing:  84%|########3 | 2089/2500 [00:21<00:04, 98.36it/s, loss=0.0173]t/s, loss=0.0173]
[Trellis Server] Texture baking (opt): optimizing:  84%|########3 | 2089/2500 [00:21<00:04, 98.36it/s, loss=0.0194]
[Trellis Server] Texture baking (opt): optimizing:  84%|########3 | 2090/2500 [00:21<00:04, 98.36it/s, loss=0.018]
[Trellis Server] Texture baking (opt): optimizing:  84%|########3 | 2091/2500 [00:21<00:04, 98.36it/s, loss=0.00985]
[Trellis Server] Texture baking (opt): optimizing:  84%|########3 | 2092/2500 [00:21<00:04, 98.36it/s, loss=0.0128]
[Trellis Server] Texture baking (opt): optimizing:  84%|########3 | 2093/2500 [00:21<00:04, 98.36it/s, loss=0.00568]
[Trellis Server] Texture baking (opt): optimizing:  84%|########3 | 2094/2500 [00:22<00:04, 98.36it/s, loss=0.0104]
[Trellis Server] Texture baking (opt): optimizing:  84%|########3 | 2095/2500 [00:22<00:04, 98.36it/s, loss=0.0124]
[Trellis Server] Texture baking (opt): optimizing:  84%|########3 | 2096/2500 [00:22<00:04, 98.36it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  84%|########3 | 2097/2500 [00:22<00:04, 98.36it/s, loss=0.0129]
Texture baking (opt): optimizing:  84%|########3 | 2099/2500 [00:22<00:04, 97.71it/s, loss=0.00861]/s, loss=0.00861]
[Trellis Server] Texture baking (opt): optimizing:  84%|########3 | 2099/2500 [00:22<00:04, 97.71it/s, loss=0.0163]
[Trellis Server] Texture baking (opt): optimizing:  84%|########4 | 2100/2500 [00:22<00:04, 97.71it/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:  84%|########4 | 2101/2500 [00:22<00:04, 97.71it/s, loss=0.00609]
[Trellis Server] Texture baking (opt): optimizing:  84%|########4 | 2102/2500 [00:22<00:04, 97.71it/s, loss=0.00972]
[Trellis Server] Texture baking (opt): optimizing:  84%|########4 | 2103/2500 [00:22<00:04, 97.71it/s, loss=0.00619]
[Trellis Server] Texture baking (opt): optimizing:  84%|########4 | 2104/2500 [00:22<00:04, 97.71it/s, loss=0.00805]
[Trellis Server] Texture baking (opt): optimizing:  84%|########4 | 2105/2500 [00:22<00:04, 97.71it/s, loss=0.0124]
[Trellis Server] Texture baking (opt): optimizing:  84%|########4 | 2106/2500 [00:22<00:04, 97.71it/s, loss=0.00941]
[Trellis Server] Texture baking (opt): optimizing:  84%|########4 | 2107/2500 [00:22<00:04, 97.71it/s, loss=0.0142]
[Trellis Server] Texture baking (opt): optimizing:  84%|########4 | 2108/2500 [00:22<00:04, 97.71it/s, loss=0.0144]
Texture baking (opt): optimizing:  84%|########4 | 2110/2500 [00:22<00:03, 98.69it/s, loss=0.0105]t/s, loss=0.0105]
[Trellis Server] Texture baking (opt): optimizing:  84%|########4 | 2110/2500 [00:22<00:03, 98.69it/s, loss=0.00888]
[Trellis Server] Texture baking (opt): optimizing:  84%|########4 | 2111/2500 [00:22<00:03, 98.69it/s, loss=0.0141]
[Trellis Server] Texture baking (opt): optimizing:  84%|########4 | 2112/2500 [00:22<00:03, 98.69it/s, loss=0.00771]
[Trellis Server] Texture baking (opt): optimizing:  85%|########4 | 2113/2500 [00:22<00:03, 98.69it/s, loss=0.00748]
[Trellis Server] Texture baking (opt): optimizing:  85%|########4 | 2114/2500 [00:22<00:03, 98.69it/s, loss=0.00969]
[Trellis Server] Texture baking (opt): optimizing:  85%|########4 | 2115/2500 [00:22<00:03, 98.69it/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  85%|########4 | 2116/2500 [00:22<00:03, 98.69it/s, loss=0.0108]
[Trellis Server] Texture baking (opt): optimizing:  85%|########4 | 2117/2500 [00:22<00:03, 98.69it/s, loss=0.0104]
[Trellis Server] Texture baking (opt): optimizing:  85%|########4 | 2118/2500 [00:22<00:03, 98.69it/s, loss=0.0115]
Texture baking (opt): optimizing:  85%|########4 | 2120/2500 [00:22<00:03, 99.07it/s, loss=0.0123]t/s, loss=0.0123]
[Trellis Server] Texture baking (opt): optimizing:  85%|########4 | 2120/2500 [00:22<00:03, 99.07it/s, loss=0.0123]
[Trellis Server] Texture baking (opt): optimizing:  85%|########4 | 2121/2500 [00:22<00:03, 99.07it/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  85%|########4 | 2122/2500 [00:22<00:03, 99.07it/s, loss=0.0157]
[Trellis Server] Texture baking (opt): optimizing:  85%|########4 | 2123/2500 [00:22<00:03, 99.07it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  85%|########4 | 2124/2500 [00:22<00:03, 99.07it/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:  85%|########5 | 2125/2500 [00:22<00:03, 99.07it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  85%|########5 | 2126/2500 [00:22<00:03, 99.07it/s, loss=0.00965]
[Trellis Server] Texture baking (opt): optimizing:  85%|########5 | 2127/2500 [00:22<00:03, 99.07it/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  85%|########5 | 2128/2500 [00:22<00:03, 99.07it/s, loss=0.011]
Texture baking (opt): optimizing:  85%|########5 | 2130/2500 [00:22<00:03, 99.05it/s, loss=0.0124]t/s, loss=0.0124]
[Trellis Server] Texture baking (opt): optimizing:  85%|########5 | 2130/2500 [00:22<00:03, 99.05it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  85%|########5 | 2131/2500 [00:22<00:03, 99.05it/s, loss=0.00913]
[Trellis Server] Texture baking (opt): optimizing:  85%|########5 | 2132/2500 [00:22<00:03, 99.05it/s, loss=0.00557]
[Trellis Server] Texture baking (opt): optimizing:  85%|########5 | 2133/2500 [00:22<00:03, 99.05it/s, loss=0.00954]
[Trellis Server] Texture baking (opt): optimizing:  85%|########5 | 2134/2500 [00:22<00:03, 99.05it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  85%|########5 | 2135/2500 [00:22<00:03, 99.05it/s, loss=0.00835]
[Trellis Server] Texture baking (opt): optimizing:  85%|########5 | 2136/2500 [00:22<00:03, 99.05it/s, loss=0.0189]
[Trellis Server] Texture baking (opt): optimizing:  85%|########5 | 2137/2500 [00:22<00:03, 99.05it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  86%|########5 | 2138/2500 [00:22<00:03, 99.05it/s, loss=0.00983]
Texture baking (opt): optimizing:  86%|########5 | 2140/2500 [00:22<00:03, 98.46it/s, loss=0.0106]t/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing:  86%|########5 | 2140/2500 [00:22<00:03, 98.46it/s, loss=0.00935]
[Trellis Server] Texture baking (opt): optimizing:  86%|########5 | 2141/2500 [00:22<00:03, 98.46it/s, loss=0.0148]
[Trellis Server] Texture baking (opt): optimizing:  86%|########5 | 2142/2500 [00:22<00:03, 98.46it/s, loss=0.00618]
[Trellis Server] Texture baking (opt): optimizing:  86%|########5 | 2143/2500 [00:22<00:03, 98.46it/s, loss=0.0142]
[Trellis Server] Texture baking (opt): optimizing:  86%|########5 | 2144/2500 [00:22<00:03, 98.46it/s, loss=0.0093]
[Trellis Server] Texture baking (opt): optimizing:  86%|########5 | 2145/2500 [00:22<00:03, 98.46it/s, loss=0.0107]
[Trellis Server] Texture baking (opt): optimizing:  86%|########5 | 2146/2500 [00:22<00:03, 98.46it/s, loss=0.0107]
[Trellis Server] Texture baking (opt): optimizing:  86%|########5 | 2147/2500 [00:22<00:03, 98.46it/s, loss=0.014]
[Trellis Server] Texture baking (opt): optimizing:  86%|########5 | 2148/2500 [00:22<00:03, 98.46it/s, loss=0.0113]
Texture baking (opt): optimizing:  86%|########6 | 2150/2500 [00:22<00:03, 96.93it/s, loss=0.0109]t/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  86%|########6 | 2150/2500 [00:22<00:03, 96.93it/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  86%|########6 | 2151/2500 [00:22<00:03, 96.93it/s, loss=0.00959]
[Trellis Server] Texture baking (opt): optimizing:  86%|########6 | 2152/2500 [00:22<00:03, 96.93it/s, loss=0.0121]
[Trellis Server] Texture baking (opt): optimizing:  86%|########6 | 2153/2500 [00:22<00:03, 96.93it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  86%|########6 | 2154/2500 [00:22<00:03, 96.93it/s, loss=0.00793]
[Trellis Server] Texture baking (opt): optimizing:  86%|########6 | 2155/2500 [00:22<00:03, 96.93it/s, loss=0.00723]
[Trellis Server] Texture baking (opt): optimizing:  86%|########6 | 2156/2500 [00:22<00:03, 96.93it/s, loss=0.0166]
[Trellis Server] Texture baking (opt): optimizing:  86%|########6 | 2157/2500 [00:22<00:03, 96.93it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  86%|########6 | 2158/2500 [00:22<00:03, 96.93it/s, loss=0.0137]
Texture baking (opt): optimizing:  86%|########6 | 2160/2500 [00:22<00:03, 96.69it/s, loss=0.0123]t/s, loss=0.0123]
[Trellis Server] Texture baking (opt): optimizing:  86%|########6 | 2160/2500 [00:22<00:03, 96.69it/s, loss=0.00926]
[Trellis Server] Texture baking (opt): optimizing:  86%|########6 | 2161/2500 [00:22<00:03, 96.69it/s, loss=0.0101]
[Trellis Server] Texture baking (opt): optimizing:  86%|########6 | 2162/2500 [00:22<00:03, 96.69it/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  87%|########6 | 2163/2500 [00:22<00:03, 96.69it/s, loss=0.0103]
[Trellis Server] Texture baking (opt): optimizing:  87%|########6 | 2164/2500 [00:22<00:03, 96.69it/s, loss=0.0198]
[Trellis Server] Texture baking (opt): optimizing:  87%|########6 | 2165/2500 [00:22<00:03, 96.69it/s, loss=0.00728]
[Trellis Server] Texture baking (opt): optimizing:  87%|########6 | 2166/2500 [00:22<00:03, 96.69it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  87%|########6 | 2167/2500 [00:22<00:03, 96.69it/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing:  87%|########6 | 2168/2500 [00:22<00:03, 96.69it/s, loss=0.0114]
Texture baking (opt): optimizing:  87%|########6 | 2170/2500 [00:22<00:03, 95.16it/s, loss=0.00989]/s, loss=0.00989]
[Trellis Server] Texture baking (opt): optimizing:  87%|########6 | 2170/2500 [00:22<00:03, 95.16it/s, loss=0.0133]
[Trellis Server] Texture baking (opt): optimizing:  87%|########6 | 2171/2500 [00:22<00:03, 95.16it/s, loss=0.0144]
[Trellis Server] Texture baking (opt): optimizing:  87%|########6 | 2172/2500 [00:22<00:03, 95.16it/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  87%|########6 | 2173/2500 [00:22<00:03, 95.16it/s, loss=0.0181]
[Trellis Server] Texture baking (opt): optimizing:  87%|########6 | 2174/2500 [00:22<00:03, 95.16it/s, loss=0.0107]
[Trellis Server] Texture baking (opt): optimizing:  87%|########7 | 2175/2500 [00:22<00:03, 95.16it/s, loss=0.00459]
[Trellis Server] Texture baking (opt): optimizing:  87%|########7 | 2176/2500 [00:22<00:03, 95.16it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  87%|########7 | 2177/2500 [00:22<00:03, 95.16it/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:  87%|########7 | 2178/2500 [00:22<00:03, 95.16it/s, loss=0.0125]
Texture baking (opt): optimizing:  87%|########7 | 2180/2500 [00:22<00:03, 94.88it/s, loss=0.00957]/s, loss=0.00957]
[Trellis Server] Texture baking (opt): optimizing:  87%|########7 | 2180/2500 [00:22<00:03, 94.88it/s, loss=0.0121]
[Trellis Server] Texture baking (opt): optimizing:  87%|########7 | 2181/2500 [00:22<00:03, 94.88it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  87%|########7 | 2182/2500 [00:22<00:03, 94.88it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  87%|########7 | 2183/2500 [00:22<00:03, 94.88it/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  87%|########7 | 2184/2500 [00:22<00:03, 94.88it/s, loss=0.0125]
[Trellis Server] Texture baking (opt): optimizing:  87%|########7 | 2185/2500 [00:22<00:03, 94.88it/s, loss=0.0103]
[Trellis Server] Texture baking (opt): optimizing:  87%|########7 | 2186/2500 [00:22<00:03, 94.88it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  87%|########7 | 2187/2500 [00:22<00:03, 94.88it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  88%|########7 | 2188/2500 [00:22<00:03, 94.88it/s, loss=0.0119]
Texture baking (opt): optimizing:  88%|########7 | 2190/2500 [00:23<00:03, 92.61it/s, loss=0.0103]t/s, loss=0.0103]
[Trellis Server] Texture baking (opt): optimizing:  88%|########7 | 2190/2500 [00:23<00:03, 92.61it/s, loss=0.00651]
[Trellis Server] Texture baking (opt): optimizing:  88%|########7 | 2191/2500 [00:23<00:03, 92.61it/s, loss=0.014]
[Trellis Server] Texture baking (opt): optimizing:  88%|########7 | 2192/2500 [00:23<00:03, 92.61it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  88%|########7 | 2193/2500 [00:23<00:03, 92.61it/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing:  88%|########7 | 2194/2500 [00:23<00:03, 92.61it/s, loss=0.0163]
[Trellis Server] Texture baking (opt): optimizing:  88%|########7 | 2195/2500 [00:23<00:03, 92.61it/s, loss=0.00951]
[Trellis Server] Texture baking (opt): optimizing:  88%|########7 | 2196/2500 [00:23<00:03, 92.61it/s, loss=0.00993]
[Trellis Server] Texture baking (opt): optimizing:  88%|########7 | 2197/2500 [00:23<00:03, 92.61it/s, loss=0.0066]
[Trellis Server] Texture baking (opt): optimizing:  88%|########7 | 2198/2500 [00:23<00:03, 92.61it/s, loss=0.0177]
Texture baking (opt): optimizing:  88%|########8 | 2200/2500 [00:23<00:03, 91.63it/s, loss=0.00608]/s, loss=0.00608]
[Trellis Server] Texture baking (opt): optimizing:  88%|########8 | 2200/2500 [00:23<00:03, 91.63it/s, loss=0.0072]
[Trellis Server] Texture baking (opt): optimizing:  88%|########8 | 2201/2500 [00:23<00:03, 91.63it/s, loss=0.012]
[Trellis Server] Texture baking (opt): optimizing:  88%|########8 | 2202/2500 [00:23<00:03, 91.63it/s, loss=0.00912]
[Trellis Server] Texture baking (opt): optimizing:  88%|########8 | 2203/2500 [00:23<00:03, 91.63it/s, loss=0.0121]
[Trellis Server] Texture baking (opt): optimizing:  88%|########8 | 2204/2500 [00:23<00:03, 91.63it/s, loss=0.016]
[Trellis Server] Texture baking (opt): optimizing:  88%|########8 | 2205/2500 [00:23<00:03, 91.63it/s, loss=0.0121]
[Trellis Server] Texture baking (opt): optimizing:  88%|########8 | 2206/2500 [00:23<00:03, 91.63it/s, loss=0.00982]
[Trellis Server] Texture baking (opt): optimizing:  88%|########8 | 2207/2500 [00:23<00:03, 91.63it/s, loss=0.0108]
[Trellis Server] Texture baking (opt): optimizing:  88%|########8 | 2208/2500 [00:23<00:03, 91.63it/s, loss=0.0169]
Texture baking (opt): optimizing:  88%|########8 | 2210/2500 [00:23<00:03, 93.19it/s, loss=0.0111]t/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  88%|########8 | 2210/2500 [00:23<00:03, 93.19it/s, loss=0.0123]
[Trellis Server] Texture baking (opt): optimizing:  88%|########8 | 2211/2500 [00:23<00:03, 93.19it/s, loss=0.0087]
[Trellis Server] Texture baking (opt): optimizing:  88%|########8 | 2212/2500 [00:23<00:03, 93.19it/s, loss=0.00961]
[Trellis Server] Texture baking (opt): optimizing:  89%|########8 | 2213/2500 [00:23<00:03, 93.19it/s, loss=0.0172]
[Trellis Server] Texture baking (opt): optimizing:  89%|########8 | 2214/2500 [00:23<00:03, 93.19it/s, loss=0.01]
[Trellis Server] Texture baking (opt): optimizing:  89%|########8 | 2215/2500 [00:23<00:03, 93.19it/s, loss=0.0121]
[Trellis Server] Texture baking (opt): optimizing:  89%|########8 | 2216/2500 [00:23<00:03, 93.19it/s, loss=0.0108]
[Trellis Server] Texture baking (opt): optimizing:  89%|########8 | 2217/2500 [00:23<00:03, 93.19it/s, loss=0.00874]
[Trellis Server] Texture baking (opt): optimizing:  89%|########8 | 2218/2500 [00:23<00:03, 93.19it/s, loss=0.0122]
Texture baking (opt): optimizing:  89%|########8 | 2220/2500 [00:23<00:03, 91.12it/s, loss=0.0117]t/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  89%|########8 | 2220/2500 [00:23<00:03, 91.12it/s, loss=0.01]
[Trellis Server] Texture baking (opt): optimizing:  89%|########8 | 2221/2500 [00:23<00:03, 91.12it/s, loss=0.0116]
[Trellis Server] Texture baking (opt): optimizing:  89%|########8 | 2222/2500 [00:23<00:03, 91.12it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  89%|########8 | 2223/2500 [00:23<00:03, 91.12it/s, loss=0.0142]
[Trellis Server] Texture baking (opt): optimizing:  89%|########8 | 2224/2500 [00:23<00:03, 91.12it/s, loss=0.0151]
[Trellis Server] Texture baking (opt): optimizing:  89%|########9 | 2225/2500 [00:23<00:03, 91.12it/s, loss=0.018]
[Trellis Server] Texture baking (opt): optimizing:  89%|########9 | 2226/2500 [00:23<00:03, 91.12it/s, loss=0.00887]
[Trellis Server] Texture baking (opt): optimizing:  89%|########9 | 2227/2500 [00:23<00:02, 91.12it/s, loss=0.00971]
[Trellis Server] Texture baking (opt): optimizing:  89%|########9 | 2228/2500 [00:23<00:02, 91.12it/s, loss=0.0116]
Texture baking (opt): optimizing:  89%|########9 | 2230/2500 [00:23<00:02, 93.35it/s, loss=0.013]it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  89%|########9 | 2230/2500 [00:23<00:02, 93.35it/s, loss=0.00829]
[Trellis Server] Texture baking (opt): optimizing:  89%|########9 | 2231/2500 [00:23<00:02, 93.35it/s, loss=0.0125]
[Trellis Server] Texture baking (opt): optimizing:  89%|########9 | 2232/2500 [00:23<00:02, 93.35it/s, loss=0.0184]
[Trellis Server] Texture baking (opt): optimizing:  89%|########9 | 2233/2500 [00:23<00:02, 93.35it/s, loss=0.0104]
[Trellis Server] Texture baking (opt): optimizing:  89%|########9 | 2234/2500 [00:23<00:02, 93.35it/s, loss=0.0142]
[Trellis Server] Texture baking (opt): optimizing:  89%|########9 | 2235/2500 [00:23<00:02, 93.35it/s, loss=0.0158]
[Trellis Server] Texture baking (opt): optimizing:  89%|########9 | 2236/2500 [00:23<00:02, 93.35it/s, loss=0.0155]
[Trellis Server] Texture baking (opt): optimizing:  89%|########9 | 2237/2500 [00:23<00:02, 93.35it/s, loss=0.0134]
[Trellis Server] Texture baking (opt): optimizing:  90%|########9 | 2238/2500 [00:23<00:02, 93.35it/s, loss=0.0108]
Texture baking (opt): optimizing:  90%|########9 | 2240/2500 [00:23<00:02, 92.09it/s, loss=0.0111]t/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  90%|########9 | 2240/2500 [00:23<00:02, 92.09it/s, loss=0.0107]
[Trellis Server] Texture baking (opt): optimizing:  90%|########9 | 2241/2500 [00:23<00:02, 92.09it/s, loss=0.00817]
[Trellis Server] Texture baking (opt): optimizing:  90%|########9 | 2242/2500 [00:23<00:02, 92.09it/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  90%|########9 | 2243/2500 [00:23<00:02, 92.09it/s, loss=0.0173]
[Trellis Server] Texture baking (opt): optimizing:  90%|########9 | 2244/2500 [00:23<00:02, 92.09it/s, loss=0.00933]
[Trellis Server] Texture baking (opt): optimizing:  90%|########9 | 2245/2500 [00:23<00:02, 92.09it/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:  90%|########9 | 2246/2500 [00:23<00:02, 92.09it/s, loss=0.00647]
[Trellis Server] Texture baking (opt): optimizing:  90%|########9 | 2247/2500 [00:23<00:02, 92.09it/s, loss=0.00819]
[Trellis Server] Texture baking (opt): optimizing:  90%|########9 | 2248/2500 [00:23<00:02, 92.09it/s, loss=0.0124]
[Trellis Server] Texture baking (opt): optimizing:  90%|########9 | 2249/2500 [00:23<00:02, 92.09it/s, loss=0.0173]
Texture baking (opt): optimizing:  90%|######### | 2251/2500 [00:23<00:02, 94.23it/s, loss=0.0102]t/s, loss=0.0102]
[Trellis Server] Texture baking (opt): optimizing:  90%|######### | 2251/2500 [00:23<00:02, 94.23it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  90%|######### | 2252/2500 [00:23<00:02, 94.23it/s, loss=0.00934]
[Trellis Server] Texture baking (opt): optimizing:  90%|######### | 2253/2500 [00:23<00:02, 94.23it/s, loss=0.0136]
[Trellis Server] Texture baking (opt): optimizing:  90%|######### | 2254/2500 [00:23<00:02, 94.23it/s, loss=0.00658]
[Trellis Server] Texture baking (opt): optimizing:  90%|######### | 2255/2500 [00:23<00:02, 94.23it/s, loss=0.00622]
[Trellis Server] Texture baking (opt): optimizing:  90%|######### | 2256/2500 [00:23<00:02, 94.23it/s, loss=0.0163]
[Trellis Server] Texture baking (opt): optimizing:  90%|######### | 2257/2500 [00:23<00:02, 94.23it/s, loss=0.0162]
[Trellis Server] Texture baking (opt): optimizing:  90%|######### | 2258/2500 [00:23<00:02, 94.23it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  90%|######### | 2259/2500 [00:23<00:02, 94.23it/s, loss=0.0142]
Texture baking (opt): optimizing:  90%|######### | 2261/2500 [00:23<00:02, 95.05it/s, loss=0.0133]t/s, loss=0.0133]
[Trellis Server] Texture baking (opt): optimizing:  90%|######### | 2261/2500 [00:23<00:02, 95.05it/s, loss=0.00744]
[Trellis Server] Texture baking (opt): optimizing:  90%|######### | 2262/2500 [00:23<00:02, 95.05it/s, loss=0.0124]
[Trellis Server] Texture baking (opt): optimizing:  91%|######### | 2263/2500 [00:23<00:02, 95.05it/s, loss=0.0124]
[Trellis Server] Texture baking (opt): optimizing:  91%|######### | 2264/2500 [00:23<00:02, 95.05it/s, loss=0.0118]
[Trellis Server] Texture baking (opt): optimizing:  91%|######### | 2265/2500 [00:23<00:02, 95.05it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  91%|######### | 2266/2500 [00:23<00:02, 95.05it/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  91%|######### | 2267/2500 [00:23<00:02, 95.05it/s, loss=0.00716]
[Trellis Server] Texture baking (opt): optimizing:  91%|######### | 2268/2500 [00:23<00:02, 95.05it/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  91%|######### | 2269/2500 [00:23<00:02, 95.05it/s, loss=0.0116]
Texture baking (opt): optimizing:  91%|######### | 2271/2500 [00:23<00:02, 96.46it/s, loss=0.0112]t/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  91%|######### | 2271/2500 [00:23<00:02, 96.46it/s, loss=0.0146]
[Trellis Server] Texture baking (opt): optimizing:  91%|######### | 2272/2500 [00:23<00:02, 96.46it/s, loss=0.00946]
[Trellis Server] Texture baking (opt): optimizing:  91%|######### | 2273/2500 [00:23<00:02, 96.46it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  91%|######### | 2274/2500 [00:23<00:02, 96.46it/s, loss=0.0104]
[Trellis Server] Texture baking (opt): optimizing:  91%|#########1| 2275/2500 [00:23<00:02, 96.46it/s, loss=0.00948]
[Trellis Server] Texture baking (opt): optimizing:  91%|#########1| 2276/2500 [00:23<00:02, 96.46it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  91%|#########1| 2277/2500 [00:23<00:02, 96.46it/s, loss=0.014]
[Trellis Server] Texture baking (opt): optimizing:  91%|#########1| 2278/2500 [00:23<00:02, 96.46it/s, loss=0.00563]
[Trellis Server] Texture baking (opt): optimizing:  91%|#########1| 2279/2500 [00:23<00:02, 96.46it/s, loss=0.0125]
Texture baking (opt): optimizing:  91%|#########1| 2281/2500 [00:23<00:02, 96.65it/s, loss=0.0122]t/s, loss=0.0122]
[Trellis Server] Texture baking (opt): optimizing:  91%|#########1| 2281/2500 [00:23<00:02, 96.65it/s, loss=0.00821]
[Trellis Server] Texture baking (opt): optimizing:  91%|#########1| 2282/2500 [00:23<00:02, 96.65it/s, loss=0.0182]
[Trellis Server] Texture baking (opt): optimizing:  91%|#########1| 2283/2500 [00:23<00:02, 96.65it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  91%|#########1| 2284/2500 [00:24<00:02, 96.65it/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  91%|#########1| 2285/2500 [00:24<00:02, 96.65it/s, loss=0.0105]
[Trellis Server] Texture baking (opt): optimizing:  91%|#########1| 2286/2500 [00:24<00:02, 96.65it/s, loss=0.012]
[Trellis Server] Texture baking (opt): optimizing:  91%|#########1| 2287/2500 [00:24<00:02, 96.65it/s, loss=0.00915]
[Trellis Server] Texture baking (opt): optimizing:  92%|#########1| 2288/2500 [00:24<00:02, 96.65it/s, loss=0.0123]
[Trellis Server] Texture baking (opt): optimizing:  92%|#########1| 2289/2500 [00:24<00:02, 96.65it/s, loss=0.0111]
Texture baking (opt): optimizing:  92%|#########1| 2291/2500 [00:24<00:02, 97.06it/s, loss=0.0123]t/s, loss=0.0123]
[Trellis Server] Texture baking (opt): optimizing:  92%|#########1| 2291/2500 [00:24<00:02, 97.06it/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  92%|#########1| 2292/2500 [00:24<00:02, 97.06it/s, loss=0.0107]
[Trellis Server] Texture baking (opt): optimizing:  92%|#########1| 2293/2500 [00:24<00:02, 97.06it/s, loss=0.0085]
[Trellis Server] Texture baking (opt): optimizing:  92%|#########1| 2294/2500 [00:24<00:02, 97.06it/s, loss=0.0102]
[Trellis Server] Texture baking (opt): optimizing:  92%|#########1| 2295/2500 [00:24<00:02, 97.06it/s, loss=0.00837]
[Trellis Server] Texture baking (opt): optimizing:  92%|#########1| 2296/2500 [00:24<00:02, 97.06it/s, loss=0.00994]
[Trellis Server] Texture baking (opt): optimizing:  92%|#########1| 2297/2500 [00:24<00:02, 97.06it/s, loss=0.0146]
[Trellis Server] Texture baking (opt): optimizing:  92%|#########1| 2298/2500 [00:24<00:02, 97.06it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  92%|#########1| 2299/2500 [00:24<00:02, 97.06it/s, loss=0.0106]
Texture baking (opt): optimizing:  92%|#########2| 2301/2500 [00:24<00:02, 97.92it/s, loss=0.00974]/s, loss=0.00974]
[Trellis Server] Texture baking (opt): optimizing:  92%|#########2| 2301/2500 [00:24<00:02, 97.92it/s, loss=0.0152]
[Trellis Server] Texture baking (opt): optimizing:  92%|#########2| 2302/2500 [00:24<00:02, 97.92it/s, loss=0.0104]
[Trellis Server] Texture baking (opt): optimizing:  92%|#########2| 2303/2500 [00:24<00:02, 97.92it/s, loss=0.0183]
[Trellis Server] Texture baking (opt): optimizing:  92%|#########2| 2304/2500 [00:24<00:02, 97.92it/s, loss=0.007]
[Trellis Server] Texture baking (opt): optimizing:  92%|#########2| 2305/2500 [00:24<00:01, 97.92it/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  92%|#########2| 2306/2500 [00:24<00:01, 97.92it/s, loss=0.0116]
[Trellis Server] Texture baking (opt): optimizing:  92%|#########2| 2307/2500 [00:24<00:01, 97.92it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  92%|#########2| 2308/2500 [00:24<00:01, 97.92it/s, loss=0.0133]
[Trellis Server] Texture baking (opt): optimizing:  92%|#########2| 2309/2500 [00:24<00:01, 97.92it/s, loss=0.00829]
Texture baking (opt): optimizing:  92%|#########2| 2311/2500 [00:24<00:01, 97.38it/s, loss=0.0116]t/s, loss=0.0116]
[Trellis Server] Texture baking (opt): optimizing:  92%|#########2| 2311/2500 [00:24<00:01, 97.38it/s, loss=0.0157]
[Trellis Server] Texture baking (opt): optimizing:  92%|#########2| 2312/2500 [00:24<00:01, 97.38it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  93%|#########2| 2313/2500 [00:24<00:01, 97.38it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  93%|#########2| 2314/2500 [00:24<00:01, 97.38it/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  93%|#########2| 2315/2500 [00:24<00:01, 97.38it/s, loss=0.0151]
[Trellis Server] Texture baking (opt): optimizing:  93%|#########2| 2316/2500 [00:24<00:01, 97.38it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  93%|#########2| 2317/2500 [00:24<00:01, 97.38it/s, loss=0.0122]
[Trellis Server] Texture baking (opt): optimizing:  93%|#########2| 2318/2500 [00:24<00:01, 97.38it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  93%|#########2| 2319/2500 [00:24<00:01, 97.38it/s, loss=0.00805]
Texture baking (opt): optimizing:  93%|#########2| 2321/2500 [00:24<00:01, 97.01it/s, loss=0.0116]t/s, loss=0.0116]
[Trellis Server] Texture baking (opt): optimizing:  93%|#########2| 2321/2500 [00:24<00:01, 97.01it/s, loss=0.0145]
[Trellis Server] Texture baking (opt): optimizing:  93%|#########2| 2322/2500 [00:24<00:01, 97.01it/s, loss=0.00795]
[Trellis Server] Texture baking (opt): optimizing:  93%|#########2| 2323/2500 [00:24<00:01, 97.01it/s, loss=0.0121]
[Trellis Server] Texture baking (opt): optimizing:  93%|#########2| 2324/2500 [00:24<00:01, 97.01it/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  93%|#########3| 2325/2500 [00:24<00:01, 97.01it/s, loss=0.0103]
[Trellis Server] Texture baking (opt): optimizing:  93%|#########3| 2326/2500 [00:24<00:01, 97.01it/s, loss=0.0168]
[Trellis Server] Texture baking (opt): optimizing:  93%|#########3| 2327/2500 [00:24<00:01, 97.01it/s, loss=0.00944]
[Trellis Server] Texture baking (opt): optimizing:  93%|#########3| 2328/2500 [00:24<00:01, 97.01it/s, loss=0.00708]
[Trellis Server] Texture baking (opt): optimizing:  93%|#########3| 2329/2500 [00:24<00:01, 97.01it/s, loss=0.00981]
Texture baking (opt): optimizing:  93%|#########3| 2331/2500 [00:24<00:01, 97.04it/s, loss=0.0113]t/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  93%|#########3| 2331/2500 [00:24<00:01, 97.04it/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:  93%|#########3| 2332/2500 [00:24<00:01, 97.04it/s, loss=0.00688]
[Trellis Server] Texture baking (opt): optimizing:  93%|#########3| 2333/2500 [00:24<00:01, 97.04it/s, loss=0.014]
[Trellis Server] Texture baking (opt): optimizing:  93%|#########3| 2334/2500 [00:24<00:01, 97.04it/s, loss=0.00827]
[Trellis Server] Texture baking (opt): optimizing:  93%|#########3| 2335/2500 [00:24<00:01, 97.04it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  93%|#########3| 2336/2500 [00:24<00:01, 97.04it/s, loss=0.00963]
[Trellis Server] Texture baking (opt): optimizing:  93%|#########3| 2337/2500 [00:24<00:01, 97.04it/s, loss=0.0119]
[Trellis Server] Texture baking (opt): optimizing:  94%|#########3| 2338/2500 [00:24<00:01, 97.04it/s, loss=0.0116]
[Trellis Server] Texture baking (opt): optimizing:  94%|#########3| 2339/2500 [00:24<00:01, 97.04it/s, loss=0.0131]
Texture baking (opt): optimizing:  94%|#########3| 2341/2500 [00:24<00:01, 95.38it/s, loss=0.0112]t/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  94%|#########3| 2341/2500 [00:24<00:01, 95.38it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  94%|#########3| 2342/2500 [00:24<00:01, 95.38it/s, loss=0.00805]
[Trellis Server] Texture baking (opt): optimizing:  94%|#########3| 2343/2500 [00:24<00:01, 95.38it/s, loss=0.0153]
[Trellis Server] Texture baking (opt): optimizing:  94%|#########3| 2344/2500 [00:24<00:01, 95.38it/s, loss=0.0188]
[Trellis Server] Texture baking (opt): optimizing:  94%|#########3| 2345/2500 [00:24<00:01, 95.38it/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:  94%|#########3| 2346/2500 [00:24<00:01, 95.38it/s, loss=0.0141]
[Trellis Server] Texture baking (opt): optimizing:  94%|#########3| 2347/2500 [00:24<00:01, 95.38it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  94%|#########3| 2348/2500 [00:24<00:01, 95.38it/s, loss=0.014]
[Trellis Server] Texture baking (opt): optimizing:  94%|#########3| 2349/2500 [00:24<00:01, 95.38it/s, loss=0.00799]
[Trellis Server] Texture baking (opt): optimizing:  94%|#########3| 2350/2500 [00:24<00:01, 95.38it/s, loss=0.013]
Texture baking (opt): optimizing:  94%|#########4| 2352/2500 [00:24<00:01, 96.82it/s, loss=0.0138]t/s, loss=0.0138]
[Trellis Server] Texture baking (opt): optimizing:  94%|#########4| 2352/2500 [00:24<00:01, 96.82it/s, loss=0.00786]
[Trellis Server] Texture baking (opt): optimizing:  94%|#########4| 2353/2500 [00:24<00:01, 96.82it/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  94%|#########4| 2354/2500 [00:24<00:01, 96.82it/s, loss=0.0137]
[Trellis Server] Texture baking (opt): optimizing:  94%|#########4| 2355/2500 [00:24<00:01, 96.82it/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing:  94%|#########4| 2356/2500 [00:24<00:01, 96.82it/s, loss=0.0118]
[Trellis Server] Texture baking (opt): optimizing:  94%|#########4| 2357/2500 [00:24<00:01, 96.82it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  94%|#########4| 2358/2500 [00:24<00:01, 96.82it/s, loss=0.0169]
[Trellis Server] Texture baking (opt): optimizing:  94%|#########4| 2359/2500 [00:24<00:01, 96.82it/s, loss=0.0078]
[Trellis Server] Texture baking (opt): optimizing:  94%|#########4| 2360/2500 [00:24<00:01, 96.82it/s, loss=0.0109]
Texture baking (opt): optimizing:  94%|#########4| 2362/2500 [00:24<00:01, 97.45it/s, loss=0.0137]t/s, loss=0.0137]
[Trellis Server] Texture baking (opt): optimizing:  94%|#########4| 2362/2500 [00:24<00:01, 97.45it/s, loss=0.00905]
[Trellis Server] Texture baking (opt): optimizing:  95%|#########4| 2363/2500 [00:24<00:01, 97.45it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  95%|#########4| 2364/2500 [00:24<00:01, 97.45it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  95%|#########4| 2365/2500 [00:24<00:01, 97.45it/s, loss=0.0172]
[Trellis Server] Texture baking (opt): optimizing:  95%|#########4| 2366/2500 [00:24<00:01, 97.45it/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:  95%|#########4| 2367/2500 [00:24<00:01, 97.45it/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  95%|#########4| 2368/2500 [00:24<00:01, 97.45it/s, loss=0.0133]
[Trellis Server] Texture baking (opt): optimizing:  95%|#########4| 2369/2500 [00:24<00:01, 97.45it/s, loss=0.0136]
[Trellis Server] Texture baking (opt): optimizing:  95%|#########4| 2370/2500 [00:24<00:01, 97.45it/s, loss=0.00718]
Texture baking (opt): optimizing:  95%|#########4| 2372/2500 [00:24<00:01, 96.23it/s, loss=0.0112]t/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  95%|#########4| 2372/2500 [00:24<00:01, 96.23it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  95%|#########4| 2373/2500 [00:24<00:01, 96.23it/s, loss=0.00775]
[Trellis Server] Texture baking (opt): optimizing:  95%|#########4| 2374/2500 [00:24<00:01, 96.23it/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  95%|#########5| 2375/2500 [00:24<00:01, 96.23it/s, loss=0.0108]
[Trellis Server] Texture baking (opt): optimizing:  95%|#########5| 2376/2500 [00:24<00:01, 96.23it/s, loss=0.017]
[Trellis Server] Texture baking (opt): optimizing:  95%|#########5| 2377/2500 [00:24<00:01, 96.23it/s, loss=0.0131]
[Trellis Server] Texture baking (opt): optimizing:  95%|#########5| 2378/2500 [00:24<00:01, 96.23it/s, loss=0.0081]
[Trellis Server] Texture baking (opt): optimizing:  95%|#########5| 2379/2500 [00:24<00:01, 96.23it/s, loss=0.00924]
[Trellis Server] Texture baking (opt): optimizing:  95%|#########5| 2380/2500 [00:24<00:01, 96.23it/s, loss=0.0108]
Texture baking (opt): optimizing:  95%|#########5| 2382/2500 [00:25<00:01, 97.32it/s, loss=0.00645]/s, loss=0.00645]
[Trellis Server] Texture baking (opt): optimizing:  95%|#########5| 2382/2500 [00:25<00:01, 97.32it/s, loss=0.0122]
[Trellis Server] Texture baking (opt): optimizing:  95%|#########5| 2383/2500 [00:25<00:01, 97.32it/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  95%|#########5| 2384/2500 [00:25<00:01, 97.32it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  95%|#########5| 2385/2500 [00:25<00:01, 97.32it/s, loss=0.0147]
[Trellis Server] Texture baking (opt): optimizing:  95%|#########5| 2386/2500 [00:25<00:01, 97.32it/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing:  95%|#########5| 2387/2500 [00:25<00:01, 97.32it/s, loss=0.0126]
[Trellis Server] Texture baking (opt): optimizing:  96%|#########5| 2388/2500 [00:25<00:01, 97.32it/s, loss=0.00608]
[Trellis Server] Texture baking (opt): optimizing:  96%|#########5| 2389/2500 [00:25<00:01, 97.32it/s, loss=0.0102]
[Trellis Server] Texture baking (opt): optimizing:  96%|#########5| 2390/2500 [00:25<00:01, 97.32it/s, loss=0.0117]
[Trellis Server] Texture baking (opt): optimizing:  96%|#########5| 2391/2500 [00:25<00:01, 97.32it/s, loss=0.012]
Texture baking (opt): optimizing:  96%|#########5| 2393/2500 [00:25<00:01, 98.35it/s, loss=0.0115]t/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  96%|#########5| 2393/2500 [00:25<00:01, 98.35it/s, loss=0.0107]
[Trellis Server] Texture baking (opt): optimizing:  96%|#########5| 2394/2500 [00:25<00:01, 98.35it/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  96%|#########5| 2395/2500 [00:25<00:01, 98.35it/s, loss=0.0102]
[Trellis Server] Texture baking (opt): optimizing:  96%|#########5| 2396/2500 [00:25<00:01, 98.35it/s, loss=0.0116]
[Trellis Server] Texture baking (opt): optimizing:  96%|#########5| 2397/2500 [00:25<00:01, 98.35it/s, loss=0.0167]
[Trellis Server] Texture baking (opt): optimizing:  96%|#########5| 2398/2500 [00:25<00:01, 98.35it/s, loss=0.013]
[Trellis Server] Texture baking (opt): optimizing:  96%|#########5| 2399/2500 [00:25<00:01, 98.35it/s, loss=0.0136]
[Trellis Server] Texture baking (opt): optimizing:  96%|#########6| 2400/2500 [00:25<00:01, 98.35it/s, loss=0.0149]
[Trellis Server] Texture baking (opt): optimizing:  96%|#########6| 2401/2500 [00:25<00:01, 98.35it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  96%|#########6| 2402/2500 [00:25<00:00, 98.35it/s, loss=0.00632]
Texture baking (opt): optimizing:  96%|#########6| 2404/2500 [00:25<00:00, 98.86it/s, loss=0.0108]t/s, loss=0.0108]
[Trellis Server] Texture baking (opt): optimizing:  96%|#########6| 2404/2500 [00:25<00:00, 98.86it/s, loss=0.0136]
[Trellis Server] Texture baking (opt): optimizing:  96%|#########6| 2405/2500 [00:25<00:00, 98.86it/s, loss=0.00988]
[Trellis Server] Texture baking (opt): optimizing:  96%|#########6| 2406/2500 [00:25<00:00, 98.86it/s, loss=0.0115]
[Trellis Server] Texture baking (opt): optimizing:  96%|#########6| 2407/2500 [00:25<00:00, 98.86it/s, loss=0.0059]
[Trellis Server] Texture baking (opt): optimizing:  96%|#########6| 2408/2500 [00:25<00:00, 98.86it/s, loss=0.0116]
[Trellis Server] Texture baking (opt): optimizing:  96%|#########6| 2409/2500 [00:25<00:00, 98.86it/s, loss=0.0158]
[Trellis Server] Texture baking (opt): optimizing:  96%|#########6| 2410/2500 [00:25<00:00, 98.86it/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  96%|#########6| 2411/2500 [00:25<00:00, 98.86it/s, loss=0.0102]
[Trellis Server] Texture baking (opt): optimizing:  96%|#########6| 2412/2500 [00:25<00:00, 98.86it/s, loss=0.0145]
Texture baking (opt): optimizing:  97%|#########6| 2414/2500 [00:25<00:00, 97.78it/s, loss=0.012]it/s, loss=0.012]
[Trellis Server] Texture baking (opt): optimizing:  97%|#########6| 2414/2500 [00:25<00:00, 97.78it/s, loss=0.00932]
[Trellis Server] Texture baking (opt): optimizing:  97%|#########6| 2415/2500 [00:25<00:00, 97.78it/s, loss=0.00984]
[Trellis Server] Texture baking (opt): optimizing:  97%|#########6| 2416/2500 [00:25<00:00, 97.78it/s, loss=0.0129]
[Trellis Server] Texture baking (opt): optimizing:  97%|#########6| 2417/2500 [00:25<00:00, 97.78it/s, loss=0.0101]
[Trellis Server] Texture baking (opt): optimizing:  97%|#########6| 2418/2500 [00:25<00:00, 97.78it/s, loss=0.00927]
[Trellis Server] Texture baking (opt): optimizing:  97%|#########6| 2419/2500 [00:25<00:00, 97.78it/s, loss=0.00919]
[Trellis Server] Texture baking (opt): optimizing:  97%|#########6| 2420/2500 [00:25<00:00, 97.78it/s, loss=0.0153]
[Trellis Server] Texture baking (opt): optimizing:  97%|#########6| 2421/2500 [00:25<00:00, 97.78it/s, loss=0.0102]
[Trellis Server] Texture baking (opt): optimizing:  97%|#########6| 2422/2500 [00:25<00:00, 97.78it/s, loss=0.0116]
Texture baking (opt): optimizing:  97%|#########6| 2424/2500 [00:25<00:00, 95.14it/s, loss=0.0114]t/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  97%|#########6| 2424/2500 [00:25<00:00, 95.14it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  97%|#########7| 2425/2500 [00:25<00:00, 95.14it/s, loss=0.00791]
[Trellis Server] Texture baking (opt): optimizing:  97%|#########7| 2426/2500 [00:25<00:00, 95.14it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  97%|#########7| 2427/2500 [00:25<00:00, 95.14it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  97%|#########7| 2428/2500 [00:25<00:00, 95.14it/s, loss=0.00802]
[Trellis Server] Texture baking (opt): optimizing:  97%|#########7| 2429/2500 [00:25<00:00, 95.14it/s, loss=0.0105]
[Trellis Server] Texture baking (opt): optimizing:  97%|#########7| 2430/2500 [00:25<00:00, 95.14it/s, loss=0.0103]
[Trellis Server] Texture baking (opt): optimizing:  97%|#########7| 2431/2500 [00:25<00:00, 95.14it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  97%|#########7| 2432/2500 [00:25<00:00, 95.14it/s, loss=0.00804]
Texture baking (opt): optimizing:  97%|#########7| 2434/2500 [00:25<00:00, 95.17it/s, loss=0.0112]t/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  97%|#########7| 2434/2500 [00:25<00:00, 95.17it/s, loss=0.0121]
[Trellis Server] Texture baking (opt): optimizing:  97%|#########7| 2435/2500 [00:25<00:00, 95.17it/s, loss=0.00929]
[Trellis Server] Texture baking (opt): optimizing:  97%|#########7| 2436/2500 [00:25<00:00, 95.17it/s, loss=0.00584]
[Trellis Server] Texture baking (opt): optimizing:  97%|#########7| 2437/2500 [00:25<00:00, 95.17it/s, loss=0.00926]
[Trellis Server] Texture baking (opt): optimizing:  98%|#########7| 2438/2500 [00:25<00:00, 95.17it/s, loss=0.0145]
[Trellis Server] Texture baking (opt): optimizing:  98%|#########7| 2439/2500 [00:25<00:00, 95.17it/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  98%|#########7| 2440/2500 [00:25<00:00, 95.17it/s, loss=0.0107]
[Trellis Server] Texture baking (opt): optimizing:  98%|#########7| 2441/2500 [00:25<00:00, 95.17it/s, loss=0.00783]
[Trellis Server] Texture baking (opt): optimizing:  98%|#########7| 2442/2500 [00:25<00:00, 95.17it/s, loss=0.00579]
Texture baking (opt): optimizing:  98%|#########7| 2444/2500 [00:25<00:00, 94.56it/s, loss=0.0109]t/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  98%|#########7| 2444/2500 [00:25<00:00, 94.56it/s, loss=0.00913]
[Trellis Server] Texture baking (opt): optimizing:  98%|#########7| 2445/2500 [00:25<00:00, 94.56it/s, loss=0.0127]
[Trellis Server] Texture baking (opt): optimizing:  98%|#########7| 2446/2500 [00:25<00:00, 94.56it/s, loss=0.00989]
[Trellis Server] Texture baking (opt): optimizing:  98%|#########7| 2447/2500 [00:25<00:00, 94.56it/s, loss=0.00987]
[Trellis Server] Texture baking (opt): optimizing:  98%|#########7| 2448/2500 [00:25<00:00, 94.56it/s, loss=0.0123]
[Trellis Server] Texture baking (opt): optimizing:  98%|#########7| 2449/2500 [00:25<00:00, 94.56it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  98%|#########8| 2450/2500 [00:25<00:00, 94.56it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  98%|#########8| 2451/2500 [00:25<00:00, 94.56it/s, loss=0.007]
[Trellis Server] Texture baking (opt): optimizing:  98%|#########8| 2452/2500 [00:25<00:00, 94.56it/s, loss=0.0108]
Texture baking (opt): optimizing:  98%|#########8| 2454/2500 [00:25<00:00, 93.03it/s, loss=0.0112]t/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  98%|#########8| 2454/2500 [00:25<00:00, 93.03it/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  98%|#########8| 2455/2500 [00:25<00:00, 93.03it/s, loss=0.00925]
[Trellis Server] Texture baking (opt): optimizing:  98%|#########8| 2456/2500 [00:25<00:00, 93.03it/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  98%|#########8| 2457/2500 [00:25<00:00, 93.03it/s, loss=0.00966]
[Trellis Server] Texture baking (opt): optimizing:  98%|#########8| 2458/2500 [00:25<00:00, 93.03it/s, loss=0.00912]
[Trellis Server] Texture baking (opt): optimizing:  98%|#########8| 2459/2500 [00:25<00:00, 93.03it/s, loss=0.0167]
[Trellis Server] Texture baking (opt): optimizing:  98%|#########8| 2460/2500 [00:25<00:00, 93.03it/s, loss=0.0091]
[Trellis Server] Texture baking (opt): optimizing:  98%|#########8| 2461/2500 [00:25<00:00, 93.03it/s, loss=0.0114]
[Trellis Server] Texture baking (opt): optimizing:  98%|#########8| 2462/2500 [00:25<00:00, 93.03it/s, loss=0.0112]
Texture baking (opt): optimizing:  99%|#########8| 2464/2500 [00:25<00:00, 92.48it/s, loss=0.0113]t/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  99%|#########8| 2464/2500 [00:25<00:00, 92.48it/s, loss=0.0116]
[Trellis Server] Texture baking (opt): optimizing:  99%|#########8| 2465/2500 [00:25<00:00, 92.48it/s, loss=0.01]
[Trellis Server] Texture baking (opt): optimizing:  99%|#########8| 2466/2500 [00:25<00:00, 92.48it/s, loss=0.0144]
[Trellis Server] Texture baking (opt): optimizing:  99%|#########8| 2467/2500 [00:25<00:00, 92.48it/s, loss=0.00922]
[Trellis Server] Texture baking (opt): optimizing:  99%|#########8| 2468/2500 [00:25<00:00, 92.48it/s, loss=0.011]
[Trellis Server] Texture baking (opt): optimizing:  99%|#########8| 2469/2500 [00:25<00:00, 92.48it/s, loss=0.0132]
[Trellis Server] Texture baking (opt): optimizing:  99%|#########8| 2470/2500 [00:25<00:00, 92.48it/s, loss=0.0129]
[Trellis Server] Texture baking (opt): optimizing:  99%|#########8| 2471/2500 [00:25<00:00, 92.48it/s, loss=0.00988]
[Trellis Server] Texture baking (opt): optimizing:  99%|#########8| 2472/2500 [00:25<00:00, 92.48it/s, loss=0.0153]
Texture baking (opt): optimizing:  99%|#########8| 2474/2500 [00:25<00:00, 93.81it/s, loss=0.0144]t/s, loss=0.0144]
[Trellis Server] Texture baking (opt): optimizing:  99%|#########8| 2474/2500 [00:25<00:00, 93.81it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  99%|#########9| 2475/2500 [00:25<00:00, 93.81it/s, loss=0.01]
[Trellis Server] Texture baking (opt): optimizing:  99%|#########9| 2476/2500 [00:26<00:00, 93.81it/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing:  99%|#########9| 2477/2500 [00:26<00:00, 93.81it/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing:  99%|#########9| 2478/2500 [00:26<00:00, 93.81it/s, loss=0.0102]
[Trellis Server] Texture baking (opt): optimizing:  99%|#########9| 2479/2500 [00:26<00:00, 93.81it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  99%|#########9| 2480/2500 [00:26<00:00, 93.81it/s, loss=0.00802]
[Trellis Server] Texture baking (opt): optimizing:  99%|#########9| 2481/2500 [00:26<00:00, 93.81it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing:  99%|#########9| 2482/2500 [00:26<00:00, 93.81it/s, loss=0.0118]
Texture baking (opt): optimizing:  99%|#########9| 2484/2500 [00:26<00:00, 93.18it/s, loss=0.0139]t/s, loss=0.0139]
[Trellis Server] Texture baking (opt): optimizing:  99%|#########9| 2484/2500 [00:26<00:00, 93.18it/s, loss=0.0113]
[Trellis Server] Texture baking (opt): optimizing:  99%|#########9| 2485/2500 [00:26<00:00, 93.18it/s, loss=0.0167]
[Trellis Server] Texture baking (opt): optimizing:  99%|#########9| 2486/2500 [00:26<00:00, 93.18it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing:  99%|#########9| 2487/2500 [00:26<00:00, 93.18it/s, loss=0.0109]
[Trellis Server] Texture baking (opt): optimizing: 100%|#########9| 2488/2500 [00:26<00:00, 93.18it/s, loss=0.0128]
[Trellis Server] Texture baking (opt): optimizing: 100%|#########9| 2489/2500 [00:26<00:00, 93.18it/s, loss=0.0118]
[Trellis Server] Texture baking (opt): optimizing: 100%|#########9| 2490/2500 [00:26<00:00, 93.18it/s, loss=0.00697]
[Trellis Server] Texture baking (opt): optimizing: 100%|#########9| 2491/2500 [00:26<00:00, 93.18it/s, loss=0.0106]
[Trellis Server] Texture baking (opt): optimizing: 100%|#########9| 2492/2500 [00:26<00:00, 93.18it/s, loss=0.00803]
Texture baking (opt): optimizing: 100%|#########9| 2494/2500 [00:26<00:00, 94.85it/s, loss=0.0186]t/s, loss=0.0186]
[Trellis Server] Texture baking (opt): optimizing: 100%|#########9| 2494/2500 [00:26<00:00, 94.85it/s, loss=0.0112]
[Trellis Server] Texture baking (opt): optimizing: 100%|#########9| 2495/2500 [00:26<00:00, 94.85it/s, loss=0.0111]
[Trellis Server] Texture baking (opt): optimizing: 100%|#########9| 2496/2500 [00:26<00:00, 94.85it/s, loss=0.0131]
[Trellis Server] Texture baking (opt): optimizing: 100%|#########9| 2497/2500 [00:26<00:00, 94.85it/s, loss=0.0102]
[Trellis Server] Texture baking (opt): optimizing: 100%|#########9| 2498/2500 [00:26<00:00, 94.85it/s, loss=0.0129]
Texture baking (opt): optimizing: 100%|##########| 2500/2500 [00:26<00:00, 95.25it/s, loss=0.0113]t/s, loss=0.0113]
[Trellis Server] 07:02:05 - INFO - 3D model generation completed in 74.71 seconds
[Trellis Server] POST response status: 200
[Trellis Server] POST response data: {
  status: 'COMPLETED',
  progress: 100,
  message: '3D model generation complete',
  preview_urls: null,
  model_url: '/download/model',
  model_base64: null
}
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'download',
  progress: 100,
  message: 'Downloading generated 3D model',
  description: 'Downloading generated 3D model'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'download',
  progress: 100,
  message: 'Downloading generated 3D model',
  description: 'Downloading generated 3D model'
}
[Trellis Server] Model URL from server: /download/model
[Trellis Server] Full model URL for download: http://127.0.0.1:7960/download/model
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'download',
  progress: 100,
  message: 'Downloading generated 3D model',
  description: 'Downloading generated 3D model'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'download',
  progress: 100,
  message: 'Downloading generated 3D model',
  description: 'Downloading generated 3D model'
}
[Trellis Server] 07:02:05 - INFO - Client is downloading a model.
[Trellis Server] Downloading model to: N:\3D AI Studio\output\model.glb
[Trellis Server] Model response status: 200
[Trellis Server] Model response headers: model/gltf-binary
[Trellis Server] Model file saved successfully
[Pipeline Loader] Wrapped progress callback called: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'download',
  progress: 100,
  message: 'Downloading generated 3D model',
  description: 'Downloading generated 3D model'
}
[IPC Handler] Sending pipeline status to frontend: {
  event: 'progress',
  session_id: 'f4e9976b-b5c1-437d-84af-eb04a5b5418c',
  stage: 'download',
  progress: 100,
  message: 'Downloading generated 3D model',
  description: 'Downloading generated 3D model'
}
[Trellis Server] Output file absolute path: N:\3D AI Studio\output\model.glb
[Trellis Server] Output file relative path: output\model.glb
[Trellis Server] File exists: true
[Trellis Server] File size: 1407016 bytes
info: [load-file] Received request for: output\model.glb {"service":"user-service","timestamp":"2025-07-08 07:02:05"}
info: [load-file] Reading absolute path: N:\3D AI Studio\output\model.glb {"service":"user-service","timestamp":"2025-07-08 07:02:05"}
warn: [load-file] Received invalid request for: undefined {"service":"user-service","timestamp":"2025-07-08 07:02:05"}
