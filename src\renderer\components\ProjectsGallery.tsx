/// <reference types="../vite-env.d.ts" />
import React, { useState, useEffect, useImperativeHandle, forwardRef, useRef } from 'react';
import { FolderOpen, Calendar, Download, Eye, Trash2, RefreshCw, Plus, Search, X, ChevronDown, Cuboid, Video, Image as ImageIcon, Info } from 'lucide-react';
import { ModelViewer } from './ModelViewer';

// Custom Hook to load a file via IPC and return a data URL
const useDataUrl = (relativePath?: string) => {
  const [url, setUrl] = useState<string | undefined>(undefined);

  useEffect(() => {
    let isMounted = true;
    if (relativePath) {
      // Reset url when path changes, to prevent showing old content
      setUrl(undefined); 
      window.electronAPI.loadFile(relativePath).then((dataUrl: string | undefined) => {
        if (isMounted) {
          setUrl(dataUrl);
        }
      }).catch((error: Error) => {
        console.error(`useDataUrl: Failed to load ${relativePath}`, error);
        if (isMounted) {
          setUrl(undefined);
        }
      });
    } else {
      setUrl(undefined);
    }
    return () => {
      isMounted = false;
    };
  }, [relativePath]);

  return url;
};

interface Project {
  id: string;
  name: string;
  created_at: string;
  type: 'image-to-3d' | 'text-to-3d' | 'image-generation';
  prompt?: string;
  original_image_path?: string;
  thumbnail_url?: string;
  files?: {
    model?: string;
    video?: string;
    generated_image?: string;
    thumbnail?: string;
  };
  generationStats?: {
    generationMode?: 'text-to-3d' | 'image-to-3d';
    prompt?: string;
    enhancedPrompt?: string;
    imageModel?: string;
    settings?: {
      ss_steps?: number;
      ss_cfg_strength?: number;
      slat_steps?: number;
      slat_cfg_strength?: number;
      seed?: number;
      simplify?: number;
      texture_size?: number;
      enable_lighting_optimizer?: boolean;
      enable_delighter?: boolean;
      delighter_quality?: string;
    };
    fileInfo?: {
      type?: string;
      size?: number;
      vertices?: number;
      faces?: number;
    };
    timing?: {
      totalTime?: number;
      textToImageTime?: number;
      modelGenerationTime?: number;
      delighterTime?: number;
    };
  };
}

interface ProjectsGalleryProps {
  isDarkMode: boolean;
  onCreate3DFromImage: (imageUrl: string, prompt?: string) => void;
}

const ProjectsGallery = forwardRef<any, ProjectsGalleryProps>(({ isDarkMode, onCreate3DFromImage }, ref) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [isViewerOpen, setIsViewerOpen] = useState(false);
  
  // Delete confirmation modal state
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<Project | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const fetchProjects = () => {
    window.electronAPI.getProjects().then(setProjects);
  };

  useEffect(() => {
    fetchProjects();
  }, []);

  useImperativeHandle(ref, () => ({
    refresh: fetchProjects,
  }));

  const handleViewDetails = (project: Project) => {
    window.electronAPI.getProjectDetails(project.id).then((details: Project) => {
      setSelectedProject(details);
      setIsViewerOpen(true);
    });
  };

  const handleDeleteClick = (project: Project) => {
    setProjectToDelete(project);
    setShowDeleteConfirm(true);
  };

  const handleDeleteConfirm = async () => {
    if (!projectToDelete) return;

    try {
      setIsDeleting(true);
      await window.electronAPI.deleteProject(projectToDelete.id);
      
      fetchProjects();
      if (selectedProject?.id === projectToDelete.id) {
        setIsViewerOpen(false);
        setSelectedProject(null);
      }
      
      setShowDeleteConfirm(false);
      setProjectToDelete(null);
    } catch (error) {
      console.error('Error deleting project:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteConfirm(false);
    setProjectToDelete(null);
  };

  const handleCloseViewer = () => {
    setIsViewerOpen(false);
    setSelectedProject(null);
  };

  const ProjectCard = ({ project }: { project: Project }) => (
    <div className={`group relative rounded-lg overflow-hidden shadow-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} hover:shadow-xl transition-shadow duration-300 flex flex-col`}>
      <div className="relative">
        <ProjectThumbnail project={project} />
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <button onClick={() => handleViewDetails(project)} className="text-white bg-blue-500 hover:bg-blue-600 rounded-full p-3 transition-colors">
            <Eye size={24} />
          </button>
        </div>
      </div>
      <div className="p-4 flex-grow">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-white truncate">{project.name}</h3>
        <p className="text-sm text-gray-500 dark:text-gray-400 capitalize">{project.type.replace('-', ' ')}</p>
      </div>
      <div className="px-4 pb-4 flex justify-between items-center text-xs text-gray-400 dark:text-gray-500">
        <div className="flex items-center">
          <Calendar size={14} className="mr-1" />
          <span>{new Date(project.created_at).toLocaleDateString()}</span>
        </div>
        <button onClick={() => handleDeleteClick(project)} className="text-red-500 hover:text-red-700 transition-colors">
          <Trash2 size={16} />
        </button>
      </div>
    </div>
  );

  const ProjectThumbnail = ({ project }: { project: Project }) => {
    const thumbnailUrl = useDataUrl(project.thumbnail_url);
    return (
      <div className={`aspect-square ${isDarkMode ? 'bg-gray-600' : 'bg-gray-200'} flex items-center justify-center overflow-hidden`}>
        {thumbnailUrl ? (
          <img src={thumbnailUrl} alt={project.name} className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" />
        ) : (
          <Cuboid className={`w-16 h-16 ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`} />
        )}
      </div>
    );
  };

  const ImageViewerContent = ({ project }: { project: Project }) => {
    const generatedImageUrl = useDataUrl(project.files?.generated_image);
    const [showInfo, setShowInfo] = useState(false);

    return (
        <div className="relative w-full h-full flex items-center justify-center bg-gray-900">
            {generatedImageUrl ? (
                <img src={generatedImageUrl} alt={project.name} className="max-w-full max-h-full object-contain" />
            ) : (
                <div className="text-white">Image not found</div>
            )}

            <button 
                onClick={() => setShowInfo(!showInfo)} 
                className="absolute top-4 right-12 bg-gray-800 bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 z-20"
                title="View project info"
            >
                <Info size={24} />
            </button>

            {project.type === 'image-generation' && generatedImageUrl && (
              <button
                onClick={() => {
                  if (generatedImageUrl) {
                    onCreate3DFromImage(generatedImageUrl, project.prompt);
                    handleCloseViewer();
                  }
                }}
                className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2 z-20"
                title="Create 3D Model from this Image"
              >
                <Cuboid size={20} />
                Create 3D Model
              </button>
            )}

            {showInfo && (
                <div 
                  className={`absolute top-0 right-0 h-full ${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-l-lg shadow-lg w-96 text-sm overflow-y-auto z-10 transition-transform transform translate-x-0`}
                  onClick={e => e.stopPropagation()}
                >
                    <div className="flex justify-between items-center mb-4">
                      <h3 className={`font-bold text-xl ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{project.name}</h3>
                      <button onClick={() => setShowInfo(false)} className={`${isDarkMode ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-800'}`}>
                        <X size={20}/>
                      </button>
                    </div>
                    
                    {project.prompt && (
                        <div>
                            <h4 className={`font-semibold mt-4 mb-2 ${isDarkMode ? 'text-gray-100' : 'text-gray-800'}`}>Prompt</h4>
                            <p className={`${isDarkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'} p-3 rounded-md whitespace-pre-wrap`}>{project.prompt}</p>
                        </div>
                    )}
                     {project.generationStats?.settings && (
                        <div>
                          <h4 className={`font-semibold mt-4 mb-2 ${isDarkMode ? 'text-gray-100' : 'text-gray-800'}`}>Generation Settings</h4>
                          <div className={`text-sm space-y-2 ${isDarkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'} p-3 rounded-md`}>
                            {Object.entries(project.generationStats.settings).map(([key, value]) => (
                              <div key={key} className="flex justify-between">
                                <strong className="capitalize">{key.replace(/_/g, ' ')}:</strong> 
                                <span>{value.toString()}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                      <div className="mt-4">
                        <h4 className={`font-semibold mb-2 ${isDarkMode ? 'text-gray-100' : 'text-gray-800'}`}>Details</h4>
                         <div className={`text-sm space-y-2 ${isDarkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'} p-3 rounded-md`}>
                           <div className="flex justify-between">
                              <strong>Type:</strong>
                              <span className="capitalize">{project.type.replace('-', ' ')}</span>
                            </div>
                            <div className="flex justify-between">
                              <strong>Created:</strong>
                              <span>{new Date(project.created_at).toLocaleString()}</span>
                           </div>
                        </div>
                      </div>
                </div>
            )}
        </div>
    );
  };

  const ViewerContent = ({ project }: { project: Project }) => {
    const originalImageUrl = useDataUrl(project.original_image_path);
    const modelUrl = useDataUrl(project.files?.model);
    const videoUrl = useDataUrl(project.files?.video);
    const generatedImageUrl = useDataUrl(project.files?.generated_image);
    const [showInfo, setShowInfo] = useState(false);

    if (project.type === 'image-generation') {
        return <ImageViewerContent project={project} />;
    }

    // Don't render the model viewer until the URL is loaded
    if (!modelUrl && !videoUrl && !generatedImageUrl) {
      return (
        <div className={`w-full h-full flex items-center justify-center ${isDarkMode ? 'bg-gray-800' : 'bg-gray-900'}`}>
          <div className="text-center text-white">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
            <p>Loading asset...</p>
          </div>
        </div>
      );
    }

    const modelViewerNode = modelUrl ? (
      <ModelViewer
        isTextured={true}
        isDarkMode={isDarkMode}
        modelUrl={modelUrl}
        filePath={project.files?.model}
        generationStats={project.generationStats || {}}
      />
    ) : null;

    return (
      <div className="relative w-full h-full flex items-center justify-center bg-gray-900">
        {videoUrl ? (
          <video src={videoUrl} autoPlay loop muted controls className="w-full h-full object-contain" />
        ) : modelViewerNode ? (
          modelViewerNode
        ) : generatedImageUrl ? (
          <img src={generatedImageUrl} alt={project.name} className="w-full h-full object-contain" />
        ) : (
          <div className="text-white">Asset not found</div>
        )}

        <button 
            onClick={() => setShowInfo(!showInfo)} 
            className="absolute top-4 right-12 bg-gray-800 bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 z-20"
            title="View project info"
        >
            <Info size={24} />
        </button>

        {showInfo && (
            <div 
              className={`absolute top-0 right-0 h-full ${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-l-lg shadow-lg w-96 text-sm overflow-y-auto z-10 transition-transform transform translate-x-0`}
              onClick={e => e.stopPropagation()}
            >
                <div className="flex justify-between items-center mb-4">
                  <h3 className={`font-bold text-xl ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{project.name}</h3>
                  <button onClick={() => setShowInfo(false)} className={`${isDarkMode ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-800'}`}>
                    <X size={20}/>
                  </button>
                </div>
                
                {project.prompt && (
                    <div>
                        <h4 className={`font-semibold mt-4 mb-2 ${isDarkMode ? 'text-gray-100' : 'text-gray-800'}`}>Prompt</h4>
                        <p className={`${isDarkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'} p-3 rounded-md whitespace-pre-wrap`}>{project.prompt}</p>
                    </div>
                )}
                 {project.generationStats?.settings && (
                    <div>
                      <h4 className={`font-semibold mt-4 mb-2 ${isDarkMode ? 'text-gray-100' : 'text-gray-800'}`}>Inference Settings</h4>
                      <div className={`text-sm space-y-2 ${isDarkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'} p-3 rounded-md`}>
                        {/* Sparse Structure Settings */}
                        {project.generationStats.settings.ss_steps && (
                          <div className="flex justify-between">
                            <strong>SS Steps:</strong>
                            <span>{project.generationStats.settings.ss_steps}</span>
                          </div>
                        )}
                        {project.generationStats.settings.ss_cfg_strength && (
                          <div className="flex justify-between">
                            <strong>SS CFG Strength:</strong>
                            <span>{project.generationStats.settings.ss_cfg_strength}</span>
                          </div>
                        )}
                        {/* SLAT Settings */}
                        {project.generationStats.settings.slat_steps && (
                          <div className="flex justify-between">
                            <strong>SLAT Steps:</strong>
                            <span>{project.generationStats.settings.slat_steps}</span>
                          </div>
                        )}
                        {project.generationStats.settings.slat_cfg_strength && (
                          <div className="flex justify-between">
                            <strong>SLAT CFG Strength:</strong>
                            <span>{project.generationStats.settings.slat_cfg_strength}</span>
                          </div>
                        )}
                        {/* Other Settings */}
                        {project.generationStats.settings.seed && (
                          <div className="flex justify-between">
                            <strong>Seed:</strong>
                            <span>{project.generationStats.settings.seed}</span>
                          </div>
                        )}
                        {project.generationStats.settings.simplify && (
                          <div className="flex justify-between">
                            <strong>Simplify Ratio:</strong>
                            <span>{project.generationStats.settings.simplify}</span>
                          </div>
                        )}
                        {typeof project.generationStats.settings.enable_lighting_optimizer === 'boolean' && (
                          <div className="flex justify-between">
                            <strong>Lighting Optimizer:</strong>
                            <span>{project.generationStats.settings.enable_lighting_optimizer ? 'Enabled' : 'Disabled'}</span>
                          </div>
                        )}
                        {typeof project.generationStats.settings.enable_delighter === 'boolean' && (
                          <div className="flex justify-between">
                            <strong>Delighter:</strong>
                            <span>{project.generationStats.settings.enable_delighter ? 'Enabled' : 'Disabled'}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                  <div className="mt-4">
                    <h4 className={`font-semibold mb-2 ${isDarkMode ? 'text-gray-100' : 'text-gray-800'}`}>Details</h4>
                     <div className={`text-sm space-y-2 ${isDarkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'} p-3 rounded-md`}>
                       <div className="flex justify-between">
                          <strong>Type:</strong>
                          <span className="capitalize">{project.type.replace('-', ' ')}</span>
                        </div>
                        <div className="flex justify-between">
                          <strong>Created:</strong>
                          <span>{new Date(project.created_at).toLocaleString()}</span>
                       </div>
                       {/* Model Used */}
                       {project.generationStats?.settings && (
                         <div className="flex justify-between">
                           <strong>Model Used:</strong>
                           <span className="capitalize">
                             {project.generationStats.settings.pipeline || 'Trellis'}
                           </span>
                         </div>
                       )}
                       {/* Texture Resolution */}
                       {project.generationStats?.settings?.texture_size && (
                         <div className="flex justify-between">
                           <strong>Texture Resolution:</strong>
                           <span>{project.generationStats.settings.texture_size}x{project.generationStats.settings.texture_size}</span>
                         </div>
                       )}
                    </div>
                  </div>
                  <div className="mt-4">
                    <h4 className={`font-semibold mb-2 ${isDarkMode ? 'text-gray-100' : 'text-gray-800'}`}>Assets</h4>
                    <div className="space-y-2">
                        {originalImageUrl && <AssetItem icon={<ImageIcon/>} label="Original Image" url={originalImageUrl} onDownload={() => { if(project.original_image_path) window.electronAPI.downloadFile(project.original_image_path)}} />}
                        {modelUrl && <AssetItem icon={<Cuboid/>} label="3D Model (.glb)" url={modelUrl} onDownload={() => { if(project.files?.model) window.electronAPI.downloadFile(project.files.model)}} />}
                        {videoUrl && <AssetItem icon={<Video/>} label="Turntable Video (.mp4)" url={videoUrl} onDownload={() => { if(project.files?.video) window.electronAPI.downloadFile(project.files.video)}} />}
                    </div>
                  </div>
            </div>
        )}
      </div>
    );
  };
  
  const formatFileSize = (bytes?: number) => {
    if (!bytes || bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };
  
  const AssetItem = ({ icon, label, url, onDownload }: { icon: React.ReactNode, label: string, url: string, onDownload: () => void }) => (
    <div className="flex items-center space-x-2 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
        <div className="text-gray-500 dark:text-gray-400">{icon}</div>
        <div className="flex-grow">
            <p className="font-medium">{label}</p>
        </div>
        <a onClick={onDownload} className="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 cursor-pointer">
            <Download size={18}/>
        </a>
    </div>
  );

  return (
    <div className={`h-full ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-black'}`}>
      <header className={`p-4 border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">Projects Gallery</h1>
          <div className="flex items-center space-x-4">
            <button className="text-gray-500 hover:text-gray-800 dark:hover:text-white">
              <RefreshCw size={20} />
            </button>
            <button className="text-gray-500 hover:text-gray-800 dark:hover:text-white">
              <Plus size={20} />
            </button>
          </div>
        </div>
      </header>
      <main className="p-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
        {projects.map(p => <ProjectCard key={p.id} project={p} />)}
      </main>

      {isViewerOpen && selectedProject && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center"
          onClick={handleCloseViewer}
        >
          <div 
            className={`relative w-full h-full max-w-6xl max-h-[90vh] ${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-2xl overflow-hidden flex flex-col`}
            onClick={e => e.stopPropagation()}
          >
            <header className="p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
                <h2 className="text-xl font-bold">{selectedProject.name}</h2>
            </header>
            <main className="flex-1 overflow-auto">
                <ViewerContent project={selectedProject} />
            </main>
            <button onClick={handleCloseViewer} className="absolute top-4 right-4 text-gray-500 bg-transparent hover:bg-gray-200 dark:hover:bg-gray-700 rounded-full p-2 z-10">
              <X size={24} />
            </button>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && projectToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`p-6 rounded-lg shadow-xl max-w-md w-full mx-4 ${
            isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'
          }`}>
            <h3 className="text-lg font-semibold mb-4">Delete Project</h3>
            <p className="mb-6">
              Are you sure you want to delete "<strong>{projectToDelete.name}</strong>"? 
              This action cannot be undone and will permanently remove all associated files.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={handleDeleteCancel}
                disabled={isDeleting}
                className={`px-4 py-2 rounded-md ${
                  isDarkMode 
                    ? 'bg-gray-700 hover:bg-gray-600 text-white' 
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-800'
                } transition-colors duration-200 disabled:opacity-50`}
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteConfirm}
                disabled={isDeleting}
                className="px-4 py-2 rounded-md bg-red-600 hover:bg-red-700 text-white transition-colors duration-200 disabled:opacity-50"
              >
                {isDeleting ? 'Deleting...' : 'Delete Project'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});

export default ProjectsGallery;