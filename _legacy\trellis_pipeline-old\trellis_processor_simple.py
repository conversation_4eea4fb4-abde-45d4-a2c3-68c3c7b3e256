#!/usr/bin/env python3
"""
Simplified Trellis processor that outputs progress messages for monitoring.
This version removes the complex progress file system and just prints status messages
that can be monitored by the parent process.
"""

import os
import json
import argparse
import uuid
from pathlib import Path

# Set environment variables for optimal performance
os.environ['SPCONV_ALGO'] = 'native'  # Recommended for single runs
os.environ['CUDA_VISIBLE_DEVICES'] = '0'  # Use first GPU if available

def setup_trellis_environment():
    """Setup the Trellis environment and import required modules"""
    try:
        # Add the TRELLIS directory to Python path
        trellis_path = Path(__file__).parent.parent.parent / "Resources" / "TRELLIS"
        import sys
        if str(trellis_path) not in sys.path:
            sys.path.insert(0, str(trellis_path))

        # Import required modules
        import imageio
        from PIL import Image
        from trellis.pipelines import TrellisImageTo3DPipeline
        from trellis.utils import render_utils, postprocessing_utils

        print("Successfully imported all Trellis modules!")
        return True, imageio, Image, TrellisImageTo3DPipeline, render_utils, postprocessing_utils

    except ImportError as e:
        print(f"Failed to import Trellis modules: {e}")
        import traceback
        traceback.print_exc()
        return False, None, None, None, None, None


def process_image_with_trellis(image_path, output_dir, settings):
    """Process an image using the Trellis pipeline"""

    # Setup environment
    success, imageio, Image, TrellisImageTo3DPipeline, render_utils, postprocessing_utils = setup_trellis_environment()

    if not success:
        return {"error": "Failed to setup Trellis environment"}

    try:
        print(f"Loading image: {image_path}")
        image = Image.open(image_path)
        print(f"Image loaded: {image.size}, mode: {image.mode}")

        print(f"Loading Trellis pipeline...")
        pipeline = TrellisImageTo3DPipeline.from_pretrained("JeffreyXiang/TRELLIS-image-large")

        # Check if CUDA is available and move to GPU
        try:
            import torch
            if torch.cuda.is_available():
                pipeline.cuda()
                print(f"Pipeline loaded on GPU: {torch.cuda.get_device_name(0)}")
            else:
                print("CUDA not available, using CPU")
        except ImportError:
            print("PyTorch not available, using CPU")

        # Extract settings
        seed = settings.get('seed', 1)
        ss_steps = settings.get('ss_steps', 12)
        ss_cfg_strength = settings.get('ss_cfg_strength', 7.5)
        slat_steps = settings.get('slat_steps', 12)
        slat_cfg_strength = settings.get('slat_cfg_strength', 3.0)
        simplify = settings.get('simplify', 0.95)
        texture_size = settings.get('texture_size', 1024)

        print(f"Running Trellis pipeline with settings: {settings}")

        # Run the pipeline exactly like the official example
        outputs = pipeline.run(
            image,
            seed=seed,
            sparse_structure_sampler_params={
                "steps": ss_steps,
                "cfg_strength": ss_cfg_strength,
            },
            slat_sampler_params={
                "steps": slat_steps,
                "cfg_strength": slat_cfg_strength,
            },
        )

        print("Pipeline execution completed")

        # Generate unique output filenames
        output_id = str(uuid.uuid4())[:8]
        base_name = f"trellis_output_{output_id}"

        # Create output directory
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        # Save Gaussian as PLY
        print("Saving Gaussian PLY...")
        gaussian_ply_path = output_path / f"{base_name}_gaussian.ply"
        outputs['gaussian'][0].save_ply(str(gaussian_ply_path))

        # Generate GLB file with simplified approach
        glb_path = None
        try:
            print("Generating GLB file...")

            # Use simplified GLB generation that bypasses CUDA compilation issues
            glb = postprocessing_utils.to_glb(
                outputs['gaussian'][0],
                outputs['mesh'][0],
                simplify=simplify,
                fill_holes=False,  # Skip problematic hole filling
                texture_size=texture_size,
            )

            glb_path = output_path / f"{base_name}.glb"
            glb.export(str(glb_path))
            print("GLB file generated successfully")

        except Exception as e:
            error_msg = str(e)
            if any(keyword in error_msg for keyword in ["Ninja", "_get_vc_env", "distutils", "CUDA"]):
                print(f"Warning: Skipping GLB generation due to compilation issue: {error_msg}")
                print("PLY and video files generated successfully")
                glb_path = None
            else:
                print(f"Warning: GLB generation failed with error: {error_msg}")
                print("PLY and video files generated successfully")
                glb_path = None

        # Render videos (we'll skip this since user doesn't need it)
        print("Rendering videos...")
        gaussian_video = render_utils.render_video(outputs['gaussian'][0])['color']
        mesh_video = render_utils.render_video(outputs['mesh'][0])['normal']

        # Save videos
        gaussian_video_path = output_path / f"{base_name}_gaussian.mp4"
        mesh_video_path = output_path / f"{base_name}_mesh.mp4"

        imageio.mimsave(str(gaussian_video_path), gaussian_video, fps=30)
        imageio.mimsave(str(mesh_video_path), mesh_video, fps=30)

        print("Videos rendered successfully")

        # Return results
        result = {
            'gaussian': str(gaussian_ply_path),
            'video': str(gaussian_video_path),
            'mesh_video': str(mesh_video_path)
        }

        if glb_path:
            result['glb'] = str(glb_path)

        print(f"Processing completed successfully. Files: {result}")
        return result

    except Exception as e:
        error_msg = f"Error processing image: {str(e)}"
        print(error_msg)
        import traceback
        traceback.print_exc()
        return {"error": error_msg}


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Process image with Trellis pipeline')
    parser.add_argument('--image_path', required=True, help='Path to input image')
    parser.add_argument('--output_dir', required=True, help='Output directory')
    parser.add_argument('--settings', required=True, help='JSON string with settings')
    parser.add_argument('--result_file', required=True, help='File to write results to')

    args = parser.parse_args()

    # Add ninja executable to PATH for GLB generation
    ninja_path = Path(__file__).parent.parent.parent / "Resources" / "ninja"
    if ninja_path.exists():
        os.environ['PATH'] = str(ninja_path) + os.pathsep + os.environ.get('PATH', '')

    # Parse settings
    try:
        settings = json.loads(args.settings)
    except json.JSONDecodeError as e:
        settings = {}
        print(f"Warning: Could not parse settings JSON: {e}")

    # Process the image
    result = process_image_with_trellis(args.image_path, args.output_dir, settings)

    # Write result to file
    with open(args.result_file, 'w') as f:
        json.dump(result, f, indent=2)

    print(f"Results written to: {args.result_file}")
