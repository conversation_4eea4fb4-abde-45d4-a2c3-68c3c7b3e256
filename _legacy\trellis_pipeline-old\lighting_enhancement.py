"""
Lighting and Shadow Enhancement Module for 3D Models

This module provides various techniques to reduce dark areas and shadows
on generated 3D models, improving their visual appearance.
"""

import numpy as np
import trimesh
import trimesh.visual
from PIL import Image
import cv2
from typing import Dict, Tuple, Optional, Union
import colorsys
import json
import os
from pathlib import Path


class LightingEnhancer:
    """
    A class to enhance lighting and reduce shadows on 3D models.
    """
    
    def __init__(self):
        self.default_settings = {
            'ambient_boost': 0.08,          # Gentle ambient lighting increase
            'shadow_softening': 0.15,      # Minimal shadow reduction
            'gamma_correction': 1.05,      # Very subtle brightening
            'material_brightness': 0.05,   # Minimal material boost
            'contrast_enhancement': 1.01,  # Minimal contrast boost
            'saturation_boost': 1.0,       # No saturation change
        }
    
    def enhance_model(self, mesh: trimesh.Trimesh, settings: Optional[Dict] = None) -> trimesh.Trimesh:
        """
        Apply comprehensive lighting enhancement to a 3D model.
        
        Args:
            mesh: Input trimesh object
            settings: Enhancement settings dictionary
            
        Returns:
            Enhanced trimesh object
        """
        if settings is None:
            settings = self.default_settings.copy()
        
        print("Applying lighting enhancements to 3D model...")
        
        # Create a copy to avoid modifying the original
        enhanced_mesh = mesh.copy()
        
        # Apply material enhancements
        enhanced_mesh = self._enhance_materials(enhanced_mesh, settings)
        
        # Apply texture enhancements if texture exists
        if hasattr(enhanced_mesh.visual, 'material') and enhanced_mesh.visual.material:
            enhanced_mesh = self._enhance_texture(enhanced_mesh, settings)
        
        # Apply vertex color enhancements if vertex colors exist
        if hasattr(enhanced_mesh.visual, 'vertex_colors') and enhanced_mesh.visual.vertex_colors is not None:
            enhanced_mesh = self._enhance_vertex_colors(enhanced_mesh, settings)
        
        print("✓ Lighting enhancements applied successfully")
        return enhanced_mesh
    
    def _enhance_materials(self, mesh: trimesh.Trimesh, settings: Dict) -> trimesh.Trimesh:
        """
        Enhance material properties to reduce dark areas.
        """
        if not hasattr(mesh.visual, 'material') or mesh.visual.material is None:
            return mesh
        
        material = mesh.visual.material
        
        # Enhance PBR material properties
        if hasattr(material, 'roughnessFactor'):
            # Reduce roughness slightly to increase reflectivity
            if material.roughnessFactor is not None:
                material.roughnessFactor = max(0.1, material.roughnessFactor * 0.8)
            else:
                material.roughnessFactor = 0.7  # Default value

        if hasattr(material, 'metallicFactor'):
            # Slight metallic boost for better light reflection
            if material.metallicFactor is not None:
                material.metallicFactor = min(1.0, material.metallicFactor * 1.1)
            else:
                material.metallicFactor = 0.1  # Default value
        
        # Enhance base color factor
        if hasattr(material, 'baseColorFactor'):
            if material.baseColorFactor is not None:
                base_color = np.array(material.baseColorFactor, dtype=np.float32)
                if len(base_color) >= 3:
                    # Convert to HSL, increase lightness, convert back
                    rgb = base_color[:3] / 255.0 if base_color.max() > 1.0 else base_color[:3]
                    hsl = self._rgb_to_hsl(rgb)

                    # Increase lightness
                    hsl[2] = min(1.0, hsl[2] + settings['material_brightness'])

                    # Convert back to RGB
                    enhanced_rgb = self._hsl_to_rgb(hsl)
                    base_color[:3] = enhanced_rgb * (255.0 if base_color.max() > 1.0 else 1.0)

                    material.baseColorFactor = base_color.astype(np.uint8 if base_color.max() > 1.0 else np.float32)
            else:
                # Set default base color factor
                material.baseColorFactor = np.array([200, 200, 200, 255], dtype=np.uint8)
        
        return mesh
    
    def _enhance_texture(self, mesh: trimesh.Trimesh, settings: Dict) -> trimesh.Trimesh:
        """
        Enhance texture to reduce dark areas and improve lighting.
        """
        if not hasattr(mesh.visual, 'material') or not hasattr(mesh.visual.material, 'baseColorTexture'):
            return mesh
        
        texture = mesh.visual.material.baseColorTexture
        if texture is None:
            return mesh
        
        # Convert PIL Image to numpy array
        if isinstance(texture, Image.Image):
            texture_array = np.array(texture)
        else:
            texture_array = texture
        
        # Apply enhancements
        enhanced_texture = self._enhance_image(texture_array, settings)
        
        # Convert back to PIL Image
        enhanced_texture_pil = Image.fromarray(enhanced_texture.astype(np.uint8))
        
        # Update the material
        mesh.visual.material.baseColorTexture = enhanced_texture_pil
        
        return mesh
    
    def _enhance_vertex_colors(self, mesh: trimesh.Trimesh, settings: Dict) -> trimesh.Trimesh:
        """
        Enhance vertex colors to reduce dark areas.
        """
        if mesh.visual.vertex_colors is None:
            return mesh
        
        colors = mesh.visual.vertex_colors.copy()
        
        # Convert to float for processing
        if colors.dtype == np.uint8:
            colors = colors.astype(np.float32) / 255.0
        
        # Apply gamma correction
        gamma = settings['gamma_correction']
        colors[:, :3] = np.power(colors[:, :3], 1.0 / gamma)
        
        # Apply ambient boost
        ambient_boost = settings['ambient_boost']
        colors[:, :3] = np.clip(colors[:, :3] + ambient_boost, 0.0, 1.0)
        
        # Apply contrast enhancement
        contrast = settings['contrast_enhancement']
        colors[:, :3] = np.clip((colors[:, :3] - 0.5) * contrast + 0.5, 0.0, 1.0)
        
        # Convert back to original format
        if mesh.visual.vertex_colors.dtype == np.uint8:
            colors = (colors * 255).astype(np.uint8)
        
        mesh.visual.vertex_colors = colors
        
        return mesh
    
    def _enhance_image(self, image: np.ndarray, settings: Dict) -> np.ndarray:
        """
        Apply color-preserving image enhancement techniques to reduce dark areas.
        """
        # Convert to float for processing
        img = image.astype(np.float32) / 255.0

        # Only apply enhancements if they're significant enough
        gamma = settings['gamma_correction']
        ambient_boost = settings['ambient_boost']
        contrast = settings['contrast_enhancement']
        saturation_boost = settings['saturation_boost']

        # Skip processing if all settings are minimal
        if (abs(gamma - 1.0) < 0.02 and ambient_boost < 0.02 and
            abs(contrast - 1.0) < 0.02 and abs(saturation_boost - 1.0) < 0.02):
            return image.astype(np.float32)

        # Apply very gentle gamma correction only to dark areas
        if abs(gamma - 1.0) > 0.01:
            # Create a mask for dark areas only
            luminance = np.mean(img[:, :, :3], axis=2) if len(img.shape) == 3 else img
            dark_mask = luminance < 0.4  # Only affect pixels darker than 40%

            # Apply gamma correction only to dark areas
            gamma_corrected = np.power(img, 1.0 / gamma)
            img = np.where(dark_mask[..., np.newaxis], gamma_corrected, img)

        # Apply very conservative shadow softening using LAB color space
        if len(img.shape) == 3 and img.shape[2] >= 3 and settings['shadow_softening'] > 0.05:
            # Convert to LAB color space for better luminance control
            img_lab = cv2.cvtColor((img * 255).astype(np.uint8), cv2.COLOR_RGB2LAB)

            # Apply very gentle CLAHE only to L channel
            clip_limit = 1.0 + settings['shadow_softening']  # Much more conservative
            clahe = cv2.createCLAHE(clipLimit=clip_limit, tileGridSize=(16, 16))  # Larger tiles for smoother effect

            # Store original L channel
            original_l = img_lab[:, :, 0].copy()
            enhanced_l = clahe.apply(img_lab[:, :, 0])

            # Blend original and enhanced based on how dark the area is
            luminance_norm = original_l.astype(np.float32) / 255.0
            blend_factor = (1.0 - luminance_norm) * settings['shadow_softening']  # More enhancement for darker areas
            img_lab[:, :, 0] = (original_l * (1 - blend_factor) + enhanced_l * blend_factor).astype(np.uint8)

            # Convert back to RGB
            img = cv2.cvtColor(img_lab, cv2.COLOR_LAB2RGB).astype(np.float32) / 255.0

        # Apply very gentle ambient boost only to dark areas
        if ambient_boost > 0.01:
            luminance = np.mean(img[:, :, :3], axis=2) if len(img.shape) == 3 else img
            dark_mask = luminance < 0.3  # Only boost very dark areas
            boost_strength = dark_mask * ambient_boost * (1.0 - luminance)  # Stronger boost for darker areas
            img = np.clip(img + boost_strength[..., np.newaxis], 0.0, 1.0)

        # Apply minimal contrast enhancement
        if abs(contrast - 1.0) > 0.005:
            img = np.clip((img - 0.5) * contrast + 0.5, 0.0, 1.0)

        # Apply saturation adjustment only if significant
        if len(img.shape) == 3 and img.shape[2] >= 3 and abs(saturation_boost - 1.0) > 0.005:
            # Use vectorized HSV conversion for better performance and color preservation
            img_hsv = cv2.cvtColor((img * 255).astype(np.uint8), cv2.COLOR_RGB2HSV).astype(np.float32)
            img_hsv[:, :, 1] = np.clip(img_hsv[:, :, 1] * saturation_boost, 0, 255)
            img = cv2.cvtColor(img_hsv.astype(np.uint8), cv2.COLOR_HSV2RGB).astype(np.float32) / 255.0

        return (img * 255).astype(np.float32)
    
    def _rgb_to_hsl(self, rgb: np.ndarray) -> np.ndarray:
        """Convert RGB to HSL color space."""
        r, g, b = rgb[0], rgb[1], rgb[2]
        h, l, s = colorsys.rgb_to_hls(r, g, b)
        return np.array([h, s, l])
    
    def _hsl_to_rgb(self, hsl: np.ndarray) -> np.ndarray:
        """Convert HSL to RGB color space."""
        h, s, l = hsl[0], hsl[1], hsl[2]
        r, g, b = colorsys.hls_to_rgb(h, l, s)
        return np.array([r, g, b])


def load_lighting_config(preset_name: str = "balanced") -> Dict:
    """
    Load lighting enhancement configuration from preset.

    Args:
        preset_name: Name of the preset to load ('conservative', 'balanced', 'aggressive', 'disabled')

    Returns:
        Dictionary with lighting enhancement settings
    """
    try:
        config_path = Path(__file__).parent / "lighting_config.json"

        if not config_path.exists():
            print(f"Warning: Lighting config file not found at {config_path}, using defaults")
            return LightingEnhancer().default_settings

        with open(config_path, 'r') as f:
            config = json.load(f)

        presets = config.get('lighting_enhancement_settings', {}).get('presets', {})

        if preset_name in presets:
            return presets[preset_name]
        else:
            available_presets = list(presets.keys())
            print(f"Warning: Preset '{preset_name}' not found. Available presets: {available_presets}")
            print("Using 'balanced' preset as fallback")
            return presets.get('balanced', LightingEnhancer().default_settings)

    except Exception as e:
        print(f"Error loading lighting config: {e}")
        print("Using default settings")
        return LightingEnhancer().default_settings


def enhance_model_lighting(mesh: trimesh.Trimesh, settings: Optional[Dict] = None, preset: str = None) -> trimesh.Trimesh:
    """
    Convenience function to enhance lighting on a 3D model.

    Args:
        mesh: Input trimesh object
        settings: Enhancement settings dictionary (overrides preset)
        preset: Preset name to use ('conservative', 'balanced', 'aggressive', 'disabled')

    Returns:
        Enhanced trimesh object
    """
    enhancer = LightingEnhancer()

    # Load preset if specified and no custom settings provided
    if preset and settings is None:
        settings = load_lighting_config(preset)
    elif settings is None:
        settings = load_lighting_config("pytorch3d_balanced")  # Default to PyTorch3D optimization

    return enhancer.enhance_model(mesh, settings)
