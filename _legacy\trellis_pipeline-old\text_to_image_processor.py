"""
Text-to-Image Processor Script

This script runs in a separate environment with diffusers installed
and generates images from text prompts using FLUX Schnell or Stable Diffusion.
"""

import os
import sys
import json
import argparse
import uuid
from pathlib import Path

def main():
    parser = argparse.ArgumentParser(description='Text to Image Processor')
    parser.add_argument('--prompt', type=str, required=True, help='Text prompt for image generation')
    parser.add_argument('--output_dir', type=str, required=True, help='Output directory for generated images')
    parser.add_argument('--result_file', type=str, required=True, help='File to write results to')
    parser.add_argument('--model_type', type=str, default='sdxl_turbo', help='Model type to use')
    parser.add_argument('--enhance_for_3d', action='store_true', help='Whether to enhance prompt for 3D generation')
    args = parser.parse_args()

    try:
        # Import required modules
        import torch
        from diffusers import StableDiffusionPipeline, DPMSolverMultistepScheduler
        from PIL import Image
        import json
        import uuid
        import os

        # Load settings from result file if it exists
        settings = {}
        if os.path.exists(args.result_file):
            try:
                with open(args.result_file, 'r') as f:
                    settings = json.load(f)
            except:
                pass

        # Initialize pipeline based on model type
        if args.model_type == "flux":
            try:
                print("Loading FLUX Schnell pipeline...")
                from diffusers import FluxSchnellPipeline
                pipeline = FluxSchnellPipeline.from_pretrained(
                    "stabilityai/flux-schnell",
                    torch_dtype=torch.float16,
                    use_safetensors=True
                )
                if torch.cuda.is_available():
                    pipeline.to("cuda")
                    print(f"FLUX pipeline loaded on GPU: {torch.cuda.get_device_name(0)}")
                else:
                    print("CUDA not available, using CPU for FLUX")
                model_type = "flux"
                print("FLUX pipeline loaded successfully!")
            except Exception as e:
                print(f"Failed to load FLUX pipeline: {e}")
                flux_available = False
        else:
            flux_available = False

        if not flux_available:
            # Use regular Stable Diffusion instead of SDXL Turbo
            try:
                print("Loading Stable Diffusion pipeline...")

                # Use regular SD 1.5 which is more stable and doesn't require authentication
                pipeline = StableDiffusionPipeline.from_pretrained(
                    "runwayml/stable-diffusion-v1-5",
                    torch_dtype=torch.float16,
                    safety_checker=None,
                    requires_safety_checker=False
                )

                # Use DPM solver for better quality
                pipeline.scheduler = DPMSolverMultistepScheduler.from_config(
                    pipeline.scheduler.config
                )

                if torch.cuda.is_available():
                    pipeline.to("cuda")
                    print(f"Stable Diffusion pipeline loaded on GPU: {torch.cuda.get_device_name(0)}")
                else:
                    print("CUDA not available, using CPU for Stable Diffusion")

                model_type = "stable_diffusion"
                print("Stable Diffusion pipeline loaded successfully!")

            except Exception as e2:
                error_msg = f"Failed to load Stable Diffusion pipeline: {e2}"
                print(error_msg)
                with open(args.result_file, 'w') as f:
                    json.dump({'error': error_msg}, f)
                return 1
        
        # Only enhance prompt for 3D if specifically requested
        if args.enhance_for_3d:
            enhanced_prompt = f"A detailed 3D render of {args.prompt}, studio lighting, white background, centered object, high quality, photorealistic, clean background, single object"
            print(f"Enhanced prompt for 3D: {enhanced_prompt}")
        else:
            enhanced_prompt = args.prompt
            print(f"Using original prompt: {args.prompt}")
        
        print(f"Using model: {model_type}")
        
        # Generate image
        try:
            # Set seed for reproducibility
            seed = settings.get('seed', None)
            if seed is not None:
                generator = torch.Generator(device="cuda" if torch.cuda.is_available() else "cpu").manual_seed(seed)
            else:
                generator = None
            
            if model_type == "flux":
                # FLUX Schnell parameters
                image = pipeline(
                    prompt=enhanced_prompt,
                    generator=generator,
                    num_inference_steps=settings.get('num_inference_steps', 4),
                    guidance_scale=settings.get('guidance_scale', 0.0),
                    width=settings.get('width', 1024),
                    height=settings.get('height', 1024),
                ).images[0]
            else:
                # Stable Diffusion parameters
                negative_prompt = "blurry, low quality, cropped, cut off, text, watermark, signature, multiple objects, cluttered background, dark, shadows"

                # Use simpler parameters for SD 1.5 compatibility
                image = pipeline(
                    prompt=enhanced_prompt,
                    negative_prompt=negative_prompt,
                    generator=generator,
                    num_inference_steps=settings.get('num_inference_steps', 20),
                    guidance_scale=settings.get('guidance_scale', 7.5),
                    width=settings.get('width', 512),  # SD 1.5 works better with 512x512
                    height=settings.get('height', 512),
                ).images[0]
            
            # Save the generated image
            image_id = str(uuid.uuid4())[:8]
            image_filename = f"text_to_image_{image_id}.png"
            image_path = os.path.join(args.output_dir, image_filename)
            
            # Ensure output directory exists
            os.makedirs(args.output_dir, exist_ok=True)
            
            image.save(image_path)
            print(f"Image saved to: {image_path}")
            
            # Write results
            result = {
                'image_path': image_path,
                'prompt': enhanced_prompt,
                'original_prompt': args.prompt,
                'model_type': model_type
            }
            
            with open(args.result_file, 'w') as f:
                json.dump(result, f)
            
            print("Text-to-image generation completed successfully!")
            return 0
            
        except Exception as e:
            error_msg = f"Error during image generation: {str(e)}"
            print(error_msg)
            import traceback
            traceback.print_exc()
            with open(args.result_file, 'w') as f:
                json.dump({'error': error_msg}, f)
            return 1
            
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(error_msg)
        import traceback
        traceback.print_exc()
        with open(args.result_file, 'w') as f:
            json.dump({'error': error_msg}, f)
        return 1

if __name__ == "__main__":
    sys.exit(main())
