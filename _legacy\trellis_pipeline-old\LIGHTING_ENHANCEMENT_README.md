# 3D Model Lighting Enhancement System

This system provides advanced lighting and shadow reduction techniques for 3D models generated by the Trellis pipeline. It automatically reduces dark areas, softens shadows, and improves overall model appearance.

## Features

### 🌟 **Automatic Enhancements**
- **Ambient Lighting Boost**: Brightens dark areas without overexposing highlights
- **Shadow Softening**: Reduces harsh shadows and dark crevices
- **Gamma Correction**: Improves overall brightness and visibility
- **Material Enhancement**: Optimizes material properties for better light distribution
- **Texture Processing**: Reduces dark areas in textures using advanced image processing
- **Multi-Light Setup**: Configures materials for optimal multi-light environments

### 🎛️ **Configurable Presets**
- **Conservative**: Subtle improvements with minimal changes
- **Balanced**: Recommended default for most use cases
- **Aggressive**: Strong improvements for very dark models
- **Disabled**: Turn off all enhancements

## How It Works

The lighting enhancement system is automatically integrated into the Trellis pipeline and applies the following techniques:

### 1. **Ambient Occlusion Adjustment**
- Reduces SSAO (Screen Space Ambient Occlusion) effects
- Minimizes dark areas in model crevices and corners

### 2. **Material Property Enhancement**
- Adjusts roughness and metallic factors for better light reflection
- Increases base color brightness in HSL color space
- Adds subtle emissive factors to simulate ambient lighting

### 3. **Multi-Light Rendering Setup**
- Optimizes materials for multi-directional lighting
- Reduces dependency on single light sources
- Improves light distribution across the model surface

### 4. **Shadow Softening**
- Uses bilateral filtering to preserve edges while smoothing shadows
- Applies adaptive histogram equalization to reduce dark areas
- Maintains detail while reducing harsh lighting transitions

### 5. **Post-Processing with Gamma Correction**
- Applies gamma correction to brighten dark areas
- Enhances contrast while preserving details
- Boosts saturation for more vibrant colors

## Usage

### **Automatic Integration**
The lighting enhancement system is automatically applied to all 3D models generated through the Trellis pipeline. No additional configuration is required for basic usage.

### **Customizing Enhancement Levels**
You can customize the enhancement level by modifying the settings in your generation request:

```python
# Example settings for different enhancement levels
settings = {
    # Standard Trellis settings
    'seed': 42,
    'ss_steps': 12,
    'slat_steps': 12,
    'simplify': 0.95,
    'texture_size': 1024,
    
    # Lighting enhancement settings
    'ambient_boost': 0.25,        # Increase ambient lighting (0.0-0.5)
    'shadow_softening': 0.3,      # Reduce shadow intensity (0.0-0.8)
    'gamma_correction': 1.15,     # Brighten overall appearance (0.8-2.0)
    'material_brightness': 0.15,  # Boost material colors (0.0-0.4)
    'contrast_enhancement': 1.05, # Enhance contrast (0.8-1.5)
    'saturation_boost': 1.02,     # Boost color saturation (0.8-1.3)
}
```

### **Using Presets**
You can also use predefined presets for common scenarios:

```python
# Load a specific preset
from pipelines.trellis_pipeline.lighting_enhancement import load_lighting_config

# Available presets: 'conservative', 'balanced', 'aggressive', 'disabled'
settings = load_lighting_config('balanced')
```

## Configuration File

The system uses a JSON configuration file (`lighting_config.json`) that defines presets and parameter ranges:

### **Available Presets:**

1. **Conservative** (`conservative`)
   - Subtle improvements with minimal changes
   - Best for models that are already well-lit
   - Ambient boost: 0.15, Gamma: 1.1

2. **Balanced** (`balanced`) - **Recommended Default**
   - Balanced improvements for most use cases
   - Good for models with moderate shadow issues
   - Ambient boost: 0.25, Gamma: 1.15

3. **Aggressive** (`aggressive`)
   - Strong improvements for very dark models
   - Best for models with heavy shadows
   - Ambient boost: 0.4, Gamma: 1.3

4. **Disabled** (`disabled`)
   - Turns off all lighting enhancements
   - Uses original Trellis output

## Testing

Run the test script to verify the lighting enhancement system:

```bash
cd pipelines/trellis_pipeline
python test_lighting_enhancement.py
```

This will:
- Create test meshes with dark areas and shadows
- Apply different enhancement presets
- Save comparison GLB files to `test_output/` directory
- Verify that all enhancement modules work correctly

## Troubleshooting

### **Model appears too bright:**
- Reduce `ambient_boost` and `gamma_correction` values
- Use 'conservative' preset
- Set `ambient_boost` to 0.1-0.15

### **Model still too dark:**
- Increase `ambient_boost` and `gamma_correction` values
- Use 'aggressive' preset
- Set `ambient_boost` to 0.3-0.4

### **Colors look washed out:**
- Reduce `contrast_enhancement` (try 1.0-1.02)
- Increase `saturation_boost` (try 1.05-1.1)
- Reduce `material_brightness`

### **Details are lost:**
- Reduce `shadow_softening` (try 0.1-0.2)
- Reduce `material_brightness` (try 0.05-0.1)
- Use 'conservative' preset

### **Unnatural appearance:**
- Use 'conservative' preset
- Reduce all enhancement values by 50%
- Consider using 'disabled' preset for comparison

## Technical Details

### **Dependencies:**
- `numpy`: Numerical operations
- `trimesh`: 3D mesh processing
- `PIL (Pillow)`: Image processing
- `opencv-python`: Advanced image processing
- `colorsys`: Color space conversions

### **Performance:**
- Minimal performance impact (< 5% additional processing time)
- All enhancements are applied during the existing GLB generation step
- No additional GPU memory requirements

### **Compatibility:**
- Works with all Trellis-generated models
- Compatible with existing GLB export workflow
- Preserves original model geometry and topology
- Maintains texture UV coordinates and material properties

## Integration Points

The lighting enhancement system integrates at the following points in the pipeline:

1. **`trellis_pipeline.py`**: Main pipeline integration
2. **`trellis_processor.py`**: Subprocess integration for Trellis environment
3. **`enhanced_postprocessing.py`**: Core post-processing wrapper
4. **`lighting_enhancement.py`**: Core enhancement algorithms

The system automatically falls back to standard Trellis processing if the enhancement modules are not available, ensuring robust operation.
