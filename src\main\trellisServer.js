const { spawn } = require('child_process');
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const net = require('net');
const path = require('path');

const TRELLIS_PORT = 7960;
const TRELLIS_HOST = '127.0.0.1';
const RUN_BAT = path.join(__dirname, '../../pipelines/3DPipelines/gen3d/trellis-stable-projectorz-101/run-fp16.bat');
const OUTPUT_DIR = path.join(__dirname, '../../output');

// Connection attempt counter to reduce log spam
let connectionAttemptCounter = 0;

// Automated Python process cleanup function
async function cleanupPythonProcesses() {
  console.log('[Cleanup] Checking for lingering Python processes...');

  return new Promise((resolve) => {
    // First, check if there are any Python processes running
    const checkProcess = spawn('cmd.exe', ['/c', 'tasklist | findstr python.exe'], {
      stdio: 'pipe',
      windowsHide: true
    });

    let output = '';
    checkProcess.stdout.on('data', (data) => {
      output += data.toString();
    });

    checkProcess.on('close', (code) => {
      if (output.trim() && output.includes('python.exe')) {
        console.log('[Cleanup] Found Python processes, killing them...');
        console.log('[Cleanup] Processes found:', output.trim());

        // Kill all Python processes
        const killProcess = spawn('cmd.exe', ['/c', 'taskkill /F /IM python.exe'], {
          stdio: 'pipe',
          windowsHide: true
        });

        killProcess.on('close', (killCode) => {
          if (killCode === 0) {
            console.log('[Cleanup] Successfully killed Python processes');
          } else {
            console.log('[Cleanup] Some Python processes may still be running (exit code:', killCode, ')');
          }

          // Wait a moment for processes to fully terminate
          setTimeout(() => {
            resolve();
          }, 2000);
        });

        killProcess.on('error', (err) => {
          console.warn('[Cleanup] Error killing Python processes:', err.message);
          resolve();
        });
      } else {
        console.log('[Cleanup] No Python processes found');
        resolve();
      }
    });

    checkProcess.on('error', (err) => {
      console.warn('[Cleanup] Error checking for Python processes:', err.message);
      resolve();
    });
  });
}

function isTrellisRunning() {
  console.log('[Trellis Server] Checking if server is running on', TRELLIS_HOST + ':' + TRELLIS_PORT);
  return new Promise((resolve) => {
    const socket = net.createConnection(TRELLIS_PORT, TRELLIS_HOST);
    socket.on('connect', () => {
      console.log('[Trellis Server] Server is running - connection successful');
      connectionAttemptCounter = 0; // Reset counter on successful connection
      socket.end();
      resolve(true);
    });
    socket.on('error', (error) => {
      connectionAttemptCounter++;
      // Only log every 10th connection failure to reduce spam
      if (connectionAttemptCounter % 10 === 0) {
        console.log(`[Trellis Server] Server not running - connection failed (${connectionAttemptCounter} attempts):`, error.code);
      }
      resolve(false);
    });
  });
}

function startTrellisServer(progressCb = null) {
  console.log('[Trellis Server] Starting server...');
  console.log('[Trellis Server] RUN_BAT:', RUN_BAT);
  console.log('[Trellis Server] Batch file exists:', fs.existsSync(RUN_BAT));

  if (!fs.existsSync(RUN_BAT)) {
    throw new Error('Trellis run-fp16.bat not found at: ' + RUN_BAT);
  }
  const batDir = path.dirname(RUN_BAT);
  console.log('[Trellis Server] Working directory:', batDir);
  console.log('[Trellis Server] Command: cmd.exe /c', path.basename(RUN_BAT));

  // Store the progress callback globally so it can be used during generation
  globalProgressCallback = progressCb;

  // Use cmd.exe with specific flags to prevent window showing
  const child = spawn('cmd.exe', ['/c', path.basename(RUN_BAT)], {
    cwd: batDir,
    detached: false,  // Changed to false so we can capture output
    stdio: ['ignore', 'pipe', 'pipe'],
    windowsHide: true,
    shell: false,
    env: {
      ...process.env,
      // Set environment variables to minimize window visibility
      PYTHONUNBUFFERED: '1'
    }
  });
  // Capture and display all server output in main application logs
  child.stdout.on('data', (data) => {
    const output = data.toString();
    const lines = output.split('\n').filter(line => line.trim());
    lines.forEach(line => {
      console.log('[Trellis Server] ' + line.trim());
      if (typeof logger !== 'undefined') logger.info('[Trellis Server] ' + line.trim());

      // Check for port binding errors
      if (line.includes('error while attempting to bind') || line.includes('Address already in use')) {
        console.error('[Trellis Server] PORT CONFLICT DETECTED - Another server is using port 7960');
        console.error('[Trellis Server] Please close any other Trellis servers and try again');
      }
    });

    // Parse progress from server output if we have a callback
    if (globalProgressCallback) {
      console.log('[Progress Debug] Parsing output with globalProgressCallback set');
      parseProgressFromOutput(output, globalProgressCallback);
    } else {
      console.log('[Progress Debug] No globalProgressCallback set, skipping progress parsing');
    }
  });

  child.stderr.on('data', (data) => {
    const lines = data.toString().split('\n').filter(line => line.trim());
    lines.forEach(line => {
      console.error('[Trellis Server] ' + line.trim());
      if (typeof logger !== 'undefined') logger.error('[Trellis Server] ' + line.trim());

      // Check for port binding errors in stderr too
      if (line.includes('error while attempting to bind') || line.includes('Address already in use')) {
        console.error('[Trellis Server] PORT CONFLICT DETECTED - Another server is using port 7960');
        console.error('[Trellis Server] Please close any other Trellis servers and try again');
      }
    });
  });
  // Handle process events and log them to the main application logs
  child.on('close', (code) => {
    console.log(`[Trellis Server] Process exited with code ${code}`);
    if (typeof logger !== 'undefined') logger.info(`[Trellis Server] Process exited with code ${code}`);
  });

  child.on('error', (error) => {
    console.error('[Trellis Server] Process error:', error);
    if (typeof logger !== 'undefined') logger.error('[Trellis Server] Process error: ' + error.message);
  });

  // Store the child process reference globally so we can check if it's still running
  global.trellisProcess = child;
}

async function waitForTrellisReady(timeoutMs = null, progressCb) {
  const start = Date.now();
  while (true) {
    if (await isTrellisRunning()) return true;
    if (progressCb) progressCb({ stage: 'trellis', message: 'Waiting for Trellis server to start...' });
    await new Promise(r => setTimeout(r, 2000));
    // No timeout: wait forever
  }
}

// Global progress callback for server output parsing
let globalProgressCallback = null;

function parseProgressFromOutput(output, progressCb) {
  if (!progressCb) {
    console.log('[Progress Debug] No progressCb provided, skipping parsing');
    return;
  }

  const lines = output.split('\n');
  for (const line of lines) {
    const trimmedLine = line.trim();
    if (!trimmedLine) continue;

    // Debug: Log all non-empty lines to see what we're processing
    if (trimmedLine.includes('Sampling:') || trimmedLine.includes('Decimating') || trimmedLine.includes('Rasterizing') || trimmedLine.includes('Rendering') || trimmedLine.includes('Texture baking')) {
      console.log(`[Progress Debug] Raw progress line: "${trimmedLine}"`);
    }

    // Remove [Trellis Server] prefix if present
    const cleanLine = trimmedLine.replace(/^\[Trellis Server\]\s*/, '');

    // Debug: Log lines that might contain progress information after cleaning
    if (cleanLine.includes('Sampling:') || cleanLine.includes('Decimating') || cleanLine.includes('Rasterizing') || cleanLine.includes('Rendering') || cleanLine.includes('Texture baking')) {
      console.log(`[Progress Debug] Processing potential progress line: "${cleanLine}"`);
    }

    // Parse different progress patterns from the log
    let progress = 0;
    let stage = '';
    let description = '';

    // Sampling progress: "Sampling:   8%|8         | 1/12 [00:00<00:05,  2.13it/s]" or "[Trellis Server] Sampling:   8%|8..."
    const samplingMatch = cleanLine.match(/Sampling:\s*(\d+)%/);
    if (samplingMatch) {
      progress = parseInt(samplingMatch[1]);
      stage = 'sparse_structure';
      description = `Generating 3D structure (Sampling ${progress}%)`;
      console.log(`[Progress Debug] Sampling match found: ${progress}% -> stage: ${stage}, progress: ${Math.min(progress * 0.4, 40)}`);
      progressCb({ stage, progress: Math.min(progress * 0.4, 40), message: description });
      continue;
    } else if (cleanLine.includes('Sampling:')) {
      console.log(`[Progress Debug] Sampling line found but regex failed: "${cleanLine}"`);
    }

    // Decimating mesh: "Decimating Mesh: 10%|# [00:00<00:01]" or "[Trellis Server] Decimating Mesh: 10%|#..."
    const decimatingMatch = cleanLine.match(/Decimating Mesh:\s*(\d+)%/);
    if (decimatingMatch) {
      progress = parseInt(decimatingMatch[1]);
      stage = 'slat_generation';
      description = `Processing and decimating mesh (${progress}%)`;
      console.log(`[Progress Debug] Decimating match found: ${progress}% -> stage: ${stage}, progress: ${40 + Math.min(progress * 0.1, 10)}`);
      progressCb({ stage, progress: 40 + Math.min(progress * 0.1, 10), message: description });
      continue;
    }

    // Rasterizing: "Rasterizing: 3%|2 | 27/1000 [00:00<00:03, 266.56it/s]" or "[Trellis Server] Rasterizing: 3%|2..."
    const rasterizingMatch = cleanLine.match(/Rasterizing:\s*(\d+)%.*?(\d+)\/(\d+)/);
    if (rasterizingMatch) {
      const current = parseInt(rasterizingMatch[2]);
      const total = parseInt(rasterizingMatch[3]);
      progress = Math.round((current / total) * 100);
      stage = 'mesh_creation';
      description = `Rasterizing mesh (${current}/${total})`;
      console.log(`[Progress Debug] Rasterizing match found: ${progress}% -> stage: ${stage}, progress: ${50 + Math.min(progress * 0.15, 15)}`);
      progressCb({ stage, progress: 50 + Math.min(progress * 0.15, 15), message: description });
      continue;
    }

    // Rendering: "Rendering: 54it [00:01, 28.38it/s]" or "[Trellis Server] Rendering: 54it..."
    const renderingMatch = cleanLine.match(/Rendering:\s*(\d+)it/);
    if (renderingMatch) {
      const iterations = parseInt(renderingMatch[1]);
      progress = Math.min((iterations / 100) * 100, 100);
      stage = 'mesh_creation';
      description = `Rendering mesh (${iterations} iterations)`;
      console.log(`[Progress Debug] Rendering match found: ${iterations} iterations -> stage: ${stage}, progress: ${65 + Math.min(progress * 0.1, 10)}`);
      progressCb({ stage, progress: 65 + Math.min(progress * 0.1, 10), message: description });
      continue;
    }

    // Texture baking UV: "Texture baking (opt): UV: 38%|###8 | 38/100" or "[Trellis Server] Texture baking (opt): UV: 38%|###8..."
    const textureBakingUVMatch = cleanLine.match(/Texture baking \(opt\): UV:\s*(\d+)%/);
    if (textureBakingUVMatch) {
      progress = parseInt(textureBakingUVMatch[1]);
      stage = 'glb_export';
      description = `Texture baking UV mapping (${progress}%)`;
      console.log(`[Progress Debug] Texture UV match found: ${progress}% -> stage: ${stage}, progress: ${75 + Math.min(progress * 0.05, 5)}`);
      progressCb({ stage, progress: 75 + Math.min(progress * 0.05, 5), message: description });
      continue;
    }

    // Texture optimization: "Texture baking (opt): optimizing: 97%|#########6| 2419/2500" or "[Trellis Server] Texture baking (opt): optimizing: 97%|#########6..."
    const textureOptMatch = cleanLine.match(/Texture baking \(opt\): optimizing:\s*(\d+)%.*?(\d+)\/(\d+)/);
    if (textureOptMatch) {
      const percent = parseInt(textureOptMatch[1]);
      const current = parseInt(textureOptMatch[2]);
      const total = parseInt(textureOptMatch[3]);
      stage = 'glb_export';
      description = `Texture optimization (${current}/${total}, ${percent}%)`;
      console.log(`[Progress Debug] Texture Opt match found: ${percent}% -> stage: ${stage}, progress: ${80 + Math.min(percent * 0.15, 15)}`);
      progressCb({ stage, progress: 80 + Math.min(percent * 0.15, 15), message: description });
      continue;
    }

    // Completion: "3D model generation completed in 69.29 seconds"
    if (cleanLine.includes('3D model generation completed')) {
      stage = 'download';
      description = '3D model generation completed';
      console.log(`[Progress Debug] Completion -> stage: ${stage}, progress: 100`);
      progressCb({ stage, progress: 100, message: description });
      continue;
    }
  }
}

function mapTrellisMessageToStage(message, progress) {
  // Simple stage mapping based on progress percentage
  // This matches the ProgressBar component's expected stages

  if (progress <= 0) {
    return { stage: 'preprocessing', description: 'Loading image and initializing pipeline' };
  } else if (progress <= 10) {
    return { stage: 'sparse_structure', description: 'Generating 3D structure foundation' };
  } else if (progress <= 50) {
    return { stage: 'slat_generation', description: 'Creating detailed 3D representation' };
  } else if (progress <= 60) {
    return { stage: 'mesh_creation', description: 'Processing and optimizing mesh' };
  } else if (progress <= 95) {
    return { stage: 'glb_export', description: 'Finalizing and exporting 3D model' };
  } else {
    return { stage: 'download', description: 'Downloading generated 3D model' };
  }
}

async function generate3DModel(imagePath, progressCb) {
  console.log('[Trellis Server] generate3DModel called with imagePath:', imagePath);

  // First, cleanup any lingering Python processes to prevent permission errors
  await cleanupPythonProcesses();

  const isRunning = await isTrellisRunning();
  console.log('[Trellis Server] isTrellisRunning() returned:', isRunning);

  // Set the global progress callback for server output parsing
  globalProgressCallback = progressCb;

  if (!isRunning) {
    console.log('[Trellis Server] Server not running, starting server...');
    if (progressCb) {
      const { stage, description } = mapTrellisMessageToStage('Starting Trellis server...', 0);
      progressCb({ stage, progress: 0, message: description });
    }
    try {
      startTrellisServer(progressCb);
      console.log('[Trellis Server] startTrellisServer() called successfully');
    } catch (error) {
      console.error('[Trellis Server] Error starting server:', error);
      throw error;
    }
    await waitForTrellisReady(null, progressCb);
  } else {
    console.log('[Trellis Server] Server already running, skipping startup');
  }

  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage('Sending image to Trellis for 3D generation...', 0);
    progressCb({ stage, progress: 0, message: description });
  }
  const form = new FormData();
  form.append('file', fs.createReadStream(imagePath), { filename: path.basename(imagePath) });

  let response;
  try {
    console.log('[Trellis Server] Sending POST request to:', `http://${TRELLIS_HOST}:${TRELLIS_PORT}/generate_no_preview`);
    console.log('[Trellis Server] Form data keys:', Object.keys(form._streams || {}));

    response = await axios.post(
      `http://${TRELLIS_HOST}:${TRELLIS_PORT}/generate_no_preview`,
      form,
      {
        headers: form.getHeaders(),
        maxBodyLength: Infinity,
        timeout: 300000 // 5 minutes timeout for long-running generation
      }
    );
    console.log('[Trellis Server] POST response status:', response.status);
    console.log('[Trellis Server] POST response data:', response.data);
  } catch (err) {
    console.error('[Trellis Server] POST request failed:', err.response?.status, err.response?.data);
    console.error('[Trellis Server] Full error:', err.message);
    throw new Error('Failed to POST to Trellis: ' + (err.response?.data?.message || err.message));
  }

  let status = response.data.status;
  let progress = response.data.progress;
  let message = response.data.message;
  let modelUrl = response.data.model_url;

  // Emit initial progress
  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage(message, progress);
    progressCb({ stage, progress, message: description });
  }

  // If still processing, poll for progress using the status endpoint
  while (status === 'PROCESSING') {
    await new Promise(r => setTimeout(r, 2000));
    try {
      const pollResponse = await axios.get(`http://${TRELLIS_HOST}:${TRELLIS_PORT}/status`, { timeout: 30000 });
      status = pollResponse.data.status;
      progress = pollResponse.data.progress;
      message = pollResponse.data.message;
      modelUrl = pollResponse.data.model_url;

      console.log(`[Trellis Server] Progress poll - Status: ${status}, Progress: ${progress}%, Message: ${message}`);

      if (progressCb) {
        const { stage, description } = mapTrellisMessageToStage(message, progress);
        console.log(`[Trellis Server] Calling progress callback - Stage: ${stage}, Progress: ${progress}%, Description: ${description}`);
        progressCb({ stage, progress, message: description });
      } else {
        console.log('[Trellis Server] No progress callback available during polling');
      }
    } catch (err) {
      console.error('[Trellis Server] Error during progress polling:', err.message);
      if (progressCb) progressCb({ stage: 'trellis', progress, message: 'Error polling Trellis progress: ' + err.message });
      break;
    }
  }

  if (!modelUrl) throw new Error('No model_url in Trellis response');

  // Convert relative URL to absolute URL if needed
  const fullModelUrl = modelUrl.startsWith('http') ? modelUrl : `http://${TRELLIS_HOST}:${TRELLIS_PORT}${modelUrl}`;

  console.log('[Trellis Server] Model URL from server:', modelUrl);
  console.log('[Trellis Server] Full model URL for download:', fullModelUrl);

  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage('Downloading generated 3D model...', 100);
    progressCb({ stage, progress: 100, message: description });
  }

  // Ensure output directory exists
  if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
  }

  const modelResponse = await axios.get(fullModelUrl, { responseType: 'stream', timeout: 60000 });
  let fileName = path.basename(fullModelUrl.split('?')[0]) || 'model';

  // Ensure the file has a .glb extension
  if (!fileName.endsWith('.glb') && !fileName.endsWith('.gltf')) {
    fileName = fileName + '.glb';
  }

  const outputPath = path.join(OUTPUT_DIR, fileName);

  console.log('[Trellis Server] Downloading model to:', outputPath);
  console.log('[Trellis Server] Model response status:', modelResponse.status);
  console.log('[Trellis Server] Model response headers:', modelResponse.headers['content-type']);

  await new Promise((resolve, reject) => {
    const writer = fs.createWriteStream(outputPath);
    modelResponse.data.pipe(writer);
    writer.on('finish', () => {
      console.log('[Trellis Server] Model file saved successfully');
      resolve();
    });
    writer.on('error', (error) => {
      console.error('[Trellis Server] Error saving model file:', error);
      reject(error);
    });
  });
  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage('3D model downloaded.', 100);
    progressCb({ stage, progress: 100, message: description });
  }

  // Convert absolute path to relative path for the frontend
  const app = require('electron').app;
  const relativePath = path.relative(app.getAppPath(), outputPath);

  console.log('[Trellis Server] Output file absolute path:', outputPath);
  console.log('[Trellis Server] Output file relative path:', relativePath);
  console.log('[Trellis Server] File exists:', fs.existsSync(outputPath));
  if (fs.existsSync(outputPath)) {
    const stats = fs.statSync(outputPath);
    console.log('[Trellis Server] File size:', stats.size, 'bytes');
  }

  return relativePath;
}

module.exports = {
  generate3DModel,
  cleanupPythonProcesses
};