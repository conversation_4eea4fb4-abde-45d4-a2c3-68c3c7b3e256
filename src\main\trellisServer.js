const { spawn } = require('child_process');
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const net = require('net');
const path = require('path');

const TRELLIS_PORT = 7960;
const TRELLIS_HOST = '127.0.0.1';
const RUN_BAT = path.join(__dirname, '../../pipelines/3DPipelines/gen3d/trellis-stable-projectorz-101/run-fp16.bat');
const OUTPUT_DIR = path.join(__dirname, '../../output');

// Connection attempt counter to reduce log spam
let connectionAttemptCounter = 0;

// Automated Python process cleanup function
async function cleanupPythonProcesses() {
  console.log('[Cleanup] Checking for lingering Python processes...');

  return new Promise((resolve) => {
    // First, check if there are any Python processes running
    const checkProcess = spawn('cmd.exe', ['/c', 'tasklist | findstr python.exe'], {
      stdio: 'pipe',
      windowsHide: true
    });

    let output = '';
    checkProcess.stdout.on('data', (data) => {
      output += data.toString();
    });

    checkProcess.on('close', (code) => {
      if (output.trim() && output.includes('python.exe')) {
        console.log('[Cleanup] Found Python processes, killing them...');
        console.log('[Cleanup] Processes found:', output.trim());

        // Kill all Python processes
        const killProcess = spawn('cmd.exe', ['/c', 'taskkill /F /IM python.exe'], {
          stdio: 'pipe',
          windowsHide: true
        });

        killProcess.on('close', (killCode) => {
          if (killCode === 0) {
            console.log('[Cleanup] Successfully killed Python processes');
          } else {
            console.log('[Cleanup] Some Python processes may still be running (exit code:', killCode, ')');
          }

          // Wait a moment for processes to fully terminate
          setTimeout(() => {
            resolve();
          }, 2000);
        });

        killProcess.on('error', (err) => {
          console.warn('[Cleanup] Error killing Python processes:', err.message);
          resolve();
        });
      } else {
        console.log('[Cleanup] No Python processes found');
        resolve();
      }
    });

    checkProcess.on('error', (err) => {
      console.warn('[Cleanup] Error checking for Python processes:', err.message);
      resolve();
    });
  });
}

function isTrellisRunning() {
  console.log('[Trellis Server] Checking if server is running on', TRELLIS_HOST + ':' + TRELLIS_PORT);
  return new Promise((resolve) => {
    const socket = net.createConnection(TRELLIS_PORT, TRELLIS_HOST);
    socket.on('connect', () => {
      console.log('[Trellis Server] Server is running - connection successful');
      connectionAttemptCounter = 0; // Reset counter on successful connection
      socket.end();
      resolve(true);
    });
    socket.on('error', (error) => {
      connectionAttemptCounter++;
      // Only log every 10th connection failure to reduce spam
      if (connectionAttemptCounter % 10 === 0) {
        console.log(`[Trellis Server] Server not running - connection failed (${connectionAttemptCounter} attempts):`, error.code);
      }
      resolve(false);
    });
  });
}

function startTrellisServer(progressCb = null) {
  console.log('[Trellis Server] Starting server...');
  console.log('[Trellis Server] RUN_BAT:', RUN_BAT);
  console.log('[Trellis Server] Batch file exists:', fs.existsSync(RUN_BAT));

  if (!fs.existsSync(RUN_BAT)) {
    throw new Error('Trellis run-fp16.bat not found at: ' + RUN_BAT);
  }
  const batDir = path.dirname(RUN_BAT);
  console.log('[Trellis Server] Working directory:', batDir);
  console.log('[Trellis Server] Command: cmd.exe /c', path.basename(RUN_BAT));

  // Store the progress callback globally so it can be used during generation
  globalProgressCallback = progressCb;

  // Use cmd.exe with specific flags to prevent window showing
  const child = spawn('cmd.exe', ['/c', path.basename(RUN_BAT)], {
    cwd: batDir,
    detached: false,  // Changed to false so we can capture output
    stdio: ['ignore', 'pipe', 'pipe'],
    windowsHide: true,
    shell: false,
    env: {
      ...process.env,
      // Set environment variables to minimize window visibility
      PYTHONUNBUFFERED: '1'
    }
  });
  // Capture and display all server output in main application logs
  child.stdout.on('data', (data) => {
    const output = data.toString();
    const lines = output.split('\n').filter(line => line.trim());
    lines.forEach(line => {
      console.log('[Trellis Server] ' + line.trim());
      if (typeof logger !== 'undefined') logger.info('[Trellis Server] ' + line.trim());

      // Check for port binding errors
      if (line.includes('error while attempting to bind') || line.includes('Address already in use')) {
        console.error('[Trellis Server] PORT CONFLICT DETECTED - Another server is using port 7960');
        console.error('[Trellis Server] Please close any other Trellis servers and try again');
      }
    });

    // Parse progress from server output if we have a callback
    if (globalProgressCallback) {
      console.log('[Progress Debug] Parsing output with globalProgressCallback set');
      parseProgressFromOutput(output, globalProgressCallback);
    } else {
      console.log('[Progress Debug] No globalProgressCallback set, skipping progress parsing');
    }
  });

  child.stderr.on('data', (data) => {
    const lines = data.toString().split('\n').filter(line => line.trim());
    lines.forEach(line => {
      console.error('[Trellis Server] ' + line.trim());
      if (typeof logger !== 'undefined') logger.error('[Trellis Server] ' + line.trim());

      // Check for port binding errors in stderr too
      if (line.includes('error while attempting to bind') || line.includes('Address already in use')) {
        console.error('[Trellis Server] PORT CONFLICT DETECTED - Another server is using port 7960');
        console.error('[Trellis Server] Please close any other Trellis servers and try again');
      }
    });
  });
  // Handle process events and log them to the main application logs
  child.on('close', (code) => {
    console.log(`[Trellis Server] Process exited with code ${code}`);
    if (typeof logger !== 'undefined') logger.info(`[Trellis Server] Process exited with code ${code}`);
  });

  child.on('error', (error) => {
    console.error('[Trellis Server] Process error:', error);
    if (typeof logger !== 'undefined') logger.error('[Trellis Server] Process error: ' + error.message);
  });

  // Store the child process reference globally so we can check if it's still running
  global.trellisProcess = child;
}

async function waitForTrellisReady(timeoutMs = null, progressCb) {
  const start = Date.now();
  while (true) {
    if (await isTrellisRunning()) return true;
    if (progressCb) progressCb({ stage: 'trellis', message: 'Waiting for Trellis server to start...' });
    await new Promise(r => setTimeout(r, 2000));
    // No timeout: wait forever
  }
}

// Global progress callback for server output parsing
let globalProgressCallback = null;

// Track sampling stage count (0=sparse, 1=slat) like the old working system
let samplingStageCount = 0;

function parseProgressFromOutput(output, progressCb) {
  if (!progressCb) {
    console.log('[Progress Debug] No progressCb provided, skipping parsing');
    return;
  }

  const lines = output.split('\n');
  for (const line of lines) {
    const trimmedLine = line.trim();
    if (!trimmedLine) continue;

    // Remove [Trellis Server] prefix if present
    const cleanLine = trimmedLine.replace(/^\[Trellis Server\]\s*/, '');

    try {
      // Parse different progress patterns using the EXACT same logic as the old working system

      // Preprocessing stage
      if (cleanLine.includes('Loading image:')) {
        progressCb({ stage: 'preprocessing', progress: 25, message: 'Loading input image...' });
        continue;
      } else if (cleanLine.includes('Image loaded:')) {
        progressCb({ stage: 'preprocessing', progress: 50, message: 'Image loaded successfully' });
        continue;
      } else if (cleanLine.includes('Loading Trellis pipeline')) {
        progressCb({ stage: 'preprocessing', progress: 75, message: 'Loading Trellis pipeline...' });
        continue;
      } else if (cleanLine.includes('Pipeline loaded on') || cleanLine.includes('CUDA not available') || cleanLine.includes('PyTorch not available')) {
        progressCb({ stage: 'preprocessing', progress: 100, message: 'Pipeline loaded and ready' });
        progressCb({ stage: 'sparse_structure', progress: 0, message: 'Starting sparse structure generation...' });
        continue;
      } else if (cleanLine.includes('Running Trellis pipeline')) {
        progressCb({ stage: 'sparse_structure', progress: 5, message: 'Initializing 3D structure generation...' });
        continue;
      }

      // Sparse Structure Sampling - Parse actual percentage and iteration count (EXACT same logic as old system)
      else if (cleanLine.includes('Sampling:')) {
        console.log(`[Progress Debug] Found Sampling line: "${cleanLine}"`);
        // Extract percentage from lines like "Sampling:  25%|##5       | 3/12 [00:02<00:05,  1.54it/s]"
        const percentageMatch = cleanLine.match(/Sampling:\s*(\d+)%/);
        let percentage = 0;
        if (percentageMatch) {
          percentage = parseInt(percentageMatch[1]);
          console.log(`[Progress Debug] Sampling percentage extracted: ${percentage}%`);
        } else {
          console.log(`[Progress Debug] Sampling regex failed to match: "${cleanLine}"`);
        }

        // Track which sampling stage we're in (EXACT same logic as old system)
        if (samplingStageCount === 0) {
          // First sampling stage is sparse structure
          console.log(`[Progress Debug] Updating sparse_structure stage: ${percentage}%`);
          progressCb({ stage: 'sparse_structure', progress: percentage, message: `Sparse structure sampling: ${percentage}%` });
          if (percentage >= 100) {
            samplingStageCount = 1;
            console.log(`[Progress Debug] Moving to SLAT generation stage`);
            progressCb({ stage: 'slat_generation', progress: 0, message: 'Starting SLAT generation...' });
          }
        } else if (samplingStageCount === 1) {
          // Second sampling stage is SLAT generation
          console.log(`[Progress Debug] Updating slat_generation stage: ${percentage}%`);
          progressCb({ stage: 'slat_generation', progress: percentage, message: `SLAT generation sampling: ${percentage}%` });
        }
        continue;
      }

      // Pipeline execution completed
      else if (cleanLine.includes('Pipeline execution completed')) {
        console.log('[Progress Debug] Pipeline execution completed detected!');
        progressCb({ stage: 'slat_generation', progress: 100, message: 'SLAT generation completed' });
        progressCb({ stage: 'mesh_creation', progress: 0, message: 'Starting mesh processing...' });
        continue;
      } else if (cleanLine.includes('Sparse structure completed')) {
        console.log('[Progress Debug] Sparse structure completed detected!');
        progressCb({ stage: 'sparse_structure', progress: 100, message: 'Sparse structure completed' });
        continue;
      } else if (cleanLine.includes('SLAT generation completed')) {
        console.log('[Progress Debug] SLAT generation completed detected!');
        progressCb({ stage: 'slat_generation', progress: 100, message: 'SLAT generation completed' });
        progressCb({ stage: 'mesh_creation', progress: 5, message: 'Starting mesh processing...' });
        continue;
      } else if (cleanLine.includes('Saving Gaussian PLY')) {
        progressCb({ stage: 'mesh_creation', progress: 10, message: 'Saving Gaussian PLY...' });
        continue;
      } else if (cleanLine.includes('Generating GLB file')) {
        progressCb({ stage: 'mesh_creation', progress: 20, message: 'Generating GLB file...' });
        continue;
      }

      // Mesh Decimation - Parse actual percentage or detect start (EXACT same logic as old system)
      else if (cleanLine.includes('Decimating Mesh:')) {
        console.log(`[Progress Debug] Found Decimating line: "${cleanLine}"`);
        const percentageMatch = cleanLine.match(/Decimating Mesh:\s*(\d+)%/);
        if (percentageMatch) {
          const percentage = parseInt(percentageMatch[1]);
          // Map decimation progress to 20-60% of mesh creation stage (EXACT same as old system)
          const meshProgress = 20 + Math.floor(percentage * 0.4);
          console.log(`[Progress Debug] Decimating percentage: ${percentage}% -> mesh progress: ${meshProgress}%`);
          progressCb({ stage: 'mesh_creation', progress: meshProgress, message: `Decimating mesh: ${percentage}%` });
        } else {
          // Just started decimation
          console.log(`[Progress Debug] Decimating started (no percentage)`);
          progressCb({ stage: 'mesh_creation', progress: 20, message: 'Starting mesh decimation...' });
        }
        continue;
      } else if (cleanLine.includes('After decimate:')) {
        // Decimation completed
        progressCb({ stage: 'mesh_creation', progress: 60, message: 'Mesh decimation completed' });
        continue;
      }

      // Rendering - Parse iteration count (EXACT same logic as old system)
      else if (cleanLine.includes('Rendering:') && cleanLine.includes('it [')) {
        // Extract iteration from lines like "Rendering: 54it [00:01, 28.38it/s]"
        const iterationMatch = cleanLine.match(/Rendering:\s*(\d+)it/);
        if (iterationMatch) {
          const iterations = parseInt(iterationMatch[1]);
          // Use 100 as baseline for rendering progress (EXACT same as old system)
          const percentage = Math.min(Math.floor((iterations / 100) * 100), 100);
          // Map rendering progress to 60-80% of mesh creation stage
          const meshProgress = 60 + Math.floor(percentage * 0.2);
          progressCb({ stage: 'mesh_creation', progress: meshProgress, message: `Rendering: ${iterations} iterations` });
        }
        continue;
      } else if (cleanLine.includes('Rendering:') && cleanLine.includes('0it [')) {
        // Rendering started
        progressCb({ stage: 'mesh_creation', progress: 60, message: 'Starting rendering...' });
        continue;
      }

      // Texture Baking - Parse actual percentage and iteration count (EXACT same logic as old system)
      else if (cleanLine.includes('Texture baking (opt): optimizing:')) {
        // Extract percentage from lines like "Texture baking (opt): optimizing: 1%|          | 31/2500 [00:00<00:39, 61.78it/s, loss=0.437]"
        const percentageMatch = cleanLine.match(/optimizing:\s*(\d+)%/);
        let percentage = 0;
        if (percentageMatch) {
          percentage = parseInt(percentageMatch[1]);
        }

        // Map texture baking to 80-100% of mesh creation stage (EXACT same as old system)
        const meshProgress = 80 + Math.floor(percentage * 0.2);
        progressCb({ stage: 'mesh_creation', progress: meshProgress, message: `Texture baking: ${percentage}%` });
        continue;
      } else if (cleanLine.includes('Texture baking (opt):') && !cleanLine.includes('optimizing:')) {
        // Texture baking started
        progressCb({ stage: 'mesh_creation', progress: 80, message: 'Starting texture baking...' });
        continue;
      }

      else if (cleanLine.includes('GLB file generated successfully')) {
        console.log('[Progress Debug] GLB file generated successfully detected!');
        progressCb({ stage: 'mesh_creation', progress: 100, message: 'Mesh creation completed' });
        progressCb({ stage: 'glb_export', progress: 25, message: 'GLB file generated successfully' });
        continue;
      } else if (cleanLine.includes('Rendering videos')) {
        console.log('[Progress Debug] Rendering videos detected!');
        progressCb({ stage: 'glb_export', progress: 50, message: 'Rendering videos...' });
        continue;
      } else if (cleanLine.includes('Successfully generated output files')) {
        console.log('[Progress Debug] Successfully generated output files detected!');
        progressCb({ stage: 'glb_export', progress: 100, message: 'GLB export completed' });
        continue;
      } else if (cleanLine.includes('Videos rendered successfully')) {
        console.log('[Progress Debug] Videos rendered successfully detected!');
        progressCb({ stage: 'glb_export', progress: 100, message: 'All processing completed' });
        continue;
      }

    } catch (error) {
      console.log(`[Progress Debug] Error in progress tracking: ${error}`);
    }
  }
}

function mapTrellisMessageToStage(message, progress) {
  // Simple stage mapping based on progress percentage
  // This matches the ProgressBar component's expected stages

  if (progress <= 0) {
    return { stage: 'preprocessing', description: 'Loading image and initializing pipeline' };
  } else if (progress <= 10) {
    return { stage: 'sparse_structure', description: 'Generating 3D structure foundation' };
  } else if (progress <= 50) {
    return { stage: 'slat_generation', description: 'Creating detailed 3D representation' };
  } else if (progress <= 60) {
    return { stage: 'mesh_creation', description: 'Processing and optimizing mesh' };
  } else if (progress <= 95) {
    return { stage: 'glb_export', description: 'Finalizing and exporting 3D model' };
  } else {
    return { stage: 'download', description: 'Downloading generated 3D model' };
  }
}

async function generate3DModel(imagePath, progressCb) {
  console.log('[Trellis Server] generate3DModel called with imagePath:', imagePath);

  // First, cleanup any lingering Python processes to prevent permission errors
  await cleanupPythonProcesses();

  const isRunning = await isTrellisRunning();
  console.log('[Trellis Server] isTrellisRunning() returned:', isRunning);

  // Reset sampling stage count for new generation
  samplingStageCount = 0;

  // Set the global progress callback for server output parsing
  globalProgressCallback = progressCb;

  if (!isRunning) {
    console.log('[Trellis Server] Server not running, starting server...');
    if (progressCb) {
      const { stage, description } = mapTrellisMessageToStage('Starting Trellis server...', 0);
      progressCb({ stage, progress: 0, message: description });
    }
    try {
      startTrellisServer(progressCb);
      console.log('[Trellis Server] startTrellisServer() called successfully');
    } catch (error) {
      console.error('[Trellis Server] Error starting server:', error);
      throw error;
    }
    await waitForTrellisReady(null, progressCb);
  } else {
    console.log('[Trellis Server] Server already running, skipping startup');
  }

  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage('Sending image to Trellis for 3D generation...', 0);
    progressCb({ stage, progress: 0, message: description });
  }
  const form = new FormData();
  form.append('file', fs.createReadStream(imagePath), { filename: path.basename(imagePath) });

  let response;
  try {
    console.log('[Trellis Server] Sending POST request to:', `http://${TRELLIS_HOST}:${TRELLIS_PORT}/generate_no_preview`);
    console.log('[Trellis Server] Form data keys:', Object.keys(form._streams || {}));

    response = await axios.post(
      `http://${TRELLIS_HOST}:${TRELLIS_PORT}/generate_no_preview`,
      form,
      {
        headers: form.getHeaders(),
        maxBodyLength: Infinity,
        timeout: 300000 // 5 minutes timeout for long-running generation
      }
    );
    console.log('[Trellis Server] POST response status:', response.status);
    console.log('[Trellis Server] POST response data:', response.data);
  } catch (err) {
    console.error('[Trellis Server] POST request failed:', err.response?.status, err.response?.data);
    console.error('[Trellis Server] Full error:', err.message);
    throw new Error('Failed to POST to Trellis: ' + (err.response?.data?.message || err.message));
  }

  let status = response.data.status;
  let progress = response.data.progress;
  let message = response.data.message;
  let modelUrl = response.data.model_url;

  // Emit initial progress
  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage(message, progress);
    progressCb({ stage, progress, message: description });
  }

  // If still processing, poll for progress using the status endpoint
  while (status === 'PROCESSING') {
    await new Promise(r => setTimeout(r, 2000));
    try {
      const pollResponse = await axios.get(`http://${TRELLIS_HOST}:${TRELLIS_PORT}/status`, { timeout: 30000 });
      status = pollResponse.data.status;
      progress = pollResponse.data.progress;
      message = pollResponse.data.message;
      modelUrl = pollResponse.data.model_url;

      console.log(`[Trellis Server] Progress poll - Status: ${status}, Progress: ${progress}%, Message: ${message}`);

      if (progressCb) {
        const { stage, description } = mapTrellisMessageToStage(message, progress);
        console.log(`[Trellis Server] Calling progress callback - Stage: ${stage}, Progress: ${progress}%, Description: ${description}`);
        progressCb({ stage, progress, message: description });
      } else {
        console.log('[Trellis Server] No progress callback available during polling');
      }
    } catch (err) {
      console.error('[Trellis Server] Error during progress polling:', err.message);
      if (progressCb) progressCb({ stage: 'trellis', progress, message: 'Error polling Trellis progress: ' + err.message });
      break;
    }
  }

  if (!modelUrl) throw new Error('No model_url in Trellis response');

  // Convert relative URL to absolute URL if needed
  const fullModelUrl = modelUrl.startsWith('http') ? modelUrl : `http://${TRELLIS_HOST}:${TRELLIS_PORT}${modelUrl}`;

  console.log('[Trellis Server] Model URL from server:', modelUrl);
  console.log('[Trellis Server] Full model URL for download:', fullModelUrl);

  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage('Downloading generated 3D model...', 100);
    progressCb({ stage, progress: 100, message: description });
  }

  // Ensure output directory exists
  if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
  }

  const modelResponse = await axios.get(fullModelUrl, { responseType: 'stream', timeout: 60000 });
  let fileName = path.basename(fullModelUrl.split('?')[0]) || 'model';

  // Ensure the file has a .glb extension
  if (!fileName.endsWith('.glb') && !fileName.endsWith('.gltf')) {
    fileName = fileName + '.glb';
  }

  const outputPath = path.join(OUTPUT_DIR, fileName);

  console.log('[Trellis Server] Downloading model to:', outputPath);
  console.log('[Trellis Server] Model response status:', modelResponse.status);
  console.log('[Trellis Server] Model response headers:', modelResponse.headers['content-type']);

  await new Promise((resolve, reject) => {
    const writer = fs.createWriteStream(outputPath);
    modelResponse.data.pipe(writer);
    writer.on('finish', () => {
      console.log('[Trellis Server] Model file saved successfully');
      resolve();
    });
    writer.on('error', (error) => {
      console.error('[Trellis Server] Error saving model file:', error);
      reject(error);
    });
  });
  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage('3D model downloaded.', 100);
    progressCb({ stage, progress: 100, message: description });
  }

  // Convert absolute path to relative path for the frontend
  const app = require('electron').app;
  const relativePath = path.relative(app.getAppPath(), outputPath);

  console.log('[Trellis Server] Output file absolute path:', outputPath);
  console.log('[Trellis Server] Output file relative path:', relativePath);
  console.log('[Trellis Server] File exists:', fs.existsSync(outputPath));
  if (fs.existsSync(outputPath)) {
    const stats = fs.statSync(outputPath);
    console.log('[Trellis Server] File size:', stats.size, 'bytes');
  }

  return relativePath;
}

module.exports = {
  generate3DModel,
  cleanupPythonProcesses
};