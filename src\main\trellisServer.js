const { spawn } = require('child_process');
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const net = require('net');
const path = require('path');

const TRELLIS_PORT = 7960;
const TRELLIS_HOST = '127.0.0.1';
const RUN_BAT = path.join(__dirname, '../../utils/helpers/StableProjectorZ/StableProjectorz/gen3d/trellis-stable-projectorz-101/run-fp16.bat');
const OUTPUT_DIR = path.join(__dirname, '../../output');

function isTrellisRunning() {
  return new Promise((resolve) => {
    const socket = net.createConnection(TRELLIS_PORT, TRELLIS_HOST);
    socket.on('connect', () => { socket.end(); resolve(true); });
    socket.on('error', () => resolve(false));
  });
}

function startTrellisServer() {
  console.log('RUN_BAT:', RUN_BAT);
  if (!fs.existsSync(RUN_BAT)) {
    throw new Error('Trellis run-fp16.bat not found at: ' + RUN_BAT);
  }
  const batDir = path.dirname(RUN_BAT);
  // Use cmd.exe to run the batch file on Windows, set cwd to batch file directory
  const child = spawn('cmd.exe', ['/c', RUN_BAT], {
    cwd: batDir,
    detached: true,
    stdio: ['ignore', 'pipe', 'pipe'],
    windowsHide: true,
    shell: false,
    windowsVerbatimArguments: false
  });
  child.stdout.on('data', (data) => {
    console.log('[Trellis STDOUT]', data.toString());
    if (typeof logger !== 'undefined') logger.info('[Trellis STDOUT] ' + data.toString());
  });
  child.stderr.on('data', (data) => {
    console.error('[Trellis STDERR]', data.toString());
    if (typeof logger !== 'undefined') logger.error('[Trellis STDERR] ' + data.toString());
  });
  child.on('close', (code) => {
    console.log(`[Trellis] Server process exited with code ${code}`);
    if (typeof logger !== 'undefined') logger.info(`[Trellis] Server process exited with code ${code}`);
  });
}

async function waitForTrellisReady(timeoutMs = null, progressCb) {
  const start = Date.now();
  while (true) {
    if (await isTrellisRunning()) return true;
    if (progressCb) progressCb({ stage: 'trellis', message: 'Waiting for Trellis server to start...' });
    await new Promise(r => setTimeout(r, 2000));
    // No timeout: wait forever
  }
}

function mapTrellisMessageToStage(message, progress) {
  if (!message) return { stage: 'preprocessing', description: 'Loading image and initializing pipeline' };
  const msg = message.toLowerCase();

  // Stage 1: Image Preprocessing (0-10%)
  if (msg.includes('image preprocessing') || msg.includes('loading image') || msg.includes('initializing pipeline')) {
    return { stage: 'preprocessing', description: 'Loading image and initializing pipeline' };
  }

  // Stage 2: Sparse Structure Sampling (10-50%)
  if (msg.includes('sparse structure sampling') || msg.includes('generating 3d structure foundation')) {
    return { stage: 'sparse_structure', description: 'Generating 3D structure foundation' };
  }

  // Stage 3: SLAT Generation Sampling (50-60%)
  if (msg.includes('slat generation sampling') || msg.includes('creating detailed 3d representation')) {
    return { stage: 'slat_generation', description: 'Creating detailed 3D representation' };
  }

  // Stage 4: Mesh Processing & Optimization (60-95%)
  if (msg.includes('mesh processing') || msg.includes('optimization') || msg.includes('decimating mesh') ||
      msg.includes('rendering') || msg.includes('texture baking')) {
    return { stage: 'mesh_creation', description: 'Decimating mesh, rendering, and texture baking' };
  }

  // Stage 5: GLB Export (95-100%)
  if (msg.includes('glb export') || msg.includes('finalizing') || msg.includes('exporting 3d model')) {
    return { stage: 'glb_export', description: 'Finalizing and exporting 3D model' };
  }

  // Client-side stages
  if (msg.includes('downloading') || msg.includes('download')) {
    return { stage: 'download', description: 'Downloading generated 3D model' };
  }

  // Fallback: use progress ranges to determine stage
  if (progress < 10) return { stage: 'preprocessing', description: message };
  if (progress < 50) return { stage: 'sparse_structure', description: message };
  if (progress < 60) return { stage: 'slat_generation', description: message };
  if (progress < 95) return { stage: 'mesh_creation', description: message };
  if (progress < 100) return { stage: 'glb_export', description: message };
  return { stage: 'download', description: message };
}

async function generate3DModel(imagePath, progressCb) {
  if (!(await isTrellisRunning())) {
    if (progressCb) {
      const { stage, description } = mapTrellisMessageToStage('Starting Trellis server...', 0);
      progressCb({ stage, progress: 0, message: description });
    }
    startTrellisServer();
    await waitForTrellisReady(null, progressCb);
  }

  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage('Sending image to Trellis for 3D generation...', 0);
    progressCb({ stage, progress: 0, message: description });
  }
  const form = new FormData();
  form.append('file', fs.createReadStream(imagePath), { filename: path.basename(imagePath) });

  let response;
  try {
    response = await axios.post(
      `http://${TRELLIS_HOST}:${TRELLIS_PORT}/generate_no_preview`,
      form,
      {
        headers: form.getHeaders(),
        maxBodyLength: Infinity,
        timeout: 300000 // 5 minutes timeout for long-running generation
      }
    );
  } catch (err) {
    throw new Error('Failed to POST to Trellis: ' + (err.response?.data?.message || err.message));
  }

  let status = response.data.status;
  let progress = response.data.progress;
  let message = response.data.message;
  let modelUrl = response.data.model_url;

  // Emit initial progress
  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage(message, progress);
    progressCb({ stage, progress, message: description });
  }

  // If still processing, poll for progress using the status endpoint
  while (status === 'PROCESSING') {
    await new Promise(r => setTimeout(r, 2000));
    try {
      const pollResponse = await axios.get(`http://${TRELLIS_HOST}:${TRELLIS_PORT}/status`, { timeout: 30000 });
      status = pollResponse.data.status;
      progress = pollResponse.data.progress;
      message = pollResponse.data.message;
      modelUrl = pollResponse.data.model_url;
      if (progressCb) {
        const { stage, description } = mapTrellisMessageToStage(message, progress);
        progressCb({ stage, progress, message: description });
      }
    } catch (err) {
      if (progressCb) progressCb({ stage: 'trellis', progress, message: 'Error polling Trellis progress: ' + err.message });
      break;
    }
  }

  if (!modelUrl) throw new Error('No model_url in Trellis response');

  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage('Downloading generated 3D model...', 100);
    progressCb({ stage, progress: 100, message: description });
  }

  // Ensure output directory exists
  if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
  }

  const modelResponse = await axios.get(modelUrl, { responseType: 'stream', timeout: 60000 });
  const fileName = path.basename(modelUrl.split('?')[0]);
  const outputPath = path.join(OUTPUT_DIR, fileName);
  await new Promise((resolve, reject) => {
    const writer = fs.createWriteStream(outputPath);
    modelResponse.data.pipe(writer);
    writer.on('finish', resolve);
    writer.on('error', reject);
  });
  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage('3D model downloaded.', 100);
    progressCb({ stage, progress: 100, message: description });
  }

  // Convert absolute path to relative path for the frontend
  const app = require('electron').app;
  const relativePath = path.relative(app.getAppPath(), outputPath);
  return relativePath;
}

module.exports = { generate3DModel }; 