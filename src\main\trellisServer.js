const { spawn } = require('child_process');
const axios = require('axios');
const fs = require('fs');
const net = require('net');
const path = require('path');

const TRELLIS_PORT = 7960;
const TRELLIS_HOST = '127.0.0.1';
const RUN_BAT = path.join(__dirname, '../../pipelines/3DPipelines/gen3d/trellis-stable-projectorz-101/run-fp16.bat');
const OUTPUT_DIR = path.join(__dirname, '../../output');

// Connection check counter to reduce log spam
let connectionCheckCount = 0;

// Progress parsing function to extract progress from Trellis server output
function parseProgressAndSendIPC(line) {
  const { BrowserWindow } = require('electron');
  const mainWindow = BrowserWindow.getAllWindows()[0];

  if (!mainWindow) return;

  // Parse different progress stages from Trellis output
  let progress = null;
  let stage = null;
  let message = null;

  // Sampling progress: "Sampling: 45%|████▌     | 45/100 [00:12<00:15, 3.67it/s]"
  const samplingMatch = line.match(/Sampling:\s*(\d+)%/);
  if (samplingMatch) {
    progress = parseInt(samplingMatch[1]);
    stage = 'sampling';
    message = `Sampling: ${progress}%`;
  }

  // Decimating progress: "Decimating: 80%|████████  | 80/100 [00:05<00:01, 15.2it/s]"
  const decimatingMatch = line.match(/Decimating:\s*(\d+)%/);
  if (decimatingMatch) {
    progress = parseInt(decimatingMatch[1]);
    stage = 'decimating';
    message = `Decimating: ${progress}%`;
  }

  // Rasterizing progress: "Rasterizing: 60%|██████    | 60/100 [00:08<00:05, 7.5it/s]"
  const rasterizingMatch = line.match(/Rasterizing:\s*(\d+)%/);
  if (rasterizingMatch) {
    progress = parseInt(rasterizingMatch[1]);
    stage = 'rasterizing';
    message = `Rasterizing: ${progress}%`;
  }

  // Rendering progress: "Rendering: 80it [00:04, 20.94it/s]"
  const renderingMatch = line.match(/Rendering:\s*(\d+)it/);
  if (renderingMatch) {
    const iterations = parseInt(renderingMatch[1]);
    // Assume 80 iterations total for rendering (based on log)
    progress = Math.min(Math.round((iterations / 80) * 100), 100);
    stage = 'rendering';
    message = `Rendering: ${iterations}/80 iterations`;
  }

  // Texture baking progress: "Texture baking (opt): optimizing: 45%|████▌     | 1125/2500"
  const textureBakingMatch = line.match(/Texture baking.*optimizing:\s*(\d+)%/);
  if (textureBakingMatch) {
    progress = parseInt(textureBakingMatch[1]);
    stage = 'texture_baking';
    message = `Texture baking: ${progress}%`;
  }

  // Send IPC update if we found progress
  if (progress !== null && stage && message) {
    mainWindow.webContents.send('progress-update', {
      progress,
      stage,
      message
    });
  }
}

function isTrellisRunning() {
  connectionCheckCount++;

  // Only log every 10th connection check to reduce spam
  const shouldLog = connectionCheckCount % 10 === 1;

  if (shouldLog) {
    console.log('[Trellis Server] Checking if server is running on', TRELLIS_HOST + ':' + TRELLIS_PORT, `(attempt ${connectionCheckCount})`);
  }

  return new Promise((resolve) => {
    const socket = net.createConnection(TRELLIS_PORT, TRELLIS_HOST);
    socket.on('connect', () => {
      console.log('[Trellis Server] Server is running - connection successful');
      socket.end();
      resolve(true);
    });
    socket.on('error', (error) => {
      // Only log connection errors every 10th attempt to reduce spam
      if (shouldLog) {
        console.log('[Trellis Server] Server not running - connection failed:', error.code);
      }
      resolve(false);
    });
  });
}

// Check if server is healthy by making a test request
async function isServerHealthy() {
  try {
    // Try the root endpoint first, then fallback to a simple GET request
    const response = await axios.get(`http://${TRELLIS_HOST}:${TRELLIS_PORT}/`, {
      timeout: 5000
    });
    return response.status === 200 || response.status === 404; // 404 is OK, means server is responding
  } catch (error) {
    // If root endpoint fails, try a simple connection test
    try {
      const testResponse = await axios.get(`http://${TRELLIS_HOST}:${TRELLIS_PORT}/docs`, {
        timeout: 3000
      });
      return testResponse.status === 200;
    } catch (secondError) {
      console.log('[Trellis Server] Health check failed:', error.message);
      return false;
    }
  }
}

// Kill all Trellis-related processes to ensure clean restart
async function killAllTrellisProcesses() {
  console.log('[Trellis Server] Killing all Trellis processes...');

  try {
    // Kill our tracked process first
    if (global.trellisProcess && !global.trellisProcess.killed) {
      console.log('[Trellis Server] Killing tracked process...');
      global.trellisProcess.kill('SIGTERM');
      global.trellisProcess = null;
    }

    // Kill all Python processes that might be running Trellis
    const { spawn } = require('child_process');

    // Use taskkill to force kill Python processes
    const killProcess = spawn('taskkill', ['/f', '/im', 'python.exe'], {
      windowsHide: true,
      stdio: 'pipe'
    });

    await new Promise((resolve) => {
      killProcess.on('close', (code) => {
        console.log('[Trellis Server] taskkill completed with code:', code);
        resolve();
      });
      killProcess.on('error', (error) => {
        console.log('[Trellis Server] taskkill error (this is normal if no processes found):', error.message);
        resolve();
      });
    });

    // Wait a bit longer for processes to fully terminate
    console.log('[Trellis Server] Waiting for processes to terminate...');
    await new Promise(resolve => setTimeout(resolve, 5000));

  } catch (error) {
    console.error('[Trellis Server] Error killing processes:', error.message);
  }
}

function startTrellisServer() {
  console.log('[Trellis Server] Starting server...');
  console.log('[Trellis Server] RUN_BAT:', RUN_BAT);
  console.log('[Trellis Server] Batch file exists:', fs.existsSync(RUN_BAT));

  if (!fs.existsSync(RUN_BAT)) {
    throw new Error('Trellis run-fp16.bat not found at: ' + RUN_BAT);
  }
  const batDir = path.dirname(RUN_BAT);
  console.log('[Trellis Server] Working directory:', batDir);
  console.log('[Trellis Server] Command: cmd.exe /c', path.basename(RUN_BAT));

  // Use cmd.exe with specific flags to prevent window showing
  const child = spawn('cmd.exe', ['/c', path.basename(RUN_BAT)], {
    cwd: batDir,
    detached: false,  // Changed to false so we can capture output
    stdio: ['ignore', 'pipe', 'pipe'],
    windowsHide: true,
    shell: false,
    env: {
      ...process.env,
      // Set environment variables to minimize window visibility
      PYTHONUNBUFFERED: '1'
    }
  });
  // Capture and display all server output in main application logs
  child.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(line => line.trim());
    lines.forEach(line => {
      console.log('[Trellis Server] ' + line.trim());
      if (typeof logger !== 'undefined') logger.info('[Trellis Server] ' + line.trim());

      // Parse progress from Trellis server output and send IPC updates
      parseProgressAndSendIPC(line.trim());

      // Check for port binding errors
      if (line.includes('error while attempting to bind') || line.includes('Address already in use')) {
        console.error('[Trellis Server] PORT CONFLICT DETECTED - Another server is using port 7960');
        console.error('[Trellis Server] Please close any other Trellis servers and try again');
      }
    });
  });

  child.stderr.on('data', (data) => {
    const lines = data.toString().split('\n').filter(line => line.trim());
    lines.forEach(line => {
      console.error('[Trellis Server] ' + line.trim());
      if (typeof logger !== 'undefined') logger.error('[Trellis Server] ' + line.trim());

      // Check for port binding errors in stderr too
      if (line.includes('error while attempting to bind') || line.includes('Address already in use')) {
        console.error('[Trellis Server] PORT CONFLICT DETECTED - Another server is using port 7960');
        console.error('[Trellis Server] Please close any other Trellis servers and try again');
      }
    });
  });
  // Handle process events and log them to the main application logs
  child.on('close', (code) => {
    console.log(`[Trellis Server] Process exited with code ${code}`);
    if (typeof logger !== 'undefined') logger.info(`[Trellis Server] Process exited with code ${code}`);
  });

  child.on('error', (error) => {
    console.error('[Trellis Server] Process error:', error);
    if (typeof logger !== 'undefined') logger.error('[Trellis Server] Process error: ' + error.message);
  });

  // Store the child process reference globally so we can check if it's still running
  global.trellisProcess = child;
}

async function waitForTrellisReady(timeoutMs = null, progressCb) {
  const start = Date.now();
  while (true) {
    if (await isTrellisRunning()) return true;
    if (progressCb) progressCb({ stage: 'trellis', message: 'Waiting for Trellis server to start...' });
    await new Promise(r => setTimeout(r, 2000));
    // No timeout: wait forever
  }
}

function mapTrellisMessageToStage(message, progress) {
  if (!message) return { stage: 'preprocessing', description: 'Loading image and initializing pipeline' };
  const msg = message.toLowerCase();

  // Match actual server messages from the log

  // Stage 1: Initialization and Sampling (0-30%)
  if (msg.includes('sampling:') || msg.includes('sampling') || msg.includes('it/s')) {
    return { stage: 'sparse_structure', description: 'Generating 3D structure (Sampling)' };
  }

  // Stage 2: SLAT Decoding (30-40%)
  if (msg.includes('decoding the slat') || msg.includes('decoding')) {
    return { stage: 'slat_generation', description: 'Decoding the SLAT representation' };
  }

  // Stage 3: Mesh Processing (40-80%)
  if (msg.includes('decimating mesh') || msg.includes('before postprocess') || msg.includes('after decimate')) {
    return { stage: 'mesh_creation', description: 'Processing and decimating mesh' };
  }

  // Stage 4: Rasterizing (80-85%)
  if (msg.includes('rasterizing') || msg.includes('invisible faces')) {
    return { stage: 'mesh_creation', description: 'Rasterizing and removing invisible faces' };
  }

  // Stage 5: Rendering and Texture Baking (85-95%)
  if (msg.includes('rendering:') || msg.includes('texture baking') || msg.includes('optimizing')) {
    return { stage: 'glb_export', description: 'Rendering and baking textures' };
  }

  // Stage 6: Completion (95-100%)
  if (msg.includes('3d model generation completed') || msg.includes('completed in')) {
    return { stage: 'glb_export', description: '3D model generation completed' };
  }

  // Client-side stages
  if (msg.includes('downloading') || msg.includes('download')) {
    return { stage: 'download', description: 'Downloading generated 3D model' };
  }

  // Fallback: use progress ranges to determine stage
  if (progress < 30) return { stage: 'sparse_structure', description: message };
  if (progress < 40) return { stage: 'slat_generation', description: message };
  if (progress < 80) return { stage: 'mesh_creation', description: message };
  if (progress < 95) return { stage: 'glb_export', description: message };
  return { stage: 'download', description: message };
}

async function generate3DModel(imagePath, progressCb) {
  console.log('[Trellis Server] generate3DModel called with imagePath:', imagePath);

  const isRunning = await isTrellisRunning();
  console.log('[Trellis Server] isTrellisRunning() returned:', isRunning);

  if (!isRunning) {
    console.log('[Trellis Server] Server not running, starting server...');
    if (progressCb) {
      const { stage, description } = mapTrellisMessageToStage('Starting Trellis server...', 0);
      progressCb({ stage, progress: 0, message: description });
    }
    try {
      startTrellisServer();
      console.log('[Trellis Server] startTrellisServer() called successfully');
    } catch (error) {
      console.error('[Trellis Server] Error starting server:', error);
      throw error;
    }
    await waitForTrellisReady(null, progressCb);
  } else {
    console.log('[Trellis Server] Server already running, checking health...');
    const isHealthy = await isServerHealthy();
    if (!isHealthy) {
      console.log('[Trellis Server] Server is unhealthy, restarting...');
      await killAllTrellisProcesses();
      if (progressCb) {
        const { stage, description } = mapTrellisMessageToStage('Restarting Trellis server...', 0);
        progressCb({ stage, progress: 0, message: description });
      }
      startTrellisServer();
      console.log('[Trellis Server] Waiting for server to be ready...');
      await waitForTrellisReady(null, progressCb);
      console.log('[Trellis Server] Server is ready');
    } else {
      console.log('[Trellis Server] Server is healthy, proceeding with generation');
    }
  }

  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage('Sending image to Trellis for 3D generation...', 0);
    progressCb({ stage, progress: 0, message: description });
  }
  // Verify image file exists and get stats
  if (!fs.existsSync(imagePath)) {
    throw new Error(`Image file does not exist: ${imagePath}`);
  }
  const imageStats = fs.statSync(imagePath);
  console.log('[Trellis Server] Image file stats:', {
    size: imageStats.size,
    path: imagePath,
    basename: path.basename(imagePath)
  });

  // Convert image to base64 as required by the API
  const imageBuffer = fs.readFileSync(imagePath);
  const imageBase64 = imageBuffer.toString('base64');
  console.log('[Trellis Server] Converted image to base64, length:', imageBase64.length);

  // Prepare request data as JSON (not FormData)
  const requestData = {
    image_base64: imageBase64,
    seed: 42,
    ss_guidance_strength: 7.5,
    ss_sampling_steps: 12,
    slat_guidance_strength: 3.0,
    slat_sampling_steps: 12
  };

  let response;
  try {
    console.log('[Trellis Server] Sending POST request to:', `http://${TRELLIS_HOST}:${TRELLIS_PORT}/generate_no_preview`);
    console.log('[Trellis Server] Request data keys:', Object.keys(requestData));
    console.log('[Trellis Server] Request parameters:', {
      seed: requestData.seed,
      ss_guidance_strength: requestData.ss_guidance_strength,
      ss_sampling_steps: requestData.ss_sampling_steps,
      slat_guidance_strength: requestData.slat_guidance_strength,
      slat_sampling_steps: requestData.slat_sampling_steps,
      image_base64_length: requestData.image_base64.length
    });

    response = await axios.post(
      `http://${TRELLIS_HOST}:${TRELLIS_PORT}/generate_no_preview`,
      requestData,
      {
        headers: {
          'Content-Type': 'application/json'
        },
        maxBodyLength: Infinity,
        timeout: 300000 // 5 minutes timeout for long-running generation
      }
    );
    console.log('[Trellis Server] POST response status:', response.status);
    console.log('[Trellis Server] POST response data:', response.data);
  } catch (err) {
    console.error('[Trellis Server] POST request failed:', err.response?.status, err.response?.data);
    console.error('[Trellis Server] Full error:', err.message);

    // If we get a 500 error, the server might be in a bad state - try restarting once
    if (err.response?.status === 500) {
      console.log('[Trellis Server] Server returned 500 error, attempting restart and retry...');

      // Kill all Trellis processes to ensure clean restart
      await killAllTrellisProcesses();

      if (progressCb) {
        const { stage, description } = mapTrellisMessageToStage('Server error detected, restarting...', 0);
        progressCb({ stage, progress: 0, message: description });
      }

      // Restart server
      startTrellisServer();
      await waitForTrellisReady(null, progressCb);

      // Wait a bit longer for server to be fully ready
      console.log('[Trellis Server] Waiting additional time for server to be fully ready...');
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Double-check server health before retry
      const isHealthyBeforeRetry = await isServerHealthy();
      if (!isHealthyBeforeRetry) {
        throw new Error('Server is still not healthy after restart, cannot retry');
      }
      console.log('[Trellis Server] Server health confirmed before retry');

      // Retry the request once
      try {
        console.log('[Trellis Server] Retrying POST request after server restart...');
        console.log('[Trellis Server] Retry request URL:', `http://${TRELLIS_HOST}:${TRELLIS_PORT}/generate_no_preview`);
        console.log('[Trellis Server] Retry request timeout: 300000ms');

        // Create fresh request data for retry
        const retryImageBuffer = fs.readFileSync(imagePath);
        const retryImageBase64 = retryImageBuffer.toString('base64');
        const retryRequestData = {
          image_base64: retryImageBase64,
          seed: 42,
          ss_guidance_strength: 7.5,
          ss_sampling_steps: 12,
          slat_guidance_strength: 3.0,
          slat_sampling_steps: 12
        };
        console.log('[Trellis Server] Created fresh request data for retry');

        // Final health check right before retry
        const finalHealthCheck = await isServerHealthy();
        console.log('[Trellis Server] Final health check before retry:', finalHealthCheck);
        if (!finalHealthCheck) {
          throw new Error('Server became unhealthy right before retry');
        }

        response = await Promise.race([
          axios.post(
            `http://${TRELLIS_HOST}:${TRELLIS_PORT}/generate_no_preview`,
            retryRequestData,
            {
              headers: {
                'Content-Type': 'application/json'
              },
              maxBodyLength: Infinity,
              timeout: 300000
            }
          ),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Retry request timeout after 60 seconds')), 60000)
          )
        ]);
        console.log('[Trellis Server] Retry successful - POST response status:', response.status);
      } catch (retryErr) {
        console.error('[Trellis Server] Retry also failed:', retryErr.message);
        console.error('[Trellis Server] Retry error details:', {
          status: retryErr.response?.status,
          statusText: retryErr.response?.statusText,
          data: retryErr.response?.data,
          code: retryErr.code
        });
        throw new Error('Failed to POST to Trellis after restart: ' + (retryErr.response?.data?.message || retryErr.message));
      }
    } else {
      throw new Error('Failed to POST to Trellis: ' + (err.response?.data?.message || err.message));
    }
  }

  let status = response.data.status;
  let progress = response.data.progress;
  let message = response.data.message;
  let modelUrl = response.data.model_url;

  // Emit initial progress
  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage(message, progress);
    progressCb({ stage, progress, message: description });
  }

  // If still processing, poll for progress using the status endpoint
  while (status === 'PROCESSING') {
    await new Promise(r => setTimeout(r, 2000));
    try {
      const pollResponse = await axios.get(`http://${TRELLIS_HOST}:${TRELLIS_PORT}/status`, { timeout: 30000 });
      status = pollResponse.data.status;
      progress = pollResponse.data.progress;
      message = pollResponse.data.message;
      modelUrl = pollResponse.data.model_url;
      if (progressCb) {
        const { stage, description } = mapTrellisMessageToStage(message, progress);
        progressCb({ stage, progress, message: description });
      }
    } catch (err) {
      if (progressCb) progressCb({ stage: 'trellis', progress, message: 'Error polling Trellis progress: ' + err.message });
      break;
    }
  }

  if (!modelUrl) throw new Error('No model_url in Trellis response');

  // Convert relative URL to absolute URL if needed
  const fullModelUrl = modelUrl.startsWith('http') ? modelUrl : `http://${TRELLIS_HOST}:${TRELLIS_PORT}${modelUrl}`;

  console.log('[Trellis Server] Model URL from server:', modelUrl);
  console.log('[Trellis Server] Full model URL for download:', fullModelUrl);

  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage('Downloading generated 3D model...', 100);
    progressCb({ stage, progress: 100, message: description });
  }

  // Ensure output directory exists
  if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
  }

  const modelResponse = await axios.get(fullModelUrl, { responseType: 'stream', timeout: 60000 });
  let fileName = path.basename(fullModelUrl.split('?')[0]) || 'model';

  // Ensure the file has a .glb extension
  if (!fileName.endsWith('.glb') && !fileName.endsWith('.gltf')) {
    fileName = fileName + '.glb';
  }

  const outputPath = path.join(OUTPUT_DIR, fileName);

  console.log('[Trellis Server] Downloading model to:', outputPath);
  console.log('[Trellis Server] Model response status:', modelResponse.status);
  console.log('[Trellis Server] Model response headers:', modelResponse.headers['content-type']);

  await new Promise((resolve, reject) => {
    const writer = fs.createWriteStream(outputPath);
    modelResponse.data.pipe(writer);
    writer.on('finish', () => {
      console.log('[Trellis Server] Model file saved successfully');
      resolve();
    });
    writer.on('error', (error) => {
      console.error('[Trellis Server] Error saving model file:', error);
      reject(error);
    });
  });
  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage('3D model downloaded.', 100);
    progressCb({ stage, progress: 100, message: description });
  }

  // Convert absolute path to relative path for the frontend
  const app = require('electron').app;
  const relativePath = path.relative(app.getAppPath(), outputPath);

  console.log('[Trellis Server] Output file absolute path:', outputPath);
  console.log('[Trellis Server] Output file relative path:', relativePath);
  console.log('[Trellis Server] File exists:', fs.existsSync(outputPath));
  if (fs.existsSync(outputPath)) {
    const stats = fs.statSync(outputPath);
    console.log('[Trellis Server] File size:', stats.size, 'bytes');
  }

  return relativePath;
}

module.exports = { generate3DModel }; 