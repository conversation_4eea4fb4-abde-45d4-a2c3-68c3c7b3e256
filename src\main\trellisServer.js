const { spawn } = require('child_process');
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const net = require('net');
const path = require('path');

const TRELLIS_PORT = 7960;
const TRELLIS_HOST = '127.0.0.1';
const RUN_BAT = path.join(__dirname, '../../pipelines/3DPipelines/gen3d/trellis-stable-projectorz-101/run-fp16.bat');
const OUTPUT_DIR = path.join(__dirname, '../../output');

function isTrellisRunning() {
  console.log('[Trellis Server] Checking if server is running on', TRELLIS_HOST + ':' + TRELLIS_PORT);
  return new Promise((resolve) => {
    const socket = net.createConnection(TRELLIS_PORT, TRELLIS_HOST);
    socket.on('connect', () => {
      console.log('[Trellis Server] Server is running - connection successful');
      socket.end();
      resolve(true);
    });
    socket.on('error', (error) => {
      console.log('[Trellis Server] Server not running - connection failed:', error.code);
      resolve(false);
    });
  });
}

function startTrellisServer() {
  console.log('[Trellis Server] Starting server...');
  console.log('[Trellis Server] RUN_BAT:', RUN_BAT);
  console.log('[Trellis Server] Batch file exists:', fs.existsSync(RUN_BAT));

  if (!fs.existsSync(RUN_BAT)) {
    throw new Error('Trellis run-fp16.bat not found at: ' + RUN_BAT);
  }
  const batDir = path.dirname(RUN_BAT);
  console.log('[Trellis Server] Working directory:', batDir);
  console.log('[Trellis Server] Command: cmd.exe /c', path.basename(RUN_BAT));

  // Use cmd.exe with specific flags to prevent window showing
  const child = spawn('cmd.exe', ['/c', path.basename(RUN_BAT)], {
    cwd: batDir,
    detached: false,  // Changed to false so we can capture output
    stdio: ['ignore', 'pipe', 'pipe'],
    windowsHide: true,
    shell: false,
    env: {
      ...process.env,
      // Set environment variables to minimize window visibility
      PYTHONUNBUFFERED: '1'
    }
  });
  // Capture and display all server output in main application logs
  child.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(line => line.trim());
    lines.forEach(line => {
      console.log('[Trellis Server] ' + line.trim());
      if (typeof logger !== 'undefined') logger.info('[Trellis Server] ' + line.trim());

      // Check for port binding errors
      if (line.includes('error while attempting to bind') || line.includes('Address already in use')) {
        console.error('[Trellis Server] PORT CONFLICT DETECTED - Another server is using port 7960');
        console.error('[Trellis Server] Please close any other Trellis servers and try again');
      }
    });
  });

  child.stderr.on('data', (data) => {
    const lines = data.toString().split('\n').filter(line => line.trim());
    lines.forEach(line => {
      console.error('[Trellis Server] ' + line.trim());
      if (typeof logger !== 'undefined') logger.error('[Trellis Server] ' + line.trim());

      // Check for port binding errors in stderr too
      if (line.includes('error while attempting to bind') || line.includes('Address already in use')) {
        console.error('[Trellis Server] PORT CONFLICT DETECTED - Another server is using port 7960');
        console.error('[Trellis Server] Please close any other Trellis servers and try again');
      }
    });
  });
  // Handle process events and log them to the main application logs
  child.on('close', (code) => {
    console.log(`[Trellis Server] Process exited with code ${code}`);
    if (typeof logger !== 'undefined') logger.info(`[Trellis Server] Process exited with code ${code}`);
  });

  child.on('error', (error) => {
    console.error('[Trellis Server] Process error:', error);
    if (typeof logger !== 'undefined') logger.error('[Trellis Server] Process error: ' + error.message);
  });

  // Store the child process reference globally so we can check if it's still running
  global.trellisProcess = child;
}

async function waitForTrellisReady(timeoutMs = null, progressCb) {
  const start = Date.now();
  while (true) {
    if (await isTrellisRunning()) return true;
    if (progressCb) progressCb({ stage: 'trellis', message: 'Waiting for Trellis server to start...' });
    await new Promise(r => setTimeout(r, 2000));
    // No timeout: wait forever
  }
}

function mapTrellisMessageToStage(message, progress) {
  if (!message) return { stage: 'preprocessing', description: 'Loading image and initializing pipeline' };
  const msg = message.toLowerCase();

  // Match actual server messages from the log

  // Stage 1: Initialization and Sampling (0-30%)
  if (msg.includes('sampling:') || msg.includes('sampling') || msg.includes('it/s')) {
    return { stage: 'sparse_structure', description: 'Generating 3D structure (Sampling)' };
  }

  // Stage 2: SLAT Decoding (30-40%)
  if (msg.includes('decoding the slat') || msg.includes('decoding')) {
    return { stage: 'slat_generation', description: 'Decoding the SLAT representation' };
  }

  // Stage 3: Mesh Processing (40-80%)
  if (msg.includes('decimating mesh') || msg.includes('before postprocess') || msg.includes('after decimate')) {
    return { stage: 'mesh_creation', description: 'Processing and decimating mesh' };
  }

  // Stage 4: Rasterizing (80-85%)
  if (msg.includes('rasterizing') || msg.includes('invisible faces')) {
    return { stage: 'mesh_creation', description: 'Rasterizing and removing invisible faces' };
  }

  // Stage 5: Rendering and Texture Baking (85-95%)
  if (msg.includes('rendering:') || msg.includes('texture baking') || msg.includes('optimizing')) {
    return { stage: 'glb_export', description: 'Rendering and baking textures' };
  }

  // Stage 6: Completion (95-100%)
  if (msg.includes('3d model generation completed') || msg.includes('completed in')) {
    return { stage: 'glb_export', description: '3D model generation completed' };
  }

  // Client-side stages
  if (msg.includes('downloading') || msg.includes('download')) {
    return { stage: 'download', description: 'Downloading generated 3D model' };
  }

  // Fallback: use progress ranges to determine stage
  if (progress < 30) return { stage: 'sparse_structure', description: message };
  if (progress < 40) return { stage: 'slat_generation', description: message };
  if (progress < 80) return { stage: 'mesh_creation', description: message };
  if (progress < 95) return { stage: 'glb_export', description: message };
  return { stage: 'download', description: message };
}

async function generate3DModel(imagePath, progressCb) {
  console.log('[Trellis Server] generate3DModel called with imagePath:', imagePath);

  const isRunning = await isTrellisRunning();
  console.log('[Trellis Server] isTrellisRunning() returned:', isRunning);

  if (!isRunning) {
    console.log('[Trellis Server] Server not running, starting server...');
    if (progressCb) {
      const { stage, description } = mapTrellisMessageToStage('Starting Trellis server...', 0);
      progressCb({ stage, progress: 0, message: description });
    }
    try {
      startTrellisServer();
      console.log('[Trellis Server] startTrellisServer() called successfully');
    } catch (error) {
      console.error('[Trellis Server] Error starting server:', error);
      throw error;
    }
    await waitForTrellisReady(null, progressCb);
  } else {
    console.log('[Trellis Server] Server already running, skipping startup');
  }

  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage('Sending image to Trellis for 3D generation...', 0);
    progressCb({ stage, progress: 0, message: description });
  }
  const form = new FormData();
  form.append('file', fs.createReadStream(imagePath), { filename: path.basename(imagePath) });

  let response;
  try {
    console.log('[Trellis Server] Sending POST request to:', `http://${TRELLIS_HOST}:${TRELLIS_PORT}/generate_no_preview`);
    console.log('[Trellis Server] Form data keys:', Object.keys(form._streams || {}));

    response = await axios.post(
      `http://${TRELLIS_HOST}:${TRELLIS_PORT}/generate_no_preview`,
      form,
      {
        headers: form.getHeaders(),
        maxBodyLength: Infinity,
        timeout: 300000 // 5 minutes timeout for long-running generation
      }
    );
    console.log('[Trellis Server] POST response status:', response.status);
    console.log('[Trellis Server] POST response data:', response.data);
  } catch (err) {
    console.error('[Trellis Server] POST request failed:', err.response?.status, err.response?.data);
    console.error('[Trellis Server] Full error:', err.message);
    throw new Error('Failed to POST to Trellis: ' + (err.response?.data?.message || err.message));
  }

  let status = response.data.status;
  let progress = response.data.progress;
  let message = response.data.message;
  let modelUrl = response.data.model_url;

  // Emit initial progress
  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage(message, progress);
    progressCb({ stage, progress, message: description });
  }

  // If still processing, poll for progress using the status endpoint
  while (status === 'PROCESSING') {
    await new Promise(r => setTimeout(r, 2000));
    try {
      const pollResponse = await axios.get(`http://${TRELLIS_HOST}:${TRELLIS_PORT}/status`, { timeout: 30000 });
      status = pollResponse.data.status;
      progress = pollResponse.data.progress;
      message = pollResponse.data.message;
      modelUrl = pollResponse.data.model_url;
      if (progressCb) {
        const { stage, description } = mapTrellisMessageToStage(message, progress);
        progressCb({ stage, progress, message: description });
      }
    } catch (err) {
      if (progressCb) progressCb({ stage: 'trellis', progress, message: 'Error polling Trellis progress: ' + err.message });
      break;
    }
  }

  if (!modelUrl) throw new Error('No model_url in Trellis response');

  // Convert relative URL to absolute URL if needed
  const fullModelUrl = modelUrl.startsWith('http') ? modelUrl : `http://${TRELLIS_HOST}:${TRELLIS_PORT}${modelUrl}`;

  console.log('[Trellis Server] Model URL from server:', modelUrl);
  console.log('[Trellis Server] Full model URL for download:', fullModelUrl);

  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage('Downloading generated 3D model...', 100);
    progressCb({ stage, progress: 100, message: description });
  }

  // Ensure output directory exists
  if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
  }

  const modelResponse = await axios.get(fullModelUrl, { responseType: 'stream', timeout: 60000 });
  const fileName = path.basename(fullModelUrl.split('?')[0]) || 'model.glb';
  const outputPath = path.join(OUTPUT_DIR, fileName);

  console.log('[Trellis Server] Downloading model to:', outputPath);
  console.log('[Trellis Server] Model response status:', modelResponse.status);
  console.log('[Trellis Server] Model response headers:', modelResponse.headers['content-type']);

  await new Promise((resolve, reject) => {
    const writer = fs.createWriteStream(outputPath);
    modelResponse.data.pipe(writer);
    writer.on('finish', () => {
      console.log('[Trellis Server] Model file saved successfully');
      resolve();
    });
    writer.on('error', (error) => {
      console.error('[Trellis Server] Error saving model file:', error);
      reject(error);
    });
  });
  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage('3D model downloaded.', 100);
    progressCb({ stage, progress: 100, message: description });
  }

  // Convert absolute path to relative path for the frontend
  const app = require('electron').app;
  const relativePath = path.relative(app.getAppPath(), outputPath);

  console.log('[Trellis Server] Output file absolute path:', outputPath);
  console.log('[Trellis Server] Output file relative path:', relativePath);
  console.log('[Trellis Server] File exists:', fs.existsSync(outputPath));
  if (fs.existsSync(outputPath)) {
    const stats = fs.statSync(outputPath);
    console.log('[Trellis Server] File size:', stats.size, 'bytes');
  }

  return relativePath;
}

module.exports = { generate3DModel }; 