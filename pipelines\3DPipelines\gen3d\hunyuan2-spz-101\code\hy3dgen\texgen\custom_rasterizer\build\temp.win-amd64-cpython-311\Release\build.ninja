ninja_required_version = 1.3
cxx = cl
nvcc = C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\bin\nvcc

cflags = /nologo /O2 /W3 /GL /DNDEBUG /MD /MD /wd4819 /wd4251 /wd4244 /wd4267 /wd4275 /wd4018 /wd4190 /wd4624 /wd4067 /wd4068 /EHsc -IC:\_myDrive\repos\Hunyuan3D-2-stable-projectorz\venv\Lib\site-packages\torch\include -IC:\_myDrive\repos\Hunyuan3D-2-stable-projectorz\venv\Lib\site-packages\torch\include\torch\csrc\api\include -IC:\_myDrive\repos\Hunyuan3D-2-stable-projectorz\venv\Lib\site-packages\torch\include\TH -IC:\_myDrive\repos\Hunyuan3D-2-stable-projectorz\venv\Lib\site-packages\torch\include\THC "-IC:\Program Files\Python311\Include" -IC:\_myDrive\repos\Hunyuan3D-2-stable-projectorz\venv\Lib\site-packages\torch\include -IC:\_myDrive\repos\Hunyuan3D-2-stable-projectorz\venv\Lib\site-packages\torch\include\torch\csrc\api\include -IC:\_myDrive\repos\Hunyuan3D-2-stable-projectorz\venv\Lib\site-packages\torch\include\TH -IC:\_myDrive\repos\Hunyuan3D-2-stable-projectorz\venv\Lib\site-packages\torch\include\THC "-IC:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include" -IC:\_myDrive\repos\Hunyuan3D-2-stable-projectorz\venv\include "-IC:\Program Files\Python311\include" "-IC:\Program Files\Python311\Include" "-IC:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.42.34433\include" "-IC:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.42.34433\ATLMFC\include" "-IC:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\um" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\shared" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\winrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\cppwinrt" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files\Python311\Include"
post_cflags = /bigobj /D_ALLOW_COMPILER_AND_STL_VERSION_MISMATCH -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=custom_rasterizer_kernel -D_GLIBCXX_USE_CXX11_ABI=0 /std:c++17
cuda_cflags = -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\_myDrive\repos\Hunyuan3D-2-stable-projectorz\venv\Lib\site-packages\torch\include -IC:\_myDrive\repos\Hunyuan3D-2-stable-projectorz\venv\Lib\site-packages\torch\include\torch\csrc\api\include -IC:\_myDrive\repos\Hunyuan3D-2-stable-projectorz\venv\Lib\site-packages\torch\include\TH -IC:\_myDrive\repos\Hunyuan3D-2-stable-projectorz\venv\Lib\site-packages\torch\include\THC "-IC:\Program Files\Python311\Include" -IC:\_myDrive\repos\Hunyuan3D-2-stable-projectorz\venv\Lib\site-packages\torch\include -IC:\_myDrive\repos\Hunyuan3D-2-stable-projectorz\venv\Lib\site-packages\torch\include\torch\csrc\api\include -IC:\_myDrive\repos\Hunyuan3D-2-stable-projectorz\venv\Lib\site-packages\torch\include\TH -IC:\_myDrive\repos\Hunyuan3D-2-stable-projectorz\venv\Lib\site-packages\torch\include\THC "-IC:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4\include" -IC:\_myDrive\repos\Hunyuan3D-2-stable-projectorz\venv\include "-IC:\Program Files\Python311\include" "-IC:\Program Files\Python311\Include" "-IC:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.42.34433\include" "-IC:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.42.34433\ATLMFC\include" "-IC:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\um" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\shared" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\winrt" "-IC:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\cppwinrt" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files\Python311\Include"
cuda_post_cflags = -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr --allow-unsupported-compiler --expt-relaxed-constexpr -D_ALLOW_COMPILER_AND_STL_VERSION_MISMATCH -Xcompiler /D_ALLOW_COMPILER_AND_STL_VERSION_MISMATCH -gencode=arch=compute_61,code=sm_61 -gencode=arch=compute_70,code=sm_70 -gencode=arch=compute_75,code=sm_75 -gencode=arch=compute_80,code=sm_80 -gencode=arch=compute_86,code=sm_86 -gencode=arch=compute_89,code=sm_89 -gencode=arch=compute_90,code=sm_90 -gencode=arch=compute_90,code=compute_90 -gencode=arch=compute_90,code=compute_90 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=custom_rasterizer_kernel -D_GLIBCXX_USE_CXX11_ABI=0
cuda_dlink_post_cflags = 
ldflags = 

rule compile
  command = cl /showIncludes $cflags -c $in /Fo$out $post_cflags
  deps = msvc

rule cuda_compile
  depfile = $out.d
  deps = gcc
  command = $nvcc --generate-dependencies-with-compile --dependency-output $out.d $cuda_cflags -c $in -o $out $cuda_post_cflags





build C$:\_myDrive\repos\Hunyuan3D-2-stable-projectorz\hy3dgen\texgen\custom_rasterizer\build\temp.win-amd64-cpython-311\Release\lib/custom_rasterizer_kernel/grid_neighbor.obj: compile C$:\_myDrive\repos\Hunyuan3D-2-stable-projectorz\hy3dgen\texgen\custom_rasterizer\lib\custom_rasterizer_kernel\grid_neighbor.cpp
build C$:\_myDrive\repos\Hunyuan3D-2-stable-projectorz\hy3dgen\texgen\custom_rasterizer\build\temp.win-amd64-cpython-311\Release\lib/custom_rasterizer_kernel/rasterizer.obj: compile C$:\_myDrive\repos\Hunyuan3D-2-stable-projectorz\hy3dgen\texgen\custom_rasterizer\lib\custom_rasterizer_kernel\rasterizer.cpp
build C$:\_myDrive\repos\Hunyuan3D-2-stable-projectorz\hy3dgen\texgen\custom_rasterizer\build\temp.win-amd64-cpython-311\Release\lib/custom_rasterizer_kernel/rasterizer_gpu.obj: cuda_compile C$:\_myDrive\repos\Hunyuan3D-2-stable-projectorz\hy3dgen\texgen\custom_rasterizer\lib\custom_rasterizer_kernel\rasterizer_gpu.cu






