"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.well_known_types import (
    Any as google___protobuf___internal___well_known_types___Any,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class Any(google___protobuf___message___Message, google___protobuf___internal___well_known_types___Any):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    type_url: typing___Text = ...
    value: builtin___bytes = ...

    def __init__(self,
        *,
        type_url : typing___Optional[typing___Text] = None,
        value : typing___Optional[builtin___bytes] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"type_url",b"type_url",u"value",b"value"]) -> None: ...
type___Any = Any
