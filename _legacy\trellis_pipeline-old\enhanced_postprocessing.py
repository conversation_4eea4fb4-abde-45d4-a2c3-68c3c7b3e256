"""
Enhanced Post-Processing Module for 3D Models

This module extends the standard Trellis post-processing with advanced
lighting and shadow reduction techniques.
"""

import numpy as np
import trimesh
import trimesh.visual
from PIL import Image
import cv2
from typing import Dict, Tuple, Optional, Union, Any
import os
import sys

# Add the current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from lighting_enhancement import LightingEnhancer


class EnhancedPostProcessor:
    """
    Enhanced post-processor that combines standard Trellis processing
    with advanced lighting and shadow reduction techniques.
    """
    
    def __init__(self):
        self.lighting_enhancer = LightingEnhancer()
        self.default_enhancement_settings = {
            'enable_lighting_enhancement': True,
            'ambient_boost': 0.25,
            'shadow_softening': 0.3,
            'gamma_correction': 1.15,
            'material_brightness': 0.15,
            'contrast_enhancement': 1.05,
            'saturation_boost': 1.02,
            'multi_light_setup': True,
            'ssao_reduction': True,
        }
    
    def enhanced_to_glb(
        self,
        app_rep: Any,
        mesh: Any,
        simplify: float = 0.95,
        fill_holes: bool = True,
        fill_holes_max_size: float = 0.04,
        texture_size: int = 1024,
        enhancement_settings: Optional[Dict] = None,
        debug: bool = False,
        verbose: bool = True,
    ) -> trimesh.Trimesh:
        """
        Enhanced version of to_glb with lighting improvements.
        
        This function wraps the standard Trellis to_glb function and adds
        lighting enhancements to reduce dark areas and shadows.
        """
        if enhancement_settings is None:
            enhancement_settings = self.default_enhancement_settings.copy()
        
        # First, use the standard Trellis processing
        try:
            # Try to import from the current environment first
            original_to_glb = None

            # Try direct import from trellis.utils (if we're in Trellis environment)
            try:
                from trellis.utils import postprocessing_utils
                original_to_glb = postprocessing_utils.to_glb
                if verbose:
                    print("Using Trellis postprocessing from current environment")
            except ImportError:
                # Try alternative import paths for Trellis postprocessing
                import_attempts = [
                    # Direct module import (most likely in Trellis environment)
                    ("trellis.utils.postprocessing_utils", "to_glb"),
                    # Try importing the module and function separately
                    ("trellis.utils", "postprocessing_utils"),
                ]

                for module_path, attr_name in import_attempts:
                    try:
                        if attr_name == "to_glb":
                            module = __import__(module_path, fromlist=[attr_name])
                            original_to_glb = getattr(module, attr_name)
                        else:
                            # Import postprocessing_utils module
                            module = __import__(module_path, fromlist=[attr_name])
                            postprocessing_utils = getattr(module, attr_name)
                            original_to_glb = getattr(postprocessing_utils, "to_glb")

                        if verbose:
                            print(f"Using Trellis postprocessing from {module_path}")
                        break
                    except (ImportError, AttributeError) as e:
                        if verbose and "trellis" in module_path:
                            print(f"Failed to import from {module_path}: {e}")
                        continue

            if original_to_glb is not None:
                if verbose:
                    print("Generating 3D model with standard Trellis processing...")

                # Generate the model using original Trellis processing
                processed_mesh = original_to_glb(
                    app_rep=app_rep,
                    mesh=mesh,
                    simplify=simplify,
                    fill_holes=fill_holes,
                    fill_holes_max_size=fill_holes_max_size,
                    texture_size=texture_size,
                    debug=debug,
                    verbose=verbose
                )
            else:
                raise ImportError("Could not find Trellis postprocessing module")

        except (ImportError, Exception) as e:
            # Fallback: create a basic mesh if original processing fails
            if verbose:
                print(f"Warning: Could not use original Trellis postprocessing ({e}), using fallback...")
                print(f"Error type: {type(e).__name__}")
                print(f"Available modules in sys.modules: {[m for m in sys.modules.keys() if 'trellis' in m.lower()]}")
                import traceback
                traceback.print_exc()
            processed_mesh = self._create_fallback_mesh(mesh)
        
        # Apply lighting enhancements if enabled
        if enhancement_settings.get('enable_lighting_enhancement', True):
            if verbose:
                print("Applying lighting enhancements to reduce shadows and dark areas...")
            
            processed_mesh = self._apply_comprehensive_enhancements(processed_mesh, enhancement_settings)
        
        return processed_mesh
    
    def _apply_comprehensive_enhancements(self, mesh: trimesh.Trimesh, settings: Dict) -> trimesh.Trimesh:
        """
        Apply comprehensive lighting and shadow reduction enhancements.
        """
        enhanced_mesh = mesh.copy()

        # 1. Try PyTorch3D optimization first, fall back to traditional method
        optimization_method = settings.get('optimization_method', 'pytorch3d')

        if optimization_method == 'pytorch3d':
            try:
                from pytorch3d_lighting_optimizer import optimize_model_lighting_pytorch3d

                # Use PyTorch3D optimization settings
                pytorch3d_settings = {
                    'num_lights': settings.get('num_lights', 4),
                    'optimization_steps': settings.get('optimization_steps', 100),
                    'learning_rate': settings.get('learning_rate', 0.01),
                    'ambient_strength': settings.get('ambient_strength', 0.3),
                    'diffuse_strength': settings.get('diffuse_strength', 0.7),
                    'specular_strength': settings.get('specular_strength', 0.2),
                    'light_distance': settings.get('light_distance', 3.0),
                    'target_brightness': settings.get('target_brightness', 0.6),
                    'shadow_softness': settings.get('shadow_softness', 0.1),
                    'color_preservation': settings.get('color_preservation', 0.8),
                }

                enhanced_mesh = optimize_model_lighting_pytorch3d(enhanced_mesh, pytorch3d_settings)
                print("✓ PyTorch3D lighting optimization applied successfully")

                # Still apply additional enhancements after PyTorch3D optimization
                enhanced_mesh = self._apply_additional_enhancements(enhanced_mesh, settings)
                return enhanced_mesh

            except Exception as e:
                print(f"PyTorch3D optimization failed: {e}")
                print("Falling back to traditional lighting enhancement...")

        # Traditional lighting enhancement pipeline
        # 1. Apply basic lighting enhancement
        enhanced_mesh = self.lighting_enhancer.enhance_model(enhanced_mesh, settings)

        # 2. Apply multi-light material setup
        if settings.get('multi_light_setup', True):
            enhanced_mesh = self._setup_multi_light_materials(enhanced_mesh, settings)

        # 3. Apply advanced texture processing
        enhanced_mesh = self._apply_advanced_texture_processing(enhanced_mesh, settings)

        # 4. Apply vertex-based enhancements
        enhanced_mesh = self._apply_vertex_enhancements(enhanced_mesh, settings)

        return enhanced_mesh

    def _apply_additional_enhancements(self, mesh: trimesh.Trimesh, settings: Dict) -> trimesh.Trimesh:
        """
        Apply additional enhancements after PyTorch3D optimization.
        """
        enhanced_mesh = mesh.copy()

        # Apply advanced texture processing (if not handled by PyTorch3D)
        enhanced_mesh = self._apply_advanced_texture_processing(enhanced_mesh, settings)

        # Apply vertex-based enhancements
        enhanced_mesh = self._apply_vertex_enhancements(enhanced_mesh, settings)

        return enhanced_mesh
    
    def _setup_multi_light_materials(self, mesh: trimesh.Trimesh, settings: Dict) -> trimesh.Trimesh:
        """
        Setup materials optimized for multi-light environments to reduce shadows.
        """
        if not hasattr(mesh.visual, 'material') or mesh.visual.material is None:
            # Create a new PBR material if none exists
            material = trimesh.visual.material.PBRMaterial(
                roughnessFactor=0.7,
                metallicFactor=0.1,
                baseColorFactor=np.array([200, 200, 200, 255], dtype=np.uint8)
            )
            mesh.visual.material = material
        
        material = mesh.visual.material
        
        # Optimize material for better light distribution
        if hasattr(material, 'roughnessFactor'):
            # Moderate roughness for good light scattering
            material.roughnessFactor = 0.6
        
        if hasattr(material, 'metallicFactor'):
            # Low metallic for better diffuse lighting
            material.metallicFactor = 0.05
        
        # Add emissive factor to simulate ambient lighting
        if hasattr(material, 'emissiveFactor'):
            ambient_strength = settings.get('ambient_boost', 0.25) * 0.1
            material.emissiveFactor = np.array([ambient_strength, ambient_strength, ambient_strength])
        else:
            # Add emissive factor as a new attribute if it doesn't exist
            ambient_strength = settings.get('ambient_boost', 0.25) * 0.1
            try:
                material.emissiveFactor = np.array([ambient_strength, ambient_strength, ambient_strength])
            except:
                pass  # Skip if material doesn't support emissive factor
        
        return mesh
    
    def _apply_advanced_texture_processing(self, mesh: trimesh.Trimesh, settings: Dict) -> trimesh.Trimesh:
        """
        Apply advanced texture processing to reduce dark areas.
        """
        if not hasattr(mesh.visual, 'material') or not hasattr(mesh.visual.material, 'baseColorTexture'):
            return mesh
        
        texture = mesh.visual.material.baseColorTexture
        if texture is None:
            return mesh
        
        # Convert to numpy array for processing
        if isinstance(texture, Image.Image):
            texture_array = np.array(texture)
        else:
            texture_array = texture
        
        # Apply shadow reduction techniques
        enhanced_texture = self._reduce_texture_shadows(texture_array, settings)
        
        # Apply unsharp masking for better detail visibility
        enhanced_texture = self._apply_unsharp_mask(enhanced_texture, settings)
        
        # Convert back to PIL Image
        enhanced_texture_pil = Image.fromarray(enhanced_texture.astype(np.uint8))
        mesh.visual.material.baseColorTexture = enhanced_texture_pil
        
        return mesh
    
    def _reduce_texture_shadows(self, texture: np.ndarray, settings: Dict) -> np.ndarray:
        """
        Reduce shadows in texture using advanced image processing.
        """
        # Convert to float for processing
        img = texture.astype(np.float32) / 255.0
        
        if len(img.shape) == 3 and img.shape[2] >= 3:
            # Use bilateral filter to preserve edges while smoothing shadows
            img_filtered = cv2.bilateralFilter(
                (img * 255).astype(np.uint8), 9, 75, 75
            ).astype(np.float32) / 255.0
            
            # Blend original with filtered for shadow reduction
            shadow_reduction = settings.get('shadow_softening', 0.3)
            img = img * (1 - shadow_reduction) + img_filtered * shadow_reduction
        
        return (img * 255).astype(np.float32)
    
    def _apply_unsharp_mask(self, texture: np.ndarray, settings: Dict) -> np.ndarray:
        """
        Apply unsharp masking to enhance detail visibility.
        """
        # Convert to uint8 for OpenCV processing
        img = texture.astype(np.uint8)
        
        if len(img.shape) == 3 and img.shape[2] >= 3:
            # Create Gaussian blur
            blurred = cv2.GaussianBlur(img, (0, 0), 1.0)
            
            # Create unsharp mask
            unsharp_strength = 0.5  # Moderate sharpening
            img = cv2.addWeighted(img, 1 + unsharp_strength, blurred, -unsharp_strength, 0)
        
        return img.astype(np.float32)
    
    def _apply_vertex_enhancements(self, mesh: trimesh.Trimesh, settings: Dict) -> trimesh.Trimesh:
        """
        Apply vertex-based enhancements for better lighting.
        """
        # Ensure smooth vertex normals for better lighting
        if hasattr(mesh, 'vertex_normals'):
            # Smooth normals to reduce harsh lighting transitions
            mesh.vertex_normals = self._smooth_vertex_normals(mesh.vertex_normals)
        
        # Apply vertex color enhancements if they exist
        if hasattr(mesh.visual, 'vertex_colors') and mesh.visual.vertex_colors is not None:
            mesh = self.lighting_enhancer._enhance_vertex_colors(mesh, settings)
        
        return mesh
    
    def _smooth_vertex_normals(self, normals: np.ndarray, smoothing_factor: float = 0.1) -> np.ndarray:
        """
        Smooth vertex normals to reduce harsh lighting transitions.
        """
        # Apply gentle smoothing to normals
        smoothed_normals = normals.copy()
        
        # Simple smoothing by averaging with neighbors (basic implementation)
        for i in range(len(normals)):
            # This is a simplified smoothing - in practice, you'd use mesh connectivity
            if i > 0 and i < len(normals) - 1:
                neighbor_avg = (normals[i-1] + normals[i+1]) / 2
                smoothed_normals[i] = normals[i] * (1 - smoothing_factor) + neighbor_avg * smoothing_factor
        
        # Normalize the smoothed normals
        norms = np.linalg.norm(smoothed_normals, axis=1, keepdims=True)
        norms[norms == 0] = 1  # Avoid division by zero
        smoothed_normals = smoothed_normals / norms
        
        return smoothed_normals
    
    def _create_fallback_mesh(self, mesh_data: Any) -> trimesh.Trimesh:
        """
        Create a fallback mesh if original processing fails.
        """
        # This is a basic fallback - in practice, you'd extract vertices and faces from mesh_data
        # For now, create a simple cube as fallback
        vertices = np.array([
            [-1, -1, -1], [1, -1, -1], [1, 1, -1], [-1, 1, -1],
            [-1, -1, 1], [1, -1, 1], [1, 1, 1], [-1, 1, 1]
        ])
        faces = np.array([
            [0, 1, 2], [0, 2, 3], [4, 7, 6], [4, 6, 5],
            [0, 4, 5], [0, 5, 1], [2, 6, 7], [2, 7, 3],
            [0, 3, 7], [0, 7, 4], [1, 5, 6], [1, 6, 2]
        ])
        
        return trimesh.Trimesh(vertices=vertices, faces=faces)


# Convenience function for easy integration
def enhanced_to_glb(
    app_rep: Any,
    mesh: Any,
    simplify: float = 0.95,
    fill_holes: bool = True,
    fill_holes_max_size: float = 0.04,
    texture_size: int = 1024,
    enhancement_settings: Optional[Dict] = None,
    debug: bool = False,
    verbose: bool = True,
) -> trimesh.Trimesh:
    """
    Enhanced version of to_glb with lighting improvements.
    """
    processor = EnhancedPostProcessor()
    return processor.enhanced_to_glb(
        app_rep=app_rep,
        mesh=mesh,
        simplify=simplify,
        fill_holes=fill_holes,
        fill_holes_max_size=fill_holes_max_size,
        texture_size=texture_size,
        enhancement_settings=enhancement_settings,
        debug=debug,
        verbose=verbose
    )
