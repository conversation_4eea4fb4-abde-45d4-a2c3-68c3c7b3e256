/// <reference types="vite/client" />

interface IElectronAPI {
  getAppVersion: () => Promise<string>;
  getConfig: (key: string) => Promise<any>;
  setConfig: (key: string, value: any) => Promise<{ success: boolean; error?: string }>;
  getAvailablePipelines: () => Promise<any[]>;
  runPipeline: (name: string, data: any) => Promise<any>;
  onPipelineStatus: (callback: (status: any) => void) => () => void;
  getProjects: () => Promise<any[]>;
  getProjectDetails: (projectId: string) => Promise<any>;
  updateProject: (id: string, data: any) => Promise<void>;
  deleteProject: (projectId: string) => Promise<void>;
  createProjectFromImage: (data: any) => Promise<any>;
  downloadFile: (fileUrl: string) => Promise<any>;
  loadFile: (relativePath: string) => Promise<string | null>;
  getSampleImages: () => Promise<any[]>;
  uploadSampleImage: (data: { buffer: Buffer, filename: string, category: string }) => Promise<any>;
  updateSampleImage: (filename: string, data: any) => Promise<any>;
  deleteSampleImage: (filename: string) => Promise<void>;
  getLogs: () => Promise<string>;
  clearLogs: () => Promise<void>;
  onLogUpdate: (callback: (log: string) => void) => () => void;
  getDependencyStatus: () => Promise<any[]>;
  installDependencies: (pipelineName: string, component: 'python' | 'model', name?: string) => Promise<void>;
  onInstallationProgress: (callback: (update: InstallationProgressUpdate) => void) => () => void;
  uploadFile: (file: { buffer: Buffer, filename: string }) => Promise<any>;
  fetchModel: (url: string) => Promise<any>;
  checkModelExists: (url: string) => Promise<boolean>;
  onProgressUpdate: (callback: (data: any) => void) => () => void;
  onLog: (callback: (message: string) => void) => void;
}

interface InstallationProgressUpdate {
  pipeline: string;
  component: 'python' | 'model';
  name: string;
  status: 'Running' | 'Complete' | 'Error';
  message: string;
  progress: number;
}

declare global {
  interface Window {
    electronAPI: IElectronAPI;
  }
}
