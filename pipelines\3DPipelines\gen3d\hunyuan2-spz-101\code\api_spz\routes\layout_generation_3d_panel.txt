vgroup padl=4 padr=4 spacing=8 flexibleWidth=1
    go minHeight=120 preferredHeight=120 flexibleWidth=1
       

    single_multi_img_input  code_name=(single_multi_img_input)  visible_name=(single_multi_img_input)  content_min_num_needed=(make_meshes_and_tex:1, retexture:0 )

    header visible_name=(max 6 images;   correct order:\nFRONT,   RIGHT,   BACK,   LEFT,   TOP,   BOT) color=(255,255,255,255)
    
    space minHeight=5

    go minHeight=140 preferredHeight=140 flexibleWidth=1
        hgroup spacing=8
            hgroup bg.color=(255,255,255,10) bg.ppu=10 flexibleWidth=1
                space minWidth=20  flexibleHeight=1  flexibleWidth=1
                slider  code_name=(guidance_scale)  visible_name=(guidance\nscale)  min=1  max=10  default=5  flexibleHeight=1
                space  minWidth=20  preferredWidth=30  flexibleHeight=1
                slider  code_name=(num_inference_steps)  visible_name=(think\nsteps)  min=1  max=50  default=12  flexibleHeight=1
                space  minWidth=20   flexibleHeight=1  flexibleWidth=1
            
            hgroup bg.color=(255,255,255,10) bg.ppu=10 flexibleWidth=1
                space  minWidth=20  flexibleHeight=1  flexibleWidth=1
                slider  code_name=(octree_resolution)  visible_name=(octree\nresolution)  min=128  max=512  default=256  show_n_decimals=0  flexibleHeight=1
                space  minWidth=20  preferredWidth=30  flexibleHeight=1
                slider  code_name=(num_chunks)  visible_name=(num\nchunks)  min=1  max=200  default=80  show_n_decimals=0  flexibleHeight=1
                space  minWidth=20  flexibleHeight=1  flexibleWidth=1       
    
    go minHeight=140 preferredHeight=140 flexibleWidth=1
        hgroup spacing=8 
            hgroup bg.color=(255,255,255,10) bg.ppu=10 padl=6 flexibleWidth=2 preferredWidth=100
                space  flexibleHeight=1  flexibleWidth=1
                slider code_name=(mesh_simplify)  visible_name=(mesh\nsimplify)  min=0  max=100  default=10  show_n_decimals=0  flexibleHeight=1  
                space  flexibleHeight=1   flexibleWidth=1  minWidth=10 

            vgroup  padl=10 padr=10 padt=27 padb=25 bg.color=(255,255,255,10) bg.ppu=10  flexibleWidth=1 spacing=8 preferredHeight=60
                go rect.offsetMin=(2,0)  minHeight=30  preferredHeight=30  flexibleWidth=1  preferredWidth=90
                    toggle  code_name=(apply_texture)  visible_name=(apply\ntexture)  default=false 
            
            go bg.color=(255,255,255,10) bg.ppu=10  flexibleWidth=1 preferredHeight=62  flexibleWidth=3
                int_horizontal  code_name=(seed)  visible_name=(seed)  min=-2147483647  max=2147483647  default=1234  as_seed=true  rect.anchors=(0,1,1,1)  rect.offsetMin=(0,-60)

          